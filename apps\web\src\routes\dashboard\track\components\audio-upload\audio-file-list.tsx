import React from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useUploader } from "@/components/upload/uploader-provider";
import { AudioFileItem } from "./audio-file-item";
import { Check, AlertCircle } from "lucide-react";

export const AudioFileList: React.FC = () => {
  const { fileStates, removeFile, deleteFile, cancelUpload, uploadFiles } =
    useUploader();

  if (fileStates.length === 0) return null;

  const pendingFiles = fileStates.filter((file) => file.status === "PENDING");
  const uploadingFiles = fileStates.filter(
    (file) => file.status === "UPLOADING"
  );
  const completedFiles = fileStates.filter(
    (file) => file.status === "COMPLETE"
  );
  const errorFiles = fileStates.filter((file) => file.status === "ERROR");

  const handleUploadAll = () => {
    const pendingKeys = pendingFiles.map((file) => file.key);
    if (pendingKeys.length > 0) {
      uploadFiles(pendingKeys);
    }
  };

  return (
    <div className="space-y-4">
      {/* Upload Actions */}
      {pendingFiles.length > 0 && (
        <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
          <div className="text-sm">
            <span className="font-medium">{pendingFiles.length}</span> file
            ready to upload
          </div>
          <Button onClick={handleUploadAll} size="sm">
            Upload File
          </Button>
        </div>
      )}

      {/* File List */}
      <div className="space-y-2">
        {fileStates.map((fileState) => (
          <AudioFileItem
            key={fileState.key}
            fileState={fileState}
            onRemove={() => removeFile(fileState.key)}
            onDelete={() => deleteFile(fileState.key)}
            onCancel={() => cancelUpload(fileState.key)}
          />
        ))}
      </div>

      {/* Summary */}
      {fileStates.length > 0 && (
        <div className="flex items-center gap-4 text-sm text-muted-foreground border-t pt-4">
          <span>Total: {fileStates.length} files</span>
          {completedFiles.length > 0 && (
            <Badge variant="secondary" className="text-green-600">
              <Check className="h-3 w-3 mr-1" />
              {completedFiles.length} uploaded
            </Badge>
          )}
          {errorFiles.length > 0 && (
            <Badge variant="destructive">
              <AlertCircle className="h-3 w-3 mr-1" />
              {errorFiles.length} failed
            </Badge>
          )}
        </div>
      )}
    </div>
  );
};
