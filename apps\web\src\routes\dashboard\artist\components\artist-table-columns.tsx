import type { ColumnDef, FilterFn } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { RowActions } from "@/routes/dashboard/artist/components/row-actions";
import type { ArtistTableItem } from "@/routes/dashboard/artist/components/artist-table";
import { capitalizeGenre } from "@/lib/genre";
import { PlatformBadge } from "@/components/platform-badge";

// Custom filter functions
export const multiColumnFilterFn: FilterFn<ArtistTableItem> = (
  row,
  columnId,
  filterValue
) => {
  const searchableRowContent = `${row.original.name}`.toLowerCase();
  const searchTerm = (filterValue ?? "").toLowerCase();
  return searchableRowContent.includes(searchTerm);
};

export const genreFilterFn: FilterFn<ArtistTableItem> = (
  row,
  columnId,
  filterValue: string[]
) => {
  if (!filterValue?.length) return true;
  const genre = row.getValue(columnId) as string;
  return filterValue.includes(genre);
};

export const createColumns = (
  onArtistDeleted: () => void,
  isAdmin: boolean
): ColumnDef<ArtistTableItem>[] => {
  const baseColumns: ColumnDef<ArtistTableItem>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      size: 28,
      enableSorting: false,
      enableHiding: false,
    },
    {
      header: "Artist Name",
      accessorKey: "name",
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue("name")}</div>
      ),
      size: 180,
      filterFn: multiColumnFilterFn,
      enableHiding: false,
    },
    {
      header: "Genre",
      accessorKey: "genre",
      cell: ({ row }) => (
        <div className="text-sm">
          {row.getValue("genre") ? (
            <Badge variant="secondary">
              {capitalizeGenre(row.getValue("genre"))}
            </Badge>
          ) : (
            <span className="text-muted-foreground">Not specified</span>
          )}
        </div>
      ),
      size: 120,
      filterFn: genreFilterFn,
    },
    {
      header: "Country",
      accessorKey: "country",
      cell: ({ row }) => (
        <div className="text-sm">
          {row.getValue("country") || (
            <span className="text-muted-foreground">Not specified</span>
          )}
        </div>
      ),
      size: 100,
    },
    {
      header: "Label",
      accessorKey: "label",
      cell: ({ row }) => (
        <div className="text-sm">
          {row.original.label?.name || (
            <span className="text-muted-foreground">Not specified</span>
          )}
        </div>
      ),
      size: 130,
    },
    {
      header: "Platforms",
      accessorKey: "identifiers",
      cell: ({ row }) => {
        const identifiers =
          row.original.identifiers?.filter(
            (id) => id.identifier !== "Create New"
          ) || [];
        if (identifiers.length === 0) {
          return (
            <span className="text-muted-foreground text-sm">No platforms</span>
          );
        }
        return (
          <div className="flex flex-wrap gap-1">
            {identifiers.slice(0, 3).map((identifier) => (
              <PlatformBadge
                key={identifier.service}
                service={identifier.service}
                identifier={identifier.identifier}
                width={16}
                height={16}
                className="text-xs h-7"
              />
            ))}
            {identifiers.length > 3 && (
              <Badge variant="outline" className="text-xs mt-1">
                +{identifiers.length - 3}
              </Badge>
            )}
          </div>
        );
      },
      size: 150,
      enableSorting: false,
    },
    {
      header: "Instagram",
      accessorKey: "instagram",
      cell: ({ row }) => (
        <div className="text-sm">
          {row.getValue("instagram") ? (
            <a
              href={`https://instagram.com/${row.getValue("instagram")}`}
              target="_blank"
              rel="noopener noreferrer"
              className="hover:underline"
            >
              @{row.getValue("instagram")}
            </a>
          ) : (
            <span className="text-muted-foreground">Not provided</span>
          )}
        </div>
      ),
      size: 130,
    },
    {
      header: "Created At",
      accessorKey: "createdAt",
      cell: ({ row }) => {
        const date = new Date(row.getValue("createdAt"));
        return (
          <div className="text-sm text-muted-foreground">
            {date.toLocaleDateString()}
          </div>
        );
      },
      size: 120,
    },
  ];

  baseColumns.push({
    id: "actions",
    header: () => <span className="sr-only">Actions</span>,
    cell: ({ row }) => (
      <RowActions row={row} onArtistDeleted={onArtistDeleted} />
    ),
    size: 80,
    enableHiding: false,
  });

  // Insert Created By column for admins before Actions
  if (isAdmin) {
    baseColumns.splice(baseColumns.length - 1, 0, {
      header: "Created By",
      accessorKey: "user",
      cell: ({ row }) => (
        <div className="text-sm min-w-0 truncate">
          {row.original.user?.email ?? "-"} | {row.original.user?.name ?? "-"}
        </div>
      ),
      size: 200,
    });
  }

  return baseColumns;
};
