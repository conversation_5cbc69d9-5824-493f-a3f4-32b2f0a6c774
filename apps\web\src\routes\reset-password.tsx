import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { useForm } from "@tanstack/react-form";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { CheckCircle, XCircle, Loader2 } from "lucide-react";
import { useTRPCClient } from "@/utils/trpc";
import { useMutation } from "@tanstack/react-query";

export const Route = createFileRoute("/reset-password")({
  component: ResetPasswordComponent,
  validateSearch: (search: Record<string, unknown>) => {
    return {
      token: (search.token as string) || "",
    };
  },
});

function ResetPasswordComponent() {
  const navigate = useNavigate();
  const { token } = Route.useSearch();
  const trpcClient = useTRPCClient();
  const [status, setStatus] = useState<
    "form" | "loading" | "success" | "error"
  >("form");
  const [errorMessage, setErrorMessage] = useState("");

  const resetPasswordMutation = useMutation({
    mutationFn: (data: { token: string; newPassword: string }) =>
      trpcClient.email.resetPassword.mutate(data),
    onSuccess: () => {
      setStatus("success");
      toast.success("Password reset successfully!");

      // Redirect to login after a short delay
      setTimeout(() => {
        navigate({ to: "/auth" });
      }, 2000);
    },
    onError: (error: any) => {
      setStatus("error");
      setErrorMessage(
        error.message ||
          "Failed to reset password. The link may be expired or invalid."
      );
      toast.error("Password reset failed");
    },
  });

  const form = useForm({
    defaultValues: {
      newPassword: "",
      confirmPassword: "",
    },
    onSubmit: async ({ value }) => {
      if (!token) {
        setStatus("error");
        setErrorMessage("Invalid reset link. No token provided.");
        return;
      }

      setStatus("loading");
      resetPasswordMutation.mutate({
        token,
        newPassword: value.newPassword,
      });
    },
    validators: {
      onSubmit: z
        .object({
          newPassword: z
            .string()
            .min(8, "Password must be at least 8 characters"),
          confirmPassword: z.string(),
        })
        .refine((data) => data.newPassword === data.confirmPassword, {
          message: "Passwords don't match",
          path: ["confirmPassword"],
        }),
    },
  });

  useEffect(() => {
    if (!token) {
      setStatus("error");
      setErrorMessage("Invalid reset link. No token provided.");
    }
  }, [token]);

  const handleReturnToLogin = () => {
    navigate({ to: "/auth" });
  };

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="max-w-md w-full mx-auto p-6">
          <div className="text-center space-y-6">
            <div className="flex justify-center">
              <Loader2 className="h-12 w-12 animate-spin text-primary" />
            </div>
            <h1 className="text-2xl font-bold">Resetting your password...</h1>
            <p className="text-muted-foreground">
              Please wait while we update your password.
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (status === "success") {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="max-w-md w-full mx-auto p-6">
          <div className="text-center space-y-6">
            <div className="flex justify-center">
              <CheckCircle className="h-12 w-12 text-green-500" />
            </div>
            <h1 className="text-2xl font-bold text-green-600">
              Password Reset Successfully!
            </h1>
            <p className="text-muted-foreground">
              Your password has been updated. You can now sign in with your new
              password.
            </p>
            <p className="text-sm text-muted-foreground">
              You will be redirected to the login page shortly...
            </p>
            <Button onClick={handleReturnToLogin} className="w-full">
              Go to Login
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (status === "error") {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="max-w-md w-full mx-auto p-6">
          <div className="text-center space-y-6">
            <div className="flex justify-center">
              <XCircle className="h-12 w-12 text-red-500" />
            </div>
            <h1 className="text-2xl font-bold text-red-600">
              Password Reset Failed
            </h1>
            <p className="text-muted-foreground">{errorMessage}</p>
            <div className="space-y-3">
              <Button onClick={handleReturnToLogin} className="w-full">
                Back to Login
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <Card className="mx-auto w-full mt-10 max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl">Reset Your Password</CardTitle>
          <CardDescription>Enter your new password below</CardDescription>
        </CardHeader>
        <CardContent>
          <form
            onSubmit={(e) => {
              e.preventDefault();
              e.stopPropagation();
              void form.handleSubmit();
            }}
            className="space-y-6"
          >
            <div>
              <form.Field name="newPassword">
                {(field) => (
                  <div className="space-y-2">
                    <Label htmlFor={field.name}>New Password</Label>
                    <Input
                      id={field.name}
                      name={field.name}
                      type="password"
                      placeholder="Enter your new password"
                      value={field.state.value}
                      onBlur={field.handleBlur}
                      onChange={(e) => field.handleChange(e.target.value)}
                    />
                    {field.state.meta.errors.map((error) => (
                      <p
                        key={error?.message}
                        className="text-destructive text-sm"
                      >
                        {error?.message}
                      </p>
                    ))}
                  </div>
                )}
              </form.Field>
            </div>

            <div>
              <form.Field name="confirmPassword">
                {(field) => (
                  <div className="space-y-2">
                    <Label htmlFor={field.name}>Confirm New Password</Label>
                    <Input
                      id={field.name}
                      name={field.name}
                      type="password"
                      placeholder="Confirm your new password"
                      value={field.state.value}
                      onBlur={field.handleBlur}
                      onChange={(e) => field.handleChange(e.target.value)}
                    />
                    {field.state.meta.errors.map((error) => (
                      <p
                        key={error?.message}
                        className="text-destructive text-sm"
                      >
                        {error?.message}
                      </p>
                    ))}
                  </div>
                )}
              </form.Field>
            </div>

            <form.Subscribe>
              {(state) => (
                <Button
                  type="submit"
                  className="w-full"
                  disabled={!state.canSubmit || state.isSubmitting}
                >
                  {state.isSubmitting
                    ? "Resetting Password..."
                    : "Reset Password"}
                </Button>
              )}
            </form.Subscribe>

            <div className="text-center">
              <button
                type="button"
                onClick={handleReturnToLogin}
                className="text-sm text-muted-foreground hover:text-primary underline"
              >
                Back to Login
              </button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
