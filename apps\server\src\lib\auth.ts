import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import prisma from "../../prisma";
import { admin } from "better-auth/plugins";
import { EmailService } from "./email";

export const auth = betterAuth({
  database: prismaAdapter(prisma, {
    provider: "postgresql",
  }),
  trustedOrigins: [process.env.CORS_ORIGIN || ""],
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: true,
    sendEmailVerificationOnSignUp: true,
    sendResetPassword: async ({ user, url }) => {
      const result = await EmailService.sendPasswordResetEmail({
        email: user.email,
        resetUrl: url,
        name: user.name,
      });

      if (!result.success) {
        throw new Error(result.error || "Failed to send password reset email");
      }
    },
    resetPasswordTokenExpiresIn: 3600, // 1 hour
  },
  emailVerification: {
    sendOnSignUp: true,
    autoSignInAfterVerification: true,
    sendVerificationEmail: async ({ user, url }) => {
      const result = await EmailService.sendVerificationEmail({
        email: user.email,
        url,
        name: user.name,
      });

      if (!result.success) {
        throw new Error(result.error || "Failed to send verification email");
      }
    },
  },
  user: {
    additionalFields: {
      phone: {
        type: "string",
        required: false,
      },
    },
    changeEmail: {
      enabled: true,
      sendChangeEmailVerification: async ({ user, newEmail, url }) => {
        const result = await EmailService.sendEmailChangeVerification({
          email: user.email, // Send verification to current email for security
          newEmail,
          url,
          name: user.name,
        });

        if (!result.success) {
          throw new Error(
            result.error || "Failed to send email change verification"
          );
        }
      },
    },
  },
  plugins: [admin()],
});
