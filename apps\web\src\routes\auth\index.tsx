import Loader from "@/components/loader";
import SignInForm from "@/components/sign-in-form";
import { authClient } from "@/lib/auth-client";
import { createFileRoute } from "@tanstack/react-router";
import { Navigate } from "@tanstack/react-router";

export const Route = createFileRoute("/auth/")({
  component: LoginComponent,
});

function LoginComponent() {
  const { data: session, isPending } = authClient.useSession();

  if (session) {
    return <Navigate to="/dashboard" />;
  }

  if (isPending) {
    return <Loader />;
  }

  return <SignInForm />;
}
