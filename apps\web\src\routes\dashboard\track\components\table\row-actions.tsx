import { useState } from "react";
import type { Row } from "@tanstack/react-table";
import { MoreHorizontalIcon, EditIcon, TrashIcon, EyeIcon } from "lucide-react";
import { toast } from "sonner";
import { useMutation } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";

import { useTRPCClient } from "@/utils/trpc";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import type { TrackTableItem } from "./track-table";
import { UpdateTrackDialog } from "./update-track-dialog";

interface RowActionsProps {
  row: Row<TrackTableItem>;
  onTrackDeleted: () => void;
}

export function RowActions({ row, onTrackDeleted }: RowActionsProps) {
  const navigate = useNavigate();
  const trpcClient = useTRPCClient();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showUpdateDialog, setShowUpdateDialog] = useState(false);
  const track = row.original;

  const deleteTrackMutation = useMutation({
    mutationFn: async () => {
      return trpcClient.track.delete.mutate({ id: track.id });
    },
    onSuccess: () => {
      toast.success("Track deleted successfully");
      setShowDeleteDialog(false);
      onTrackDeleted();
    },
    onError: (error: any) => {
      toast.error(`Failed to delete track: ${error.message}`);
    },
  });

  const handleView = () => {
    // Navigate to track details page when implemented
    // navigate({ to: "/dashboard/track/$id", params: { id: track.id } });
    toast.info("Track details view not implemented yet");
  };

  const handleEdit = () => {
    setShowUpdateDialog(true);
  };

  const handleDelete = () => {
    setShowDeleteDialog(true);
  };

  const confirmDelete = () => {
    deleteTrackMutation.mutate();
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
          >
            <MoreHorizontalIcon className="h-4 w-4" />
            <span className="sr-only">Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[160px]">
          <DropdownMenuItem onClick={handleView}>
            <EyeIcon className="mr-2 h-4 w-4" />
            View
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleEdit}>
            <EditIcon className="mr-2 h-4 w-4" />
            Edit
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            onClick={handleDelete}
            className="text-red-600 focus:text-red-600"
          >
            <TrashIcon className="mr-2 h-4 w-4" />
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the track
              "{track.title}" and all associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              disabled={deleteTrackMutation.isPending}
              className="bg-red-600 hover:bg-red-700"
            >
              {deleteTrackMutation.isPending ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <UpdateTrackDialog
        track={track}
        open={showUpdateDialog}
        onOpenChange={setShowUpdateDialog}
        onTrackUpdated={onTrackDeleted} // Reuse the refetch function
      />
    </>
  );
}
