import { useState, useEffect } from "react";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";
import { useMutation } from "@tanstack/react-query";

import { useTRPCClient } from "@/utils/trpc";
import { authClient } from "@/lib/auth-client";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import type { ContributorTableItem } from "@/routes/dashboard/contributor/components/contributor-table";

interface UpdateContributorDialogProps {
  children: React.ReactNode;
  contributor: ContributorTableItem;
  onContributorUpdated: () => void;
}

export function UpdateContributorDialog({
  children,
  contributor,
  onContributorUpdated,
}: UpdateContributorDialogProps) {
  const [open, setOpen] = useState(false);
  const trpcClient = useTRPCClient();
  const { data: session } = authClient.useSession();
  const isAdmin = session?.user?.role === "admin";
  const [formData, setFormData] = useState({
    name: "",
  });

  // Initialize form data when dialog opens
  useEffect(() => {
    if (open) {
      setFormData({
        name: contributor.name || "",
      });
    }
  }, [open, contributor]);

  const updateContributorMutation = useMutation({
    mutationFn: async (data: { id: string; name: string }) => {
      return trpcClient.contributor.update.mutate(data);
    },
    onSuccess: () => {
      toast.success("Contributor updated successfully");
      setOpen(false);
      onContributorUpdated();
    },
    onError: (error: any) => {
      console.error("Failed to update contributor:", error);
      toast.error(
        "Failed to update contributor: " + (error.message || "Unknown error")
      );
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast.error("Contributor name is required");
      return;
    }

    try {
      await updateContributorMutation.mutateAsync({
        id: contributor.id,
        name: formData.name.trim(),
      });
    } catch (error) {
      // Error handling is done in the mutation
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Edit Contributor: {contributor.name}</DialogTitle>
          <DialogDescription>
            Update the contributor information.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Basic Information */}
          <div className="space-y-4">
            {isAdmin && (
              <div className="space-y-2">
                <Label htmlFor="createdBy">Created By</Label>
                <Input
                  id="createdBy"
                  value={`${contributor.user.email} | ${contributor.user.name}`}
                  disabled
                />
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="name" className="block">
                Contributor Name <span className="text-red-500">*</span>
              </Label>
              <p
                className="text-muted-foreground text-xs"
                role="region"
                aria-live="polite"
              >
                This is the name that will be displayed for the contributor.
                Please make sure to use the correct name.
              </p>
              <Input
                id="name"
                placeholder="Enter contributor name"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                required
                disabled={updateContributorMutation.isPending}
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={updateContributorMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={
                updateContributorMutation.isPending || !formData.name.trim()
              }
            >
              {updateContributorMutation.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                "Update Contributor"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
