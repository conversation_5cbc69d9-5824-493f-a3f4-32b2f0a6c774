import { useEffect, useId, useMemo, useRef, useState } from "react";
import { authClient } from "@/lib/auth-client";
import {
  type ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  type PaginationState,
  type SortingState,
  useReactTable,
  type VisibilityState,
} from "@tanstack/react-table";
import {
  ChevronDownIcon,
  ChevronFirstIcon,
  ChevronLastIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ChevronUpIcon,
  CircleAlertIcon,
  CircleXIcon,
  Columns3Icon,
  FilterIcon,
  ListFilterIcon,
  PlusIcon,
  TrashIcon,
  UsersIcon,
} from "lucide-react";
import { toast } from "sonner";
import { useQuery, useMutation } from "@tanstack/react-query";

import { cn } from "@/lib/utils";
import { useTRPC, useTRPCClient } from "@/utils/trpc";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
} from "@/components/ui/pagination";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import { CreateContributorDialog } from "@/routes/dashboard/contributor/components/create-contributor-dialog";
import { createColumns } from "@/routes/dashboard/contributor/components/contributor-table-columns";

// Contributor types
export type ContributorTableItem = {
  id: string;
  name: string;
  userId: string;
  user: {
    id: string;
    name: string;
    email: string;
  };
  createdAt: Date;
  updatedAt: Date;
};

export default function ContributorTable() {
  const id = useId();
  const trpc = useTRPC();
  const trpcClient = useTRPCClient();
  const { data: session } = authClient.useSession();
  const isAdmin = session?.user?.role === "admin";
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const inputRef = useRef<HTMLInputElement>(null);

  const [sorting, setSorting] = useState<SortingState>([
    {
      id: "name",
      desc: false,
    },
  ]);

  // Search state
  const [searchQuery, setSearchQuery] = useState("");

  // Fetch contributors using tRPC
  const {
    data: contributorsResponse,
    isLoading,
    error,
    refetch,
  } = useQuery(
    trpc.contributor.getAll.queryOptions({
      page: pagination.pageIndex + 1,
      limit: pagination.pageSize,
      search: searchQuery || undefined,
    })
  );

  // Delete contributor mutation
  const deleteContributorMutation = useMutation({
    mutationFn: async (id: string) => {
      return trpcClient.contributor.delete.mutate({ id });
    },
    onSuccess: () => {
      toast.success("Contributor deleted successfully");
      refetch();
    },
    onError: (error: any) => {
      toast.error(`Failed to delete contributor: ${error.message}`);
    },
  });

  // Transform data for table
  const data = useMemo(() => {
    const contributors = contributorsResponse?.contributors || [];
    return contributors.map((contributor: any) => ({
      ...contributor,
      createdAt: new Date(contributor.createdAt),
      updatedAt: new Date(contributor.updatedAt),
    })) as ContributorTableItem[];
  }, [contributorsResponse?.contributors]);

  const handleDeleteRows = async () => {
    const selectedRows = table.getSelectedRowModel().rows;
    const contributorIds = selectedRows.map((row) => row.original.id);

    try {
      const deletePromises = contributorIds.map((contributorId) =>
        deleteContributorMutation.mutateAsync(contributorId)
      );

      await Promise.all(deletePromises);

      table.resetRowSelection();
    } catch (error) {
      // Error handling is done in the mutation
    }
  };

  // Create columns with refetch callback
  const columns = useMemo(
    () => createColumns(refetch, isAdmin),
    [refetch, isAdmin]
  );

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    enableSortingRemoval: false,
    getPaginationRowModel: getPaginationRowModel(),
    onPaginationChange: setPagination,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getFilteredRowModel: getFilteredRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    state: {
      sorting,
      pagination,
      columnFilters,
      columnVisibility,
    },
  });

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex flex-wrap items-center justify-between gap-3">
        <div className="flex items-center gap-3">
          {/* Filter by name */}
          <div className="relative">
            <Input
              id={`${id}-input`}
              ref={inputRef}
              className={cn(
                "peer min-w-60 max-sm:min-w-48 ps-9 h-9",
                Boolean(searchQuery) && "pe-9"
              )}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search by contributor name..."
              type="text"
              aria-label="Search by contributor name"
            />
            <div className="text-muted-foreground/80 pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 peer-disabled:opacity-50">
              <ListFilterIcon size={16} aria-hidden="true" />
            </div>
            {Boolean(searchQuery) && (
              <button
                className="text-muted-foreground/80 hover:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 absolute inset-y-0 end-0 flex h-full w-9 items-center justify-center rounded-e-md transition-[color,box-shadow] outline-none focus:z-10 focus-visible:ring-[3px] disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50"
                aria-label="Clear search"
                onClick={() => {
                  setSearchQuery("");
                  if (inputRef.current) {
                    inputRef.current.focus();
                  }
                }}
                disabled={isLoading}
              >
                <CircleXIcon size={16} aria-hidden="true" />
              </button>
            )}
          </div>

          {/* Toggle columns visibility */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="hidden sm:flex">
                <Columns3Icon
                  className="-ms-1 opacity-60"
                  size={16}
                  aria-hidden="true"
                />
                View
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Toggle columns</DropdownMenuLabel>
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  const headerText =
                    typeof column.columnDef.header === "string"
                      ? column.columnDef.header
                      : column.id;
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                      onSelect={(event) => event.preventDefault()}
                    >
                      {headerText}
                    </DropdownMenuCheckboxItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="flex items-center gap-3">
          {/* Delete button */}
          {table.getSelectedRowModel().rows.length > 0 && (
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="outline">
                  <TrashIcon
                    className="-ms-1 opacity-60"
                    size={16}
                    aria-hidden="true"
                  />
                  Delete
                  <span className="bg-background text-muted-foreground/70 -me-1 inline-flex h-5 max-h-full items-center rounded border px-1 font-[inherit] text-[0.625rem] font-medium">
                    {table.getSelectedRowModel().rows.length}
                  </span>
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <div className="flex flex-col gap-2 max-sm:items-center sm:flex-row sm:gap-4">
                  <div
                    className="flex size-9 shrink-0 items-center justify-center rounded-full border"
                    aria-hidden="true"
                  >
                    <CircleAlertIcon className="opacity-80" size={16} />
                  </div>
                  <AlertDialogHeader>
                    <AlertDialogTitle>
                      Are you absolutely sure?
                    </AlertDialogTitle>
                    <AlertDialogDescription>
                      This action cannot be undone. This will permanently delete{" "}
                      {table.getSelectedRowModel().rows.length} selected{" "}
                      {table.getSelectedRowModel().rows.length === 1
                        ? "contributor"
                        : "contributors"}
                      .
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                </div>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={handleDeleteRows}>
                    Delete
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          )}

          {/* Add contributor button */}
          <CreateContributorDialog onContributorCreated={refetch}>
            <Button variant="outline">
              <PlusIcon
                className="-ms-1 opacity-60"
                size={16}
                aria-hidden="true"
              />
              Add Contributor
            </Button>
          </CreateContributorDialog>
        </div>
      </div>

      {/* Table */}
      <div className="bg-background rounded-md border">
        <Table className="table-fixed">
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} className="hover:bg-transparent">
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead
                      key={header.id}
                      style={{ width: `${header.getSize()}px` }}
                      className="h-11"
                    >
                      {header.isPlaceholder ? null : header.column.getCanSort() ? (
                        <div
                          className={cn(
                            header.column.getCanSort() &&
                              "flex h-full cursor-pointer items-center justify-between gap-2 select-none"
                          )}
                          onClick={header.column.getToggleSortingHandler()}
                          onKeyDown={(e) => {
                            if (
                              header.column.getCanSort() &&
                              (e.key === "Enter" || e.key === " ")
                            ) {
                              e.preventDefault();
                              header.column.getToggleSortingHandler()?.(e);
                            }
                          }}
                          tabIndex={header.column.getCanSort() ? 0 : undefined}
                        >
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                          {{
                            asc: (
                              <ChevronUpIcon
                                className="shrink-0 opacity-60"
                                size={16}
                                aria-hidden="true"
                              />
                            ),
                            desc: (
                              <ChevronDownIcon
                                className="shrink-0 opacity-60"
                                size={16}
                                aria-hidden="true"
                              />
                            ),
                          }[header.column.getIsSorted() as string] ?? null}
                        </div>
                      ) : (
                        flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )
                      )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  <div className="flex items-center justify-center gap-2">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                    Loading contributors...
                  </div>
                </TableCell>
              </TableRow>
            ) : error ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  <div className="flex flex-col items-center gap-2">
                    <CircleAlertIcon className="h-8 w-8 text-red-500" />
                    <div>
                      <p className="font-medium">Failed to load contributors</p>
                      <p className="text-sm text-muted-foreground">
                        {error.message || "An unknown error occurred"}
                      </p>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => refetch()}
                        className="mt-2"
                      >
                        Try again
                      </Button>
                    </div>
                  </div>
                </TableCell>
              </TableRow>
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className="last:py-0">
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  <div className="flex flex-col items-center gap-2 p-6">
                    <UsersIcon className="h-8 w-8 text-muted-foreground" />
                    <div>
                      <p className="font-medium">No contributors found</p>
                      <p className="text-sm text-muted-foreground">
                        {searchQuery
                          ? "Try adjusting your search"
                          : "No contributors have been created yet"}
                      </p>
                    </div>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between gap-4">
        {/* Results per page */}
        <div className="flex items-center gap-2 sm:gap-3">
          <Label htmlFor={id} className="text-sm whitespace-nowrap">
            Rows per page
          </Label>
          <Select
            value={pagination.pageSize.toString()}
            onValueChange={(value) => {
              setPagination((prev) => ({
                ...prev,
                pageSize: Number(value),
                pageIndex: 0,
              }));
            }}
            disabled={isLoading}
          >
            <SelectTrigger
              id={id}
              className="w-fit whitespace-nowrap min-w-[4rem]"
            >
              <SelectValue placeholder="Select number of results" />
            </SelectTrigger>
            <SelectContent className="[&_*[role=option]]:ps-2 [&_*[role=option]]:pe-8 [&_*[role=option]>span]:start-auto [&_*[role=option]>span]:end-2">
              {[5, 10, 25, 50].map((pageSize) => (
                <SelectItem key={pageSize} value={pageSize.toString()}>
                  {pageSize}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Pagination controls */}
        <div className="flex items-center gap-4 sm:gap-8">
          {/* Page number information */}
          <div className="text-muted-foreground text-sm whitespace-nowrap">
            <p
              className="text-muted-foreground text-sm whitespace-nowrap"
              aria-live="polite"
            >
              {isLoading ? (
                "Loading..."
              ) : (
                <>
                  <span className="text-foreground">
                    {pagination.pageIndex * pagination.pageSize + 1}-
                    {Math.min(
                      (pagination.pageIndex + 1) * pagination.pageSize,
                      contributorsResponse?.pagination.total || 0
                    )}
                  </span>{" "}
                  of{" "}
                  <span className="text-foreground">
                    {contributorsResponse?.pagination.total || 0}
                  </span>
                </>
              )}
            </p>
          </div>

          {/* Pagination buttons */}
          <div>
            <Pagination>
              <PaginationContent className="gap-1">
                <PaginationItem>
                  <Button
                    size="icon"
                    variant="outline"
                    className="disabled:pointer-events-none disabled:opacity-50 h-8 w-8 sm:h-9 sm:w-9"
                    onClick={() =>
                      setPagination((prev) => ({ ...prev, pageIndex: 0 }))
                    }
                    disabled={pagination.pageIndex === 0 || isLoading}
                    aria-label="Go to first page"
                  >
                    <ChevronFirstIcon
                      size={14}
                      className="sm:size-4"
                      aria-hidden="true"
                    />
                  </Button>
                </PaginationItem>
                <PaginationItem>
                  <Button
                    size="icon"
                    variant="outline"
                    className="disabled:pointer-events-none disabled:opacity-50 h-8 w-8 sm:h-9 sm:w-9"
                    onClick={() =>
                      setPagination((prev) => ({
                        ...prev,
                        pageIndex: prev.pageIndex - 1,
                      }))
                    }
                    disabled={pagination.pageIndex === 0 || isLoading}
                    aria-label="Go to previous page"
                  >
                    <ChevronLeftIcon
                      size={14}
                      className="sm:size-4"
                      aria-hidden="true"
                    />
                  </Button>
                </PaginationItem>
                <PaginationItem>
                  <Button
                    size="icon"
                    variant="outline"
                    className="disabled:pointer-events-none disabled:opacity-50 h-8 w-8 sm:h-9 sm:w-9"
                    onClick={() =>
                      setPagination((prev) => ({
                        ...prev,
                        pageIndex: prev.pageIndex + 1,
                      }))
                    }
                    disabled={
                      isLoading ||
                      !contributorsResponse?.pagination ||
                      pagination.pageIndex >=
                        contributorsResponse.pagination.totalPages - 1
                    }
                    aria-label="Go to next page"
                  >
                    <ChevronRightIcon
                      size={14}
                      className="sm:size-4"
                      aria-hidden="true"
                    />
                  </Button>
                </PaginationItem>
                <PaginationItem>
                  <Button
                    size="icon"
                    variant="outline"
                    className="disabled:pointer-events-none disabled:opacity-50 h-8 w-8 sm:h-9 sm:w-9"
                    onClick={() => {
                      const totalPages =
                        contributorsResponse?.pagination.totalPages || 1;
                      setPagination((prev) => ({
                        ...prev,
                        pageIndex: totalPages - 1,
                      }));
                    }}
                    disabled={
                      isLoading ||
                      !contributorsResponse?.pagination ||
                      pagination.pageIndex >=
                        contributorsResponse.pagination.totalPages - 1
                    }
                    aria-label="Go to last page"
                  >
                    <ChevronLastIcon
                      size={14}
                      className="sm:size-4"
                      aria-hidden="true"
                    />
                  </Button>
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        </div>
      </div>
    </div>
  );
}
