
/* !!! This is code generated by <PERSON>rism<PERSON>. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
* This file exports all enum related types from the schema.
*
* 🟢 You can import this file directly.
*/
export const Service = {
  YOUTUBE: 'YOUTUBE',
  SPOTIFY: 'SPOTIFY',
  APPLE_MUSIC: 'APPLE_MUSIC',
  SOUNDCLOUD: 'SOUNDCLOUD',
  AUDIOMACK: 'AUDIOMACK'
} as const

export type Service = (typeof Service)[keyof typeof Service]


export const ReleaseFormat = {
  SINGLE: 'SINGLE',
  EP: 'EP',
  ALBUM: 'ALBUM'
} as const

export type ReleaseFormat = (typeof ReleaseFormat)[keyof typeof ReleaseFormat]


export const ExplicitContent = {
  EXPLICIT: 'EXPLICIT',
  NOT_EXPLICIT: 'NOT_EXPLICIT',
  CLEAN: 'CLEAN'
} as const

export type ExplicitContent = (typeof ExplicitContent)[keyof typeof ExplicitContent]


export const ArtistRole = {
  PRIMARY: 'PRIMARY',
  FEATURING: 'FEATURING'
} as const

export type ArtistRole = (typeof ArtistRole)[keyof typeof ArtistRole]


export const ReleaseStatus = {
  DRAFT: 'DRAFT',
  PENDING: 'PENDING',
  DELIVERED: 'DELIVERED',
  REJECTED: 'REJECTED'
} as const

export type ReleaseStatus = (typeof ReleaseStatus)[keyof typeof ReleaseStatus]


export const RightsClaim = {
  NO_CLAIM: 'NO_CLAIM',
  REPORT: 'REPORT',
  MONETIZE: 'MONETIZE',
  BLOCK: 'BLOCK'
} as const

export type RightsClaim = (typeof RightsClaim)[keyof typeof RightsClaim]


export const TrackStatus = {
  DRAFT: 'DRAFT',
  READY: 'READY'
} as const

export type TrackStatus = (typeof TrackStatus)[keyof typeof TrackStatus]
