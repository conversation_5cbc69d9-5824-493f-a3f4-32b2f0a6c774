import{r as B}from"./main-B9Fv5CdX.js";var ue=Object.defineProperty,ce=Object.defineProperties,le=Object.getOwnPropertyDescriptors,J=Object.getOwnPropertySymbols,fe=Object.prototype.hasOwnProperty,de=Object.prototype.propertyIsEnumerable,V=(e,t,r)=>t in e?ue(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,b=(e,t)=>{for(var r in t||(t={}))fe.call(t,r)&&V(e,r,t[r]);if(J)for(var r of J(t))de.call(t,r)&&V(e,r,t[r]);return e},T=(e,t)=>ce(e,le(t)),he=class extends Error{constructor(e,t,r){super(t||e.toString(),{cause:r}),this.status=e,this.statusText=t,this.error=r}},ye=async(e,t)=>{var r,n,s,u,a,i;let c=t||{};const o={onRequest:[t==null?void 0:t.onRequest],onResponse:[t==null?void 0:t.onResponse],onSuccess:[t==null?void 0:t.onSuccess],onError:[t==null?void 0:t.onError],onRetry:[t==null?void 0:t.onRetry]};if(!t||!(t!=null&&t.plugins))return{url:e,options:c,hooks:o};for(const l of(t==null?void 0:t.plugins)||[]){if(l.init){const f=await((r=l.init)==null?void 0:r.call(l,e.toString(),t));c=f.options||c,e=f.url}o.onRequest.push((n=l.hooks)==null?void 0:n.onRequest),o.onResponse.push((s=l.hooks)==null?void 0:s.onResponse),o.onSuccess.push((u=l.hooks)==null?void 0:u.onSuccess),o.onError.push((a=l.hooks)==null?void 0:a.onError),o.onRetry.push((i=l.hooks)==null?void 0:i.onRetry)}return{url:e,options:c,hooks:o}},G=class{constructor(e){this.options=e}shouldAttemptRetry(e,t){return this.options.shouldRetry?Promise.resolve(e<this.options.attempts&&this.options.shouldRetry(t)):Promise.resolve(e<this.options.attempts)}getDelay(){return this.options.delay}},me=class{constructor(e){this.options=e}shouldAttemptRetry(e,t){return this.options.shouldRetry?Promise.resolve(e<this.options.attempts&&this.options.shouldRetry(t)):Promise.resolve(e<this.options.attempts)}getDelay(e){return Math.min(this.options.maxDelay,this.options.baseDelay*2**e)}};function pe(e){if(typeof e=="number")return new G({type:"linear",attempts:e,delay:1e3});switch(e.type){case"linear":return new G(e);case"exponential":return new me(e);default:throw new Error("Invalid retry strategy")}}var ge=async e=>{const t={},r=async n=>typeof n=="function"?await n():n;if(e!=null&&e.auth){if(e.auth.type==="Bearer"){const n=await r(e.auth.token);if(!n)return t;t.authorization=`Bearer ${n}`}else if(e.auth.type==="Basic"){const n=r(e.auth.username),s=r(e.auth.password);if(!n||!s)return t;t.authorization=`Basic ${btoa(`${n}:${s}`)}`}else if(e.auth.type==="Custom"){const n=r(e.auth.value);if(!n)return t;t.authorization=`${r(e.auth.prefix)} ${n}`}}return t},ve=/^application\/(?:[\w!#$%&*.^`~-]*\+)?json(;.+)?$/i;function we(e){const t=e.headers.get("content-type"),r=new Set(["image/svg","application/xml","application/xhtml","application/html"]);if(!t)return"json";const n=t.split(";").shift()||"";return ve.test(n)?"json":r.has(n)||n.startsWith("text/")?"text":"blob"}function _e(e){try{return JSON.parse(e),!0}catch{return!1}}function Y(e){if(e===void 0)return!1;const t=typeof e;return t==="string"||t==="number"||t==="boolean"||t===null?!0:t!=="object"?!1:Array.isArray(e)?!0:e.buffer?!1:e.constructor&&e.constructor.name==="Object"||typeof e.toJSON=="function"}function z(e){try{return JSON.parse(e)}catch{return e}}function Q(e){return typeof e=="function"}function be(e){if(e!=null&&e.customFetchImpl)return e.customFetchImpl;if(typeof globalThis<"u"&&Q(globalThis.fetch))return globalThis.fetch;if(typeof window<"u"&&Q(window.fetch))return window.fetch;throw new Error("No fetch implementation found")}async function Re(e){const t=new Headers(e==null?void 0:e.headers),r=await ge(e);for(const[n,s]of Object.entries(r||{}))t.set(n,s);if(!t.has("content-type")){const n=Te(e==null?void 0:e.body);n&&t.set("content-type",n)}return t}function Te(e){return Y(e)?"application/json":null}function Se(e){if(!(e!=null&&e.body))return null;const t=new Headers(e==null?void 0:e.headers);if(Y(e.body)&&!t.has("content-type")){for(const[r,n]of Object.entries(e==null?void 0:e.body))n instanceof Date&&(e.body[r]=n.toISOString());return JSON.stringify(e.body)}return e.body}function Pe(e,t){var r;if(t!=null&&t.method)return t.method.toUpperCase();if(e.startsWith("@")){const n=(r=e.split("@")[1])==null?void 0:r.split("/")[0];return K.includes(n)?n.toUpperCase():t!=null&&t.body?"POST":"GET"}return t!=null&&t.body?"POST":"GET"}function Oe(e,t){let r;return!(e!=null&&e.signal)&&(e!=null&&e.timeout)&&(r=setTimeout(()=>t==null?void 0:t.abort(),e==null?void 0:e.timeout)),{abortTimeout:r,clearTimeout:()=>{r&&clearTimeout(r)}}}var Ee=class Z extends Error{constructor(t,r){super(r||JSON.stringify(t,null,2)),this.issues=t,Object.setPrototypeOf(this,Z.prototype)}};async function k(e,t){let r=await e["~standard"].validate(t);if(r.issues)throw new Ee(r.issues);return r.value}var K=["get","post","put","patch","delete"],Ae=e=>({id:"apply-schema",name:"Apply Schema",version:"1.0.0",async init(t,r){var n,s,u,a;const i=((s=(n=e.plugins)==null?void 0:n.find(c=>{var o;return(o=c.schema)!=null&&o.config?t.startsWith(c.schema.config.baseURL||"")||t.startsWith(c.schema.config.prefix||""):!1}))==null?void 0:s.schema)||e.schema;if(i){let c=t;(u=i.config)!=null&&u.prefix&&c.startsWith(i.config.prefix)&&(c=c.replace(i.config.prefix,""),i.config.baseURL&&(t=t.replace(i.config.prefix,i.config.baseURL))),(a=i.config)!=null&&a.baseURL&&c.startsWith(i.config.baseURL)&&(c=c.replace(i.config.baseURL,""));const o=i.schema[c];if(o){let l=T(b({},r),{method:o.method,output:o.output});return r!=null&&r.disableValidation||(l=T(b({},l),{body:o.input?await k(o.input,r==null?void 0:r.body):r==null?void 0:r.body,params:o.params?await k(o.params,r==null?void 0:r.params):r==null?void 0:r.params,query:o.query?await k(o.query,r==null?void 0:r.query):r==null?void 0:r.query})),{url:t,options:l}}}return{url:t,options:r}}}),Ue=e=>{async function t(r,n){const s=T(b(b({},e),n),{plugins:[...(e==null?void 0:e.plugins)||[],Ae(e||{})]});if(e!=null&&e.catchAllError)try{return await W(r,s)}catch(u){return{data:null,error:{status:500,statusText:"Fetch Error",message:"Fetch related error. Captured by catchAllError option. See error property for more details.",error:u}}}return await W(r,s)}return t};function Ie(e,t){let{baseURL:r,params:n,query:s}=t||{query:{},params:{},baseURL:""},u=e.startsWith("http")?e.split("/").slice(0,3).join("/"):r||"";if(e.startsWith("@")){const f=e.toString().split("@")[1].split("/")[0];K.includes(f)&&(e=e.replace(`@${f}/`,"/"))}u.endsWith("/")||(u+="/");let[a,i]=e.replace(u,"").split("?");const c=new URLSearchParams(i);for(const[f,h]of Object.entries(s||{}))h!=null&&c.set(f,String(h));if(n)if(Array.isArray(n)){const f=a.split("/").filter(h=>h.startsWith(":"));for(const[h,v]of f.entries()){const S=n[h];a=a.replace(v,S)}}else for(const[f,h]of Object.entries(n))a=a.replace(`:${f}`,String(h));a=a.split("/").map(encodeURIComponent).join("/"),a.startsWith("/")&&(a=a.slice(1));let o=c.toString();return o=o.length>0?`?${o}`.replace(/\+/g,"%20"):"",u.startsWith("http")?new URL(`${a}${o}`,u):`${u}${a}${o}`}var W=async(e,t)=>{var r,n,s,u,a,i,c,o;const{hooks:l,url:f,options:h}=await ye(e,t),v=be(h),S=new AbortController,I=(r=h.signal)!=null?r:S.signal,p=Ie(f,h),U=Se(h),d=await Re(h),R=Pe(f,h);let m=T(b({},h),{url:p,headers:d,body:U,method:R,signal:I});for(const w of l.onRequest)if(w){const g=await w(m);g instanceof Object&&(m=g)}("pipeTo"in m&&typeof m.pipeTo=="function"||typeof((n=t==null?void 0:t.body)==null?void 0:n.pipe)=="function")&&("duplex"in m||(m.duplex="half"));const{clearTimeout:D}=Oe(h,S);let y=await v(m.url,m);D();const x={response:y,request:m};for(const w of l.onResponse)if(w){const g=await w(T(b({},x),{response:(s=t==null?void 0:t.hookOptions)!=null&&s.cloneResponse?y.clone():y}));g instanceof Response?y=g:g instanceof Object&&(y=g.response)}if(y.ok){if(!(m.method!=="HEAD"))return{data:"",error:null};const g=we(y),P={data:"",response:y,request:m};if(g==="json"||g==="text"){const O=await y.text(),ie=await((u=m.jsonParser)!=null?u:z)(O);P.data=ie}else P.data=await y[g]();m!=null&&m.output&&m.output&&!m.disableValidation&&(P.data=await k(m.output,P.data));for(const O of l.onSuccess)O&&await O(T(b({},P),{response:(a=t==null?void 0:t.hookOptions)!=null&&a.cloneResponse?y.clone():y}));return t!=null&&t.throw?P.data:{data:P.data,error:null}}const oe=(i=t==null?void 0:t.jsonParser)!=null?i:z,L=await y.text(),H=_e(L),F=H?await oe(L):null,ae={response:y,responseText:L,request:m,error:T(b({},F),{status:y.status,statusText:y.statusText})};for(const w of l.onError)w&&await w(T(b({},ae),{response:(c=t==null?void 0:t.hookOptions)!=null&&c.cloneResponse?y.clone():y}));if(t!=null&&t.retry){const w=pe(t.retry),g=(o=t.retryAttempt)!=null?o:0;if(await w.shouldAttemptRetry(g,y)){for(const O of l.onRetry)O&&await O(x);const P=w.getDelay(g);return await new Promise(O=>setTimeout(O,P)),await W(e,T(b({},t),{retryAttempt:g+1}))}}if(t!=null&&t.throw)throw new he(y.status,y.statusText,H?F:L);return{data:null,error:T(b({},F),{status:y.status,statusText:y.statusText})}},Ne={},xe={};const $=Object.create(null),N=e=>{var t;return Ne||((t=globalThis.Deno)==null?void 0:t.env.toObject())||globalThis.__env__||(e?$:globalThis)},A=new Proxy($,{get(e,t){return N()[t]??$[t]},has(e,t){const r=N();return t in r||t in $},set(e,t,r){const n=N(!0);return n[t]=r,!0},deleteProperty(e,t){if(!t)return!1;const r=N(!0);return delete r[t],!0},ownKeys(){const e=N(!0);return Object.keys(e)}});function Le(e){return e?e!=="false":!1}const je=typeof process<"u"&&xe&&"production"||"";je==="test"||Le(A.TEST);class ee extends Error{constructor(t,r){super(t),this.name="BetterAuthError",this.message=t,this.cause=r,this.stack=""}}function qe(e){try{return new URL(e).pathname!=="/"}catch{throw new ee(`Invalid base URL: ${e}. Please provide a valid base URL.`)}}function M(e,t="/api/auth"){return qe(e)?e:(t=t.startsWith("/")?t:`/${t}`,`${e.replace(/\/+$/,"")}${t}`)}function Ce(e,t,r){if(e)return M(e,t);const n=A.BETTER_AUTH_URL||A.NEXT_PUBLIC_BETTER_AUTH_URL||A.PUBLIC_BETTER_AUTH_URL||A.NUXT_PUBLIC_BETTER_AUTH_URL||A.NUXT_PUBLIC_AUTH_URL||(A.BASE_URL!=="/"?A.BASE_URL:void 0);if(n)return M(n,t);if(typeof window<"u"&&window.location)return M(window.location.origin,t)}let _=[],E=0;const j=4;let te=e=>{let t=[],r={get(){return r.lc||r.listen(()=>{})(),r.value},lc:0,listen(n){return r.lc=t.push(n),()=>{for(let u=E+j;u<_.length;)_[u]===n?_.splice(u,j):u+=j;let s=t.indexOf(n);~s&&(t.splice(s,1),--r.lc||r.off())}},notify(n,s){let u=!_.length;for(let a of t)_.push(a,r.value,n,s);if(u){for(E=0;E<_.length;E+=j)_[E](_[E+1],_[E+2],_[E+3]);_.length=0}},off(){},set(n){let s=r.value;s!==n&&(r.value=n,r.notify(s))},subscribe(n){let s=r.listen(n);return n(r.value),s},value:e};return r};const ke=5,q=6,C=10;let $e=(e,t,r,n)=>(e.events=e.events||{},e.events[r+C]||(e.events[r+C]=n(s=>{e.events[r].reduceRight((u,a)=>(a(u),u),{shared:{},...s})})),e.events[r]=e.events[r]||[],e.events[r].push(t),()=>{let s=e.events[r],u=s.indexOf(t);s.splice(u,1),s.length||(delete e.events[r],e.events[r+C](),delete e.events[r+C])}),De=1e3,Fe=(e,t)=>$e(e,n=>{let s=t(n);s&&e.events[q].push(s)},ke,n=>{let s=e.listen;e.listen=(...a)=>(!e.lc&&!e.active&&(e.active=!0,n()),s(...a));let u=e.off;return e.events[q]=[],e.off=()=>{u(),setTimeout(()=>{if(e.active&&!e.lc){e.active=!1;for(let a of e.events[q])a();e.events[q]=[]}},De)},()=>{e.listen=s,e.off=u}});function Be(e,t,r){let n=new Set(t).add(void 0);return e.listen((s,u,a)=>{n.has(a)&&r(s,u,a)})}const Me=typeof window>"u",We=(e,t,r,n)=>{const s=te({data:null,error:null,isPending:!0,isRefetching:!1,refetch:()=>u()}),u=()=>{const i=typeof n=="function"?n({data:s.get().data,error:s.get().error,isPending:s.get().isPending}):n;return r(t,{...i,async onSuccess(c){var o;s.set({data:c.data,error:null,isPending:!1,isRefetching:!1,refetch:s.value.refetch}),await((o=i==null?void 0:i.onSuccess)==null?void 0:o.call(i,c))},async onError(c){var h,v;const{request:o}=c,l=typeof o.retry=="number"?o.retry:(h=o.retry)==null?void 0:h.attempts,f=o.retryAttempt||0;l&&f<l||(s.set({error:c.error,data:null,isPending:!1,isRefetching:!1,refetch:s.value.refetch}),await((v=i==null?void 0:i.onError)==null?void 0:v.call(i,c)))},async onRequest(c){var l;const o=s.get();s.set({isPending:o.data===null,data:o.data,error:null,isRefetching:!0,refetch:s.value.refetch}),await((l=i==null?void 0:i.onRequest)==null?void 0:l.call(i,c))}})};e=Array.isArray(e)?e:[e];let a=!1;for(const i of e)i.subscribe(()=>{Me||(a?u():Fe(s,()=>(setTimeout(()=>{u()},0),a=!0,()=>{s.off(),i.off()})))});return s},He={proto:/"(?:_|\\u0{2}5[Ff]){2}(?:p|\\u0{2}70)(?:r|\\u0{2}72)(?:o|\\u0{2}6[Ff])(?:t|\\u0{2}74)(?:o|\\u0{2}6[Ff])(?:_|\\u0{2}5[Ff]){2}"\s*:/,constructor:/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/,protoShort:/"__proto__"\s*:/,constructorShort:/"constructor"\s*:/},Je=/^\s*["[{]|^\s*-?\d{1,16}(\.\d{1,17})?([Ee][+-]?\d+)?\s*$/,X={true:!0,false:!1,null:null,undefined:void 0,nan:Number.NaN,infinity:Number.POSITIVE_INFINITY,"-infinity":Number.NEGATIVE_INFINITY},Ve=/^(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})(?:\.(\d{1,7}))?(?:Z|([+-])(\d{2}):(\d{2}))$/;function Ge(e){return e instanceof Date&&!isNaN(e.getTime())}function ze(e){const t=Ve.exec(e);if(!t)return null;const[,r,n,s,u,a,i,c,o,l,f]=t;let h=new Date(Date.UTC(parseInt(r,10),parseInt(n,10)-1,parseInt(s,10),parseInt(u,10),parseInt(a,10),parseInt(i,10),c?parseInt(c.padEnd(3,"0"),10):0));if(o){const v=(parseInt(l,10)*60+parseInt(f,10))*(o==="+"?-1:1);h.setUTCMinutes(h.getUTCMinutes()+v)}return Ge(h)?h:null}function Qe(e,t={}){const{strict:r=!1,warnings:n=!1,reviver:s,parseDates:u=!0}=t;if(typeof e!="string")return e;const a=e.trim();if(a[0]==='"'&&a.endsWith('"')&&!a.slice(1,-1).includes('"'))return a.slice(1,-1);const i=a.toLowerCase();if(i.length<=9&&i in X)return X[i];if(!Je.test(a)){if(r)throw new SyntaxError("[better-json] Invalid JSON");return e}if(Object.entries(He).some(([o,l])=>{const f=l.test(a);return f&&n&&console.warn(`[better-json] Detected potential prototype pollution attempt using ${o} pattern`),f})&&r)throw new Error("[better-json] Potential prototype pollution attempt detected");try{return JSON.parse(a,(l,f)=>{if(l==="__proto__"||l==="constructor"&&f&&typeof f=="object"&&"prototype"in f){n&&console.warn(`[better-json] Dropping "${l}" key to prevent prototype pollution`);return}if(u&&typeof f=="string"){const h=ze(f);if(h)return h}return s?s(l,f):f})}catch(o){if(r)throw o;return e}}function Xe(e,t={strict:!0}){return Qe(e,t)}const Ye={id:"redirect",name:"Redirect",hooks:{onSuccess(e){var t,r;if((t=e.data)!=null&&t.url&&((r=e.data)!=null&&r.redirect)&&typeof window<"u"&&window.location&&window.location)try{window.location.href=e.data.url}catch{}}}};function Ze(e){const t=te(!1);return{session:We(t,"/get-session",e,{method:"GET"}),$sessionSignal:t}}const Ke=e=>{var v,S,I,p,U;const t="credentials"in Request.prototype,r=Ce(e==null?void 0:e.baseURL,e==null?void 0:e.basePath),n=((v=e==null?void 0:e.plugins)==null?void 0:v.flatMap(d=>d.fetchPlugins).filter(d=>d!==void 0))||[],s=Ue({baseURL:r,...t?{credentials:"include"}:{},method:"GET",jsonParser(d){return d?Xe(d,{strict:!1}):null},customFetchImpl:async(d,R)=>{try{return await fetch(d,R)}catch{return Response.error()}},...e==null?void 0:e.fetchOptions,plugins:e!=null&&e.disableDefaultFetchPlugins?[...((S=e==null?void 0:e.fetchOptions)==null?void 0:S.plugins)||[],...n]:[Ye,...((I=e==null?void 0:e.fetchOptions)==null?void 0:I.plugins)||[],...n]}),{$sessionSignal:u,session:a}=Ze(s),i=(e==null?void 0:e.plugins)||[];let c={},o={$sessionSignal:u,session:a},l={"/sign-out":"POST","/revoke-sessions":"POST","/revoke-other-sessions":"POST","/delete-user":"POST"};const f=[{signal:"$sessionSignal",matcher(d){return d==="/sign-out"||d==="/update-user"||d.startsWith("/sign-in")||d.startsWith("/sign-up")||d==="/delete-user"||d==="/verify-email"}}];for(const d of i)d.getAtoms&&Object.assign(o,(p=d.getAtoms)==null?void 0:p.call(d,s)),d.pathMethods&&Object.assign(l,d.pathMethods),d.atomListeners&&f.push(...d.atomListeners);const h={notify:d=>{o[d].set(!o[d].get())},listen:(d,R)=>{o[d].subscribe(R)},atoms:o};for(const d of i)d.getActions&&Object.assign(c,(U=d.getActions)==null?void 0:U.call(d,s,h,e));return{pluginsActions:c,pluginsAtoms:o,pluginPathMethods:l,atomListeners:f,$fetch:s,$store:h}};function et(e,t,r){const n=t[e],{fetchOptions:s,query:u,...a}=r||{};return n||(s!=null&&s.method?s.method:a&&Object.keys(a).length>0?"POST":"GET")}function tt(e,t,r,n,s){function u(a=[]){return new Proxy(function(){},{get(i,c){const o=[...a,c];let l=e;for(const f of o)if(l&&typeof l=="object"&&f in l)l=l[f];else{l=void 0;break}return typeof l=="function"?l:u(o)},apply:async(i,c,o)=>{const l="/"+a.map(d=>d.replace(/[A-Z]/g,R=>`-${R.toLowerCase()}`)).join("/"),f=o[0]||{},h=o[1]||{},{query:v,fetchOptions:S,...I}=f,p={...h,...S},U=et(l,r,f);return await t(l,{...p,body:U==="GET"?void 0:{...I,...(p==null?void 0:p.body)||{}},query:v||(p==null?void 0:p.query),method:U,async onSuccess(d){var y;await((y=p==null?void 0:p.onSuccess)==null?void 0:y.call(p,d));const R=s==null?void 0:s.find(x=>x.matcher(l));if(!R)return;const m=n[R.signal];if(!m)return;const D=m.get();setTimeout(()=>{m.set(!D)},10)}})}})}return u()}function rt(e,t={}){let r=B.useRef(e.get());const{keys:n,deps:s=[e,n]}=t;let u=B.useCallback(i=>{const c=o=>{r.current!==o&&(r.current=o,i())};return c(e.value),n!=null&&n.length?Be(e,n,c):e.listen(c)},s),a=()=>r.current;return B.useSyncExternalStore(u,a,a)}function nt(e){return`use${st(e)}`}function st(e){return e.charAt(0).toUpperCase()+e.slice(1)}function ot(e){const{pluginPathMethods:t,pluginsActions:r,pluginsAtoms:n,$fetch:s,$store:u,atomListeners:a}=Ke(e);let i={};for(const[l,f]of Object.entries(n))i[nt(l)]=()=>rt(f);const c={...r,...i,$fetch:s,$store:u};return tt(c,s,t,n,a)}function at(e){return{authorize(t,r="AND"){let n=!1;for(const[s,u]of Object.entries(t)){const a=e[s];if(!a)return{success:!1,error:`You are not allowed to access resource: ${s}`};if(Array.isArray(u))n=u.every(i=>a.includes(i));else if(typeof u=="object"){const i=u;i.connector==="OR"?n=i.actions.some(c=>a.includes(c)):n=i.actions.every(c=>a.includes(c))}else throw new ee("Invalid access control request");if(n&&r==="OR")return{success:n};if(!n&&r==="AND")return{success:!1,error:`unauthorized to access resource "${s}"`}}return n?{success:n}:{success:!1,error:"Not authorized"}},statements:e}}function it(e){return{newRole(t){return at(t)},statements:e}}const ut={user:["create","list","set-role","ban","impersonate","delete","set-password"],session:["list","revoke","delete"]},re=it(ut),ne=re.newRole({user:["create","list","set-role","ban","impersonate","delete","set-password"],session:["list","revoke","delete"]}),se=re.newRole({user:[],session:[]}),ct={admin:ne,user:se},lt=e=>{var n,s,u,a;if(e.userId&&((s=(n=e.options)==null?void 0:n.adminUserIds)!=null&&s.includes(e.userId)))return!0;if(!e.permissions&&!e.permission)return!1;const t=(e.role||((u=e.options)==null?void 0:u.defaultRole)||"user").split(","),r=((a=e.options)==null?void 0:a.roles)||ct;for(const i of t){const c=r[i],o=c==null?void 0:c.authorize(e.permission??e.permissions);if(o!=null&&o.success)return!0}return!1},ft=e=>({id:"additional-fields-client",$InferServerPlugin:{}}),dt=e=>{const t={admin:ne,user:se,...e==null?void 0:e.roles};return{id:"admin-client",$InferServerPlugin:{},getActions:r=>({admin:{checkRolePermission:n=>lt({role:n.role,options:{ac:e==null?void 0:e.ac,roles:t},permissions:n.permissions??n.permission})}}),pathMethods:{"/admin/list-users":"GET","/admin/stop-impersonating":"POST"}}},mt=ot({baseURL:"http://localhost:3000",plugins:[dt(),ft()]});export{mt as a};
