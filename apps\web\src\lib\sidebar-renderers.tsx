import * as React from "react";
import { useNavigate } from "@tanstack/react-router";
import { SidebarMenuSubButton } from "@/components/ui/sidebar";

export type CustomRendererType = "dialog" | "action" | "navigation";

export interface CustomRendererConfig {
  type: CustomRendererType;
  component?: React.ComponentType<any>;
  action?: () => void;
  navigationPath?: string;
  onSuccess?: () => void;
  successPropName?: string; // Name of the success callback prop (e.g., 'onArtistCreated', 'onReleaseCreated')
  props?: Record<string, any>;
}

export interface SidebarCustomRenderers {
  [menuItemTitle: string]: CustomRendererConfig;
}

/**
 * Factory function to create custom renderers for sidebar menu items
 */
export function createCustomRenderer(
  config: CustomRendererConfig
): (subItem: { title: string; url: string }) => React.ReactNode {
  return (subItem) => {
    switch (config.type) {
      case "dialog":
        if (!config.component) {
          throw new Error("Dialog type requires a component");
        }

        const DialogComponent = config.component;

        // Create props object with the success callback mapped to the correct prop name
        const componentProps = {
          ...(config.props || {}),
        };

        // Map the success callback to the correct prop name
        if (config.onSuccess && config.successPropName) {
          componentProps[config.successPropName] = config.onSuccess;
        }

        return (
          <DialogComponent {...componentProps}>
            <SidebarMenuSubButton className="cursor-pointer">
              <span>{subItem.title}</span>
            </SidebarMenuSubButton>
          </DialogComponent>
        );

      case "action":
        return (
          <SidebarMenuSubButton
            className="cursor-pointer"
            onClick={config.action}
          >
            <span>{subItem.title}</span>
          </SidebarMenuSubButton>
        );

      case "navigation":
        return (
          <SidebarMenuSubButton className="cursor-pointer">
            <a href={config.navigationPath || subItem.url}>
              <span>{subItem.title}</span>
            </a>
          </SidebarMenuSubButton>
        );

      default:
        return (
          <SidebarMenuSubButton className="cursor-pointer">
            <a href={subItem.url}>
              <span>{subItem.title}</span>
            </a>
          </SidebarMenuSubButton>
        );
    }
  };
}

/**
 * Hook to create sidebar custom renderers with navigation
 */
export function useSidebarRenderers(rendererConfigs: SidebarCustomRenderers): {
  [key: string]: (subItem: { title: string; url: string }) => React.ReactNode;
} {
  const navigate = useNavigate();

  return React.useMemo(() => {
    const renderers: {
      [key: string]: (subItem: {
        title: string;
        url: string;
      }) => React.ReactNode;
    } = {};

    Object.entries(rendererConfigs).forEach(([title, config]) => {
      // Create a copy of config to avoid mutating the original
      const configCopy = { ...config };

      // Inject navigation handler for dialog success callbacks if navigationPath is provided
      if (
        configCopy.type === "dialog" &&
        configCopy.navigationPath &&
        !configCopy.onSuccess
      ) {
        configCopy.onSuccess = () =>
          navigate({ to: configCopy.navigationPath! });
      }

      renderers[title] = createCustomRenderer(configCopy);
    });

    return renderers;
  }, [rendererConfigs, navigate]);
}
