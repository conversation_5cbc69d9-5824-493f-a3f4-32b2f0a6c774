import{c as w,r as u,j as e,a as k,t as p,N as W,o as Y,k as J}from"./main-B9Fv5CdX.js";import{c as V,R as Q,I as X,S as Z,a as ee}from"./scroll-area-BkIMWkxZ.js";import{a as h,b as ae,B as f}from"./button-Ispz1G12.js";import{q as se,u as te,c as ne,a as ie,b as E,P as le,j as re,k as ce,l as oe,m as de,n as me,o as ue}from"./dialog-iGlJJq5Q.js";import{P as S,L as g,I as j}from"./label-CNQvdrLZ.js";import{S as T}from"./separator-CMLPp7Z2.js";import{u as M}from"./useForm-BIMU_iAr.js";import{a as I}from"./auth-client-C8WifPV2.js";import{u as A}from"./useMutation-DGkS69KN.js";import{S as y,D as xe}from"./send-DtqpyuMn.js";import{T as pe}from"./triangle-alert-BretsIXJ.js";import{o as _,b as he,s as b}from"./types-BF4s_UBG.js";import{M as ge}from"./mail-9LfZMTcO.js";/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ve=[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]],fe=w("House",ve);/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const je=[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]],be=w("Lock",je);/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ne=[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]],R=w("Save",Ne);/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ye=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M17 12h-2l-2 5-2-10-2 5H7",key:"15hlnc"}]],we=w("SquareActivity",ye);var C="Tabs",[Se,Xe]=ne(C,[V]),D=V(),[Ce,F]=Se(C),z=u.forwardRef((s,n)=>{const{__scopeTabs:l,value:i,onValueChange:r,defaultValue:o,orientation:c="horizontal",dir:d,activationMode:a="automatic",...t}=s,x=se(d),[m,v]=te({prop:i,onChange:r,defaultProp:o??"",caller:C});return e.jsx(Ce,{scope:l,baseId:ie(),value:m,onValueChange:v,orientation:c,dir:x,activationMode:a,children:e.jsx(S.div,{dir:x,"data-orientation":c,...t,ref:n})})});z.displayName=C;var B="TabsList",L=u.forwardRef((s,n)=>{const{__scopeTabs:l,loop:i=!0,...r}=s,o=F(B,l),c=D(l);return e.jsx(Q,{asChild:!0,...c,orientation:o.orientation,dir:o.dir,loop:i,children:e.jsx(S.div,{role:"tablist","aria-orientation":o.orientation,...r,ref:n})})});L.displayName=B;var $="TabsTrigger",q=u.forwardRef((s,n)=>{const{__scopeTabs:l,value:i,disabled:r=!1,...o}=s,c=F($,l),d=D(l),a=O(c.baseId,i),t=K(c.baseId,i),x=i===c.value;return e.jsx(X,{asChild:!0,...d,focusable:!r,active:x,children:e.jsx(S.button,{type:"button",role:"tab","aria-selected":x,"aria-controls":t,"data-state":x?"active":"inactive","data-disabled":r?"":void 0,disabled:r,id:a,...o,ref:n,onMouseDown:E(s.onMouseDown,m=>{!r&&m.button===0&&m.ctrlKey===!1?c.onValueChange(i):m.preventDefault()}),onKeyDown:E(s.onKeyDown,m=>{[" ","Enter"].includes(m.key)&&c.onValueChange(i)}),onFocus:E(s.onFocus,()=>{const m=c.activationMode!=="manual";!x&&!r&&m&&c.onValueChange(i)})})})});q.displayName=$;var H="TabsContent",G=u.forwardRef((s,n)=>{const{__scopeTabs:l,value:i,forceMount:r,children:o,...c}=s,d=F(H,l),a=O(d.baseId,i),t=K(d.baseId,i),x=i===d.value,m=u.useRef(x);return u.useEffect(()=>{const v=requestAnimationFrame(()=>m.current=!1);return()=>cancelAnimationFrame(v)},[]),e.jsx(le,{present:r||x,children:({present:v})=>e.jsx(S.div,{"data-state":x?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":a,hidden:!v,id:t,tabIndex:0,...c,ref:n,style:{...s.style,animationDuration:m.current?"0s":void 0},children:v&&o})})});G.displayName=H;function O(s,n){return`${s}-trigger-${n}`}function K(s,n){return`${s}-content-${n}`}var Ee=z,Pe=L,Te=q,ke=G;function Ae({className:s,...n}){return e.jsx(Ee,{"data-slot":"tabs",className:h("flex flex-col gap-2",s),...n})}function Fe({className:s,...n}){return e.jsx(Pe,{"data-slot":"tabs-list",className:h("bg-muted text-muted-foreground/70 inline-flex w-fit items-center justify-center rounded-md p-0.5",s),...n})}function N({className:s,...n}){return e.jsx(Te,{"data-slot":"tabs-trigger",className:h("hover:text-muted-foreground data-[state=active]:bg-background data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 inline-flex items-center justify-center rounded-sm px-3 py-1.5 text-sm font-medium whitespace-nowrap transition-all outline-none focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-xs [&_svg]:shrink-0",s),...n})}function P({className:s,...n}){return e.jsx(ke,{"data-slot":"tabs-content",className:h("flex-1 outline-none",s),...n})}const Re=ae("relative rounded-lg border",{variants:{variant:{default:"border-border bg-background",warning:"border-amber-500/50 text-amber-600",error:"border-red-500/50 text-red-600",success:"border-emerald-500/50",info:"border-blue-500/50 text-blue-600"},size:{sm:"px-4 py-3",lg:"p-4"},isNotification:{true:"z-[100] max-w-[400px] bg-background shadow-lg shadow-black/5",false:""}},defaultVariants:{variant:"default",size:"sm",isNotification:!1}}),U=u.forwardRef(({className:s,variant:n,size:l,isNotification:i,icon:r,action:o,layout:c="row",children:d,...a},t)=>e.jsx("div",{ref:t,role:"alert",className:h(Re({variant:n,size:l,isNotification:i}),s),...a,children:c==="row"?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"grow flex items-center",children:[r&&e.jsx("span",{className:"me-3 inline-flex",children:r}),d]}),o&&e.jsx("div",{className:"flex items-center shrink-0",children:o})]}):e.jsxs("div",{className:"flex gap-2",children:[r&&d?e.jsxs("div",{className:"flex grow gap-3",children:[e.jsx("span",{className:"mt-0.5 shrink-0",children:r}),e.jsx("div",{className:"grow",children:d})]}):e.jsxs("div",{className:"grow",children:[r&&e.jsx("span",{className:"me-3 inline-flex",children:r}),d]}),o&&e.jsx("div",{className:"shrink-0",children:o})]})}));U.displayName="Alert";const Ve=u.forwardRef(({className:s,...n},l)=>e.jsx("h5",{ref:l,className:h("text-sm font-medium",s),...n}));Ve.displayName="AlertTitle";const Me=u.forwardRef(({className:s,...n},l)=>e.jsx("div",{ref:l,className:h("text-sm text-muted-foreground",s),...n}));Me.displayName="AlertDescription";const Ie=u.forwardRef(({className:s,...n},l)=>e.jsx("div",{ref:l,className:h("space-y-1",s),...n}));Ie.displayName="AlertContent";function _e({session:s}){var r,o,c,d;const n=k(),l=A({mutationFn:()=>n.email.sendVerificationEmail.mutate(),onSuccess:()=>{p.success("Verification email sent! Please check your inbox.")},onError:a=>{p.error(a.message||"Failed to send verification email")}}),i=M({defaultValues:{name:((r=s==null?void 0:s.user)==null?void 0:r.name)||"",email:((o=s==null?void 0:s.user)==null?void 0:o.email)||"",phone:((c=s==null?void 0:s.user)==null?void 0:c.phone)||"",emailVerified:((d=s==null?void 0:s.user)==null?void 0:d.emailVerified)||!1},onSubmit:async({value:a})=>{await I.updateUser({name:a.name,phone:a.phone},{onSuccess:()=>{p.success("Profile updated successfully"),W({to:"/dashboard"})},onError:t=>{p.error(t.error.message)}})},validators:{onSubmit:_({name:b().min(1,"Full name is required"),phone:b().min(1,"Phone number is required").refine(a=>!a.includes("+"),"Phone number cannot contain +"),email:b().email("Invalid email address"),emailVerified:he()})}});return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex flex-col mb-4 sm:mb-5",children:[e.jsx("div",{className:"text-md sm:text-lg font-bold",children:"Personal Information"}),e.jsx("div",{className:"text-xs sm:text-sm text-muted-foreground",children:"Update your personal information here"})]}),e.jsx(T,{className:"my-4"}),e.jsxs("form",{onSubmit:a=>{a.preventDefault(),a.stopPropagation(),i.handleSubmit()},className:"space-y-6 sm:space-y-8",children:[e.jsx("div",{children:e.jsx(i.Field,{name:"name",children:a=>e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-1 lg:grid-cols-5 gap-4 lg:gap-6 xl:gap-8 items-start",children:[e.jsxs("div",{className:"lg:col-span-2 space-y-1",children:[e.jsx(g,{htmlFor:a.name,className:"text-sm font-medium",children:"Full Name"}),e.jsx("p",{className:"text-xs sm:text-sm text-muted-foreground leading-relaxed",children:"Your legal name is essential for payment verification and will be used to validate your identity during any financial transactions"})]}),e.jsxs("div",{className:"lg:col-span-3 space-y-2",children:[e.jsx(j,{id:a.name,name:a.name,type:"text",placeholder:"Enter your full name",value:a.state.value,onBlur:a.handleBlur,onChange:t=>a.handleChange(t.target.value),className:"w-full"}),a.state.meta.errors.map(t=>e.jsx("p",{className:"text-destructive text-sm",children:t==null?void 0:t.message},t==null?void 0:t.message))]})]})})}),!(s!=null&&s.user.emailVerified)&&e.jsx(U,{layout:"row",icon:e.jsx(pe,{className:"text-amber-500",size:16,strokeWidth:2}),children:e.jsxs("div",{className:"flex grow justify-between gap-3",children:[e.jsx("p",{className:"text-sm",children:"Your email address is currently not verified. Please verify your email address."}),e.jsxs("button",{onClick:()=>l.mutate(),disabled:l.isPending,className:"group whitespace-nowrap text-sm font-medium hover:text-primary disabled:opacity-50 disabled:cursor-not-allowed",children:[l.isPending?"Sending...":"Verify Email",e.jsx(y,{className:"-mt-0.5 ms-1 inline-flex opacity-60 transition-transform group-hover:translate-x-0.5",size:16,strokeWidth:2})]})]})}),e.jsx("div",{children:e.jsx(i.Field,{name:"email",children:a=>e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-1 lg:grid-cols-5 gap-4 lg:gap-6 xl:gap-8 items-start",children:[e.jsxs("div",{className:"lg:col-span-2 space-y-1",children:[e.jsx(g,{htmlFor:a.name,className:"text-sm font-medium",children:"Email Address"}),e.jsx("p",{className:"text-xs sm:text-sm text-muted-foreground leading-relaxed",children:"Your email address is used for account access and receiving important notifications"})]}),e.jsxs("div",{className:"lg:col-span-3 space-y-2",children:[e.jsx(j,{id:a.name,name:a.name,type:"email",placeholder:"Enter your email",disabled:!0,value:a.state.value,onBlur:a.handleBlur,onChange:t=>a.handleChange(t.target.value),className:"w-full"}),e.jsx("p",{className:"text-xs sm:text-sm text-muted-foreground leading-relaxed",children:"Request to change email address on account security tab"}),a.state.meta.errors.map(t=>e.jsx("p",{className:"text-destructive text-sm",children:t==null?void 0:t.message},t==null?void 0:t.message))]})]})})}),e.jsx("div",{children:e.jsx(i.Field,{name:"phone",children:a=>e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-1 lg:grid-cols-5 gap-4 lg:gap-6 xl:gap-8 items-start",children:[e.jsxs("div",{className:"lg:col-span-2 space-y-1",children:[e.jsx(g,{htmlFor:a.name,className:"text-sm font-medium",children:"Phone Number"}),e.jsx("p",{className:"text-xs sm:text-sm text-muted-foreground leading-relaxed",children:"WhatsApp number is preferred for further support and verification purposes"})]}),e.jsxs("div",{className:"lg:col-span-3 space-y-2",children:[e.jsx(j,{id:a.name,name:a.name,type:"tel",placeholder:"Enter your phone number",value:a.state.value,onBlur:a.handleBlur,onChange:t=>a.handleChange(t.target.value),className:"w-full"}),a.state.meta.errors.map(t=>e.jsx("p",{className:"text-destructive text-sm",children:t==null?void 0:t.message},t==null?void 0:t.message))]})]})})}),e.jsx(T,{className:"my-4"}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-1 lg:grid-cols-5 gap-4 lg:gap-6 xl:gap-8",children:[e.jsx("div",{className:"lg:col-span-2"}),e.jsx("div",{className:"lg:col-span-3",children:e.jsx(i.Subscribe,{selector:a=>[a.canSubmit,a.isSubmitting],children:([a,t])=>e.jsx("div",{className:"flex flex-col sm:flex-row gap-3 sm:gap-4",children:e.jsx(f,{type:"submit",disabled:!a,className:"w-full sm:w-auto sm:min-w-[160px] h-10",size:"default",children:t?e.jsxs(e.Fragment,{children:[e.jsx(R,{className:"h-4 w-4 animate-spin"}),e.jsx("span",{children:"Saving..."})]}):e.jsxs(e.Fragment,{children:[e.jsx(R,{className:"h-4 w-4"}),e.jsx("span",{children:"Save Changes"})]})})})})})]})]})]})}function De({currentEmail:s,trigger:n}){const[l,i]=u.useState(!1),r=k(),o=A({mutationFn:a=>r.email.requestEmailChange.mutate({newEmail:a}),onSuccess:()=>{p.success("Email change verification sent! Please check your current email inbox."),i(!1),c.reset()},onError:a=>{p.error(a.message||"Failed to send email change verification")}}),c=M({defaultValues:{newEmail:"",confirmEmail:""},onSubmit:async({value:a})=>{o.mutate(a.newEmail)},validators:{onSubmit:_({newEmail:b().email("Please enter a valid email address").refine(a=>a!==s,"New email must be different from current email"),confirmEmail:b().email("Please enter a valid email address")}).refine(a=>a.newEmail===a.confirmEmail,{message:"Email addresses don't match",path:["confirmEmail"]})}}),d=e.jsxs(f,{type:"button",variant:"default",className:"w-full sm:min-w-[180px] h-10",children:[e.jsx(y,{className:"h-4 w-4"}),"Send Email Change Link"]});return e.jsxs(re,{open:l,onOpenChange:i,children:[e.jsx(ce,{asChild:!0,children:n||d}),e.jsxs(oe,{className:"sm:max-w-md",children:[e.jsxs(de,{className:"gap-4",children:[e.jsxs(me,{className:"flex items-center gap-2",children:[e.jsx(ge,{className:"h-5 w-5"}),"Change Email Address"]}),e.jsxs(ue,{children:["Enter your new email address. A verification link will be sent to your ",e.jsx("strong",{children:"current email"})," (",s,") to approve this change."]})]}),e.jsxs("form",{onSubmit:a=>{a.preventDefault(),a.stopPropagation(),c.handleSubmit()},className:"space-y-6",children:[e.jsx("div",{children:e.jsx(c.Field,{name:"newEmail",children:a=>e.jsxs("div",{className:"space-y-2",children:[e.jsx(g,{htmlFor:a.name,children:"New Email Address"}),e.jsx(j,{id:a.name,name:a.name,type:"email",placeholder:"Enter your new email address",value:a.state.value,onBlur:a.handleBlur,onChange:t=>a.handleChange(t.target.value)}),a.state.meta.errors.map(t=>e.jsx("p",{className:"text-destructive text-sm",children:t==null?void 0:t.message},t==null?void 0:t.message))]})})}),e.jsx("div",{children:e.jsx(c.Field,{name:"confirmEmail",children:a=>e.jsxs("div",{className:"space-y-2",children:[e.jsx(g,{htmlFor:a.name,children:"Confirm New Email Address"}),e.jsx(j,{id:a.name,name:a.name,type:"email",placeholder:"Confirm your new email address",value:a.state.value,onBlur:a.handleBlur,onChange:t=>a.handleChange(t.target.value)}),a.state.meta.errors.map(t=>e.jsx("p",{className:"text-destructive text-sm",children:t==null?void 0:t.message},t==null?void 0:t.message))]})})}),e.jsxs("div",{className:"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 space-y-2 space-y-reverse sm:space-y-0",children:[e.jsx(f,{type:"button",variant:"outline",onClick:()=>i(!1),disabled:o.isPending,children:"Cancel"}),e.jsx(c.Subscribe,{children:a=>e.jsxs(f,{type:"submit",disabled:!a.canSubmit||a.isSubmitting||o.isPending,children:[e.jsx(y,{className:"h-4 w-4"}),o.isPending?"Sending...":"Send Verification"]})})]})]})]})]})}function ze({session:s}){var i;const n=k(),l=A({mutationFn:r=>n.email.sendPasswordResetEmail.mutate({email:r}),onSuccess:()=>{p.success("Password reset email sent! Please check your inbox.")},onError:r=>{p.error(r.message||"Failed to send password reset email")}});return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex flex-col mb-4 sm:mb-5",children:[e.jsx("div",{className:"text-md sm:text-lg font-bold",children:"Account Security"}),e.jsx("div",{className:"text-xs sm:text-sm text-muted-foreground",children:"Update your account security here"})]}),e.jsx(T,{className:"my-4"}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-1 lg:grid-cols-5 gap-4 lg:gap-6 xl:gap-8 items-start",children:[e.jsxs("div",{className:"lg:col-span-2 space-y-1",children:[e.jsx(g,{className:"text-sm font-medium",children:"Change Email Address"}),e.jsx("p",{className:"text-xs sm:text-sm text-muted-foreground leading-relaxed",children:"Send a email change link to your email address"})]}),e.jsx("div",{className:"lg:col-span-3 space-y-2",children:e.jsx(De,{currentEmail:((i=s==null?void 0:s.user)==null?void 0:i.email)||""})}),e.jsxs("div",{className:"lg:col-span-2 space-y-1",children:[e.jsx(g,{className:"text-sm font-medium",children:"Request Password Reset"}),e.jsx("p",{className:"text-xs sm:text-sm text-muted-foreground leading-relaxed",children:"Send a secure password reset link to your email"})]}),e.jsx("div",{className:"lg:col-span-3 space-y-2",children:e.jsxs(f,{type:"button",variant:"default",className:"w-full sm:min-w-[180px] h-10",onClick:()=>{var r;(r=s==null?void 0:s.user)!=null&&r.email?l.mutate(s.user.email):p.error("No email address found")},disabled:l.isPending,children:[e.jsx(y,{className:"h-4 w-4"}),l.isPending?"Sending...":"Send Password Reset Email"]})})]})]})}const Ze=function(){const n=Y.useNavigate(),{data:l,isPending:i}=I.useSession();return u.useEffect(()=>{!l&&!i&&n({to:"/auth"})},[l,i]),i?e.jsx(J,{}):e.jsxs("div",{children:[e.jsx("div",{className:"pl-2 text-xl font-bold",children:"Profile Settings"}),e.jsx("div",{className:"p-2 pt-4",children:e.jsxs(Ae,{defaultValue:"tab-1",children:[e.jsxs(Z,{children:[e.jsxs(Fe,{className:"mb-2 h-9 p-1 text-sm",children:[e.jsxs(N,{value:"tab-1",className:"h-7 px-3 text-sm cursor-pointer",children:[e.jsx(fe,{className:"-ms-0.5 me-1.5 opacity-60",size:14,"aria-hidden":"true"}),"Overview"]}),e.jsxs(N,{value:"tab-2",className:"group h-7 px-3 text-sm cursor-pointer",children:[e.jsx(be,{className:"-ms-0.5 me-1.5 opacity-60",size:14,"aria-hidden":"true"}),"Account Security"]}),e.jsxs(N,{value:"tab-3",className:"group h-7 px-3 text-sm cursor-pointer",children:[e.jsx(we,{className:"-ms-0.5 me-1.5 opacity-60",size:14,"aria-hidden":"true"}),"Activites"]}),e.jsxs(N,{value:"tab-4",className:"group h-7 px-3 text-sm cursor-pointer",children:[e.jsx(xe,{className:"-ms-0.5 me-1.5 opacity-60",size:14,"aria-hidden":"true"}),"Projects"]})]}),e.jsx(ee,{orientation:"horizontal"})]}),e.jsx(P,{value:"tab-1",children:e.jsx("div",{className:"p-1 pt-1",children:e.jsx(_e,{session:l})})}),e.jsx(P,{value:"tab-2",children:e.jsx("div",{className:"p-1 pt-1",children:e.jsx(ze,{session:l})})}),e.jsx(P,{value:"tab-3",children:e.jsx("p",{className:"text-muted-foreground p-4 pt-1 text-center text-xs",children:"Content for Tab 3"})})]})})]})};export{Ze as component};
