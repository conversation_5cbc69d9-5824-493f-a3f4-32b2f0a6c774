
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `TrackFile` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model TrackFile
 * 
 */
export type TrackFileModel = runtime.Types.Result.DefaultSelection<Prisma.$TrackFilePayload>

export type AggregateTrackFile = {
  _count: TrackFileCountAggregateOutputType | null
  _avg: TrackFileAvgAggregateOutputType | null
  _sum: TrackFileSumAggregateOutputType | null
  _min: TrackFileMinAggregateOutputType | null
  _max: TrackFileMaxAggregateOutputType | null
}

export type TrackFileAvgAggregateOutputType = {
  fileSize: number | null
  duration: number | null
}

export type TrackFileSumAggregateOutputType = {
  fileSize: number | null
  duration: number | null
}

export type TrackFileMinAggregateOutputType = {
  id: string | null
  trackId: string | null
  fileUrl: string | null
  fileKey: string | null
  fileName: string | null
  fileSize: number | null
  mimeType: string | null
  duration: number | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type TrackFileMaxAggregateOutputType = {
  id: string | null
  trackId: string | null
  fileUrl: string | null
  fileKey: string | null
  fileName: string | null
  fileSize: number | null
  mimeType: string | null
  duration: number | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type TrackFileCountAggregateOutputType = {
  id: number
  trackId: number
  fileUrl: number
  fileKey: number
  fileName: number
  fileSize: number
  mimeType: number
  duration: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type TrackFileAvgAggregateInputType = {
  fileSize?: true
  duration?: true
}

export type TrackFileSumAggregateInputType = {
  fileSize?: true
  duration?: true
}

export type TrackFileMinAggregateInputType = {
  id?: true
  trackId?: true
  fileUrl?: true
  fileKey?: true
  fileName?: true
  fileSize?: true
  mimeType?: true
  duration?: true
  createdAt?: true
  updatedAt?: true
}

export type TrackFileMaxAggregateInputType = {
  id?: true
  trackId?: true
  fileUrl?: true
  fileKey?: true
  fileName?: true
  fileSize?: true
  mimeType?: true
  duration?: true
  createdAt?: true
  updatedAt?: true
}

export type TrackFileCountAggregateInputType = {
  id?: true
  trackId?: true
  fileUrl?: true
  fileKey?: true
  fileName?: true
  fileSize?: true
  mimeType?: true
  duration?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type TrackFileAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which TrackFile to aggregate.
   */
  where?: Prisma.TrackFileWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of TrackFiles to fetch.
   */
  orderBy?: Prisma.TrackFileOrderByWithRelationInput | Prisma.TrackFileOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.TrackFileWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` TrackFiles from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` TrackFiles.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned TrackFiles
  **/
  _count?: true | TrackFileCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: TrackFileAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: TrackFileSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: TrackFileMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: TrackFileMaxAggregateInputType
}

export type GetTrackFileAggregateType<T extends TrackFileAggregateArgs> = {
      [P in keyof T & keyof AggregateTrackFile]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateTrackFile[P]>
    : Prisma.GetScalarType<T[P], AggregateTrackFile[P]>
}




export type TrackFileGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.TrackFileWhereInput
  orderBy?: Prisma.TrackFileOrderByWithAggregationInput | Prisma.TrackFileOrderByWithAggregationInput[]
  by: Prisma.TrackFileScalarFieldEnum[] | Prisma.TrackFileScalarFieldEnum
  having?: Prisma.TrackFileScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: TrackFileCountAggregateInputType | true
  _avg?: TrackFileAvgAggregateInputType
  _sum?: TrackFileSumAggregateInputType
  _min?: TrackFileMinAggregateInputType
  _max?: TrackFileMaxAggregateInputType
}

export type TrackFileGroupByOutputType = {
  id: string
  trackId: string
  fileUrl: string
  fileKey: string
  fileName: string | null
  fileSize: number | null
  mimeType: string | null
  duration: number | null
  createdAt: Date
  updatedAt: Date
  _count: TrackFileCountAggregateOutputType | null
  _avg: TrackFileAvgAggregateOutputType | null
  _sum: TrackFileSumAggregateOutputType | null
  _min: TrackFileMinAggregateOutputType | null
  _max: TrackFileMaxAggregateOutputType | null
}

type GetTrackFileGroupByPayload<T extends TrackFileGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<TrackFileGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof TrackFileGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], TrackFileGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], TrackFileGroupByOutputType[P]>
      }
    >
  > 



export type TrackFileWhereInput = {
  AND?: Prisma.TrackFileWhereInput | Prisma.TrackFileWhereInput[]
  OR?: Prisma.TrackFileWhereInput[]
  NOT?: Prisma.TrackFileWhereInput | Prisma.TrackFileWhereInput[]
  id?: Prisma.StringFilter<"TrackFile"> | string
  trackId?: Prisma.StringFilter<"TrackFile"> | string
  fileUrl?: Prisma.StringFilter<"TrackFile"> | string
  fileKey?: Prisma.StringFilter<"TrackFile"> | string
  fileName?: Prisma.StringNullableFilter<"TrackFile"> | string | null
  fileSize?: Prisma.IntNullableFilter<"TrackFile"> | number | null
  mimeType?: Prisma.StringNullableFilter<"TrackFile"> | string | null
  duration?: Prisma.IntNullableFilter<"TrackFile"> | number | null
  createdAt?: Prisma.DateTimeFilter<"TrackFile"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"TrackFile"> | Date | string
  track?: Prisma.XOR<Prisma.TrackScalarRelationFilter, Prisma.TrackWhereInput>
}

export type TrackFileOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  trackId?: Prisma.SortOrder
  fileUrl?: Prisma.SortOrder
  fileKey?: Prisma.SortOrder
  fileName?: Prisma.SortOrderInput | Prisma.SortOrder
  fileSize?: Prisma.SortOrderInput | Prisma.SortOrder
  mimeType?: Prisma.SortOrderInput | Prisma.SortOrder
  duration?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  track?: Prisma.TrackOrderByWithRelationInput
}

export type TrackFileWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.TrackFileWhereInput | Prisma.TrackFileWhereInput[]
  OR?: Prisma.TrackFileWhereInput[]
  NOT?: Prisma.TrackFileWhereInput | Prisma.TrackFileWhereInput[]
  trackId?: Prisma.StringFilter<"TrackFile"> | string
  fileUrl?: Prisma.StringFilter<"TrackFile"> | string
  fileKey?: Prisma.StringFilter<"TrackFile"> | string
  fileName?: Prisma.StringNullableFilter<"TrackFile"> | string | null
  fileSize?: Prisma.IntNullableFilter<"TrackFile"> | number | null
  mimeType?: Prisma.StringNullableFilter<"TrackFile"> | string | null
  duration?: Prisma.IntNullableFilter<"TrackFile"> | number | null
  createdAt?: Prisma.DateTimeFilter<"TrackFile"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"TrackFile"> | Date | string
  track?: Prisma.XOR<Prisma.TrackScalarRelationFilter, Prisma.TrackWhereInput>
}, "id">

export type TrackFileOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  trackId?: Prisma.SortOrder
  fileUrl?: Prisma.SortOrder
  fileKey?: Prisma.SortOrder
  fileName?: Prisma.SortOrderInput | Prisma.SortOrder
  fileSize?: Prisma.SortOrderInput | Prisma.SortOrder
  mimeType?: Prisma.SortOrderInput | Prisma.SortOrder
  duration?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.TrackFileCountOrderByAggregateInput
  _avg?: Prisma.TrackFileAvgOrderByAggregateInput
  _max?: Prisma.TrackFileMaxOrderByAggregateInput
  _min?: Prisma.TrackFileMinOrderByAggregateInput
  _sum?: Prisma.TrackFileSumOrderByAggregateInput
}

export type TrackFileScalarWhereWithAggregatesInput = {
  AND?: Prisma.TrackFileScalarWhereWithAggregatesInput | Prisma.TrackFileScalarWhereWithAggregatesInput[]
  OR?: Prisma.TrackFileScalarWhereWithAggregatesInput[]
  NOT?: Prisma.TrackFileScalarWhereWithAggregatesInput | Prisma.TrackFileScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"TrackFile"> | string
  trackId?: Prisma.StringWithAggregatesFilter<"TrackFile"> | string
  fileUrl?: Prisma.StringWithAggregatesFilter<"TrackFile"> | string
  fileKey?: Prisma.StringWithAggregatesFilter<"TrackFile"> | string
  fileName?: Prisma.StringNullableWithAggregatesFilter<"TrackFile"> | string | null
  fileSize?: Prisma.IntNullableWithAggregatesFilter<"TrackFile"> | number | null
  mimeType?: Prisma.StringNullableWithAggregatesFilter<"TrackFile"> | string | null
  duration?: Prisma.IntNullableWithAggregatesFilter<"TrackFile"> | number | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"TrackFile"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"TrackFile"> | Date | string
}

export type TrackFileCreateInput = {
  id?: string
  fileUrl: string
  fileKey: string
  fileName?: string | null
  fileSize?: number | null
  mimeType?: string | null
  duration?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  track: Prisma.TrackCreateNestedOneWithoutTrackFilesInput
}

export type TrackFileUncheckedCreateInput = {
  id?: string
  trackId: string
  fileUrl: string
  fileKey: string
  fileName?: string | null
  fileSize?: number | null
  mimeType?: string | null
  duration?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type TrackFileUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  fileUrl?: Prisma.StringFieldUpdateOperationsInput | string
  fileKey?: Prisma.StringFieldUpdateOperationsInput | string
  fileName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fileSize?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  mimeType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  duration?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  track?: Prisma.TrackUpdateOneRequiredWithoutTrackFilesNestedInput
}

export type TrackFileUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  trackId?: Prisma.StringFieldUpdateOperationsInput | string
  fileUrl?: Prisma.StringFieldUpdateOperationsInput | string
  fileKey?: Prisma.StringFieldUpdateOperationsInput | string
  fileName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fileSize?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  mimeType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  duration?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TrackFileCreateManyInput = {
  id?: string
  trackId: string
  fileUrl: string
  fileKey: string
  fileName?: string | null
  fileSize?: number | null
  mimeType?: string | null
  duration?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type TrackFileUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  fileUrl?: Prisma.StringFieldUpdateOperationsInput | string
  fileKey?: Prisma.StringFieldUpdateOperationsInput | string
  fileName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fileSize?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  mimeType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  duration?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TrackFileUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  trackId?: Prisma.StringFieldUpdateOperationsInput | string
  fileUrl?: Prisma.StringFieldUpdateOperationsInput | string
  fileKey?: Prisma.StringFieldUpdateOperationsInput | string
  fileName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fileSize?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  mimeType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  duration?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TrackFileListRelationFilter = {
  every?: Prisma.TrackFileWhereInput
  some?: Prisma.TrackFileWhereInput
  none?: Prisma.TrackFileWhereInput
}

export type TrackFileOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type TrackFileCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  trackId?: Prisma.SortOrder
  fileUrl?: Prisma.SortOrder
  fileKey?: Prisma.SortOrder
  fileName?: Prisma.SortOrder
  fileSize?: Prisma.SortOrder
  mimeType?: Prisma.SortOrder
  duration?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type TrackFileAvgOrderByAggregateInput = {
  fileSize?: Prisma.SortOrder
  duration?: Prisma.SortOrder
}

export type TrackFileMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  trackId?: Prisma.SortOrder
  fileUrl?: Prisma.SortOrder
  fileKey?: Prisma.SortOrder
  fileName?: Prisma.SortOrder
  fileSize?: Prisma.SortOrder
  mimeType?: Prisma.SortOrder
  duration?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type TrackFileMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  trackId?: Prisma.SortOrder
  fileUrl?: Prisma.SortOrder
  fileKey?: Prisma.SortOrder
  fileName?: Prisma.SortOrder
  fileSize?: Prisma.SortOrder
  mimeType?: Prisma.SortOrder
  duration?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type TrackFileSumOrderByAggregateInput = {
  fileSize?: Prisma.SortOrder
  duration?: Prisma.SortOrder
}

export type TrackFileCreateNestedManyWithoutTrackInput = {
  create?: Prisma.XOR<Prisma.TrackFileCreateWithoutTrackInput, Prisma.TrackFileUncheckedCreateWithoutTrackInput> | Prisma.TrackFileCreateWithoutTrackInput[] | Prisma.TrackFileUncheckedCreateWithoutTrackInput[]
  connectOrCreate?: Prisma.TrackFileCreateOrConnectWithoutTrackInput | Prisma.TrackFileCreateOrConnectWithoutTrackInput[]
  createMany?: Prisma.TrackFileCreateManyTrackInputEnvelope
  connect?: Prisma.TrackFileWhereUniqueInput | Prisma.TrackFileWhereUniqueInput[]
}

export type TrackFileUncheckedCreateNestedManyWithoutTrackInput = {
  create?: Prisma.XOR<Prisma.TrackFileCreateWithoutTrackInput, Prisma.TrackFileUncheckedCreateWithoutTrackInput> | Prisma.TrackFileCreateWithoutTrackInput[] | Prisma.TrackFileUncheckedCreateWithoutTrackInput[]
  connectOrCreate?: Prisma.TrackFileCreateOrConnectWithoutTrackInput | Prisma.TrackFileCreateOrConnectWithoutTrackInput[]
  createMany?: Prisma.TrackFileCreateManyTrackInputEnvelope
  connect?: Prisma.TrackFileWhereUniqueInput | Prisma.TrackFileWhereUniqueInput[]
}

export type TrackFileUpdateManyWithoutTrackNestedInput = {
  create?: Prisma.XOR<Prisma.TrackFileCreateWithoutTrackInput, Prisma.TrackFileUncheckedCreateWithoutTrackInput> | Prisma.TrackFileCreateWithoutTrackInput[] | Prisma.TrackFileUncheckedCreateWithoutTrackInput[]
  connectOrCreate?: Prisma.TrackFileCreateOrConnectWithoutTrackInput | Prisma.TrackFileCreateOrConnectWithoutTrackInput[]
  upsert?: Prisma.TrackFileUpsertWithWhereUniqueWithoutTrackInput | Prisma.TrackFileUpsertWithWhereUniqueWithoutTrackInput[]
  createMany?: Prisma.TrackFileCreateManyTrackInputEnvelope
  set?: Prisma.TrackFileWhereUniqueInput | Prisma.TrackFileWhereUniqueInput[]
  disconnect?: Prisma.TrackFileWhereUniqueInput | Prisma.TrackFileWhereUniqueInput[]
  delete?: Prisma.TrackFileWhereUniqueInput | Prisma.TrackFileWhereUniqueInput[]
  connect?: Prisma.TrackFileWhereUniqueInput | Prisma.TrackFileWhereUniqueInput[]
  update?: Prisma.TrackFileUpdateWithWhereUniqueWithoutTrackInput | Prisma.TrackFileUpdateWithWhereUniqueWithoutTrackInput[]
  updateMany?: Prisma.TrackFileUpdateManyWithWhereWithoutTrackInput | Prisma.TrackFileUpdateManyWithWhereWithoutTrackInput[]
  deleteMany?: Prisma.TrackFileScalarWhereInput | Prisma.TrackFileScalarWhereInput[]
}

export type TrackFileUncheckedUpdateManyWithoutTrackNestedInput = {
  create?: Prisma.XOR<Prisma.TrackFileCreateWithoutTrackInput, Prisma.TrackFileUncheckedCreateWithoutTrackInput> | Prisma.TrackFileCreateWithoutTrackInput[] | Prisma.TrackFileUncheckedCreateWithoutTrackInput[]
  connectOrCreate?: Prisma.TrackFileCreateOrConnectWithoutTrackInput | Prisma.TrackFileCreateOrConnectWithoutTrackInput[]
  upsert?: Prisma.TrackFileUpsertWithWhereUniqueWithoutTrackInput | Prisma.TrackFileUpsertWithWhereUniqueWithoutTrackInput[]
  createMany?: Prisma.TrackFileCreateManyTrackInputEnvelope
  set?: Prisma.TrackFileWhereUniqueInput | Prisma.TrackFileWhereUniqueInput[]
  disconnect?: Prisma.TrackFileWhereUniqueInput | Prisma.TrackFileWhereUniqueInput[]
  delete?: Prisma.TrackFileWhereUniqueInput | Prisma.TrackFileWhereUniqueInput[]
  connect?: Prisma.TrackFileWhereUniqueInput | Prisma.TrackFileWhereUniqueInput[]
  update?: Prisma.TrackFileUpdateWithWhereUniqueWithoutTrackInput | Prisma.TrackFileUpdateWithWhereUniqueWithoutTrackInput[]
  updateMany?: Prisma.TrackFileUpdateManyWithWhereWithoutTrackInput | Prisma.TrackFileUpdateManyWithWhereWithoutTrackInput[]
  deleteMany?: Prisma.TrackFileScalarWhereInput | Prisma.TrackFileScalarWhereInput[]
}

export type TrackFileCreateWithoutTrackInput = {
  id?: string
  fileUrl: string
  fileKey: string
  fileName?: string | null
  fileSize?: number | null
  mimeType?: string | null
  duration?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type TrackFileUncheckedCreateWithoutTrackInput = {
  id?: string
  fileUrl: string
  fileKey: string
  fileName?: string | null
  fileSize?: number | null
  mimeType?: string | null
  duration?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type TrackFileCreateOrConnectWithoutTrackInput = {
  where: Prisma.TrackFileWhereUniqueInput
  create: Prisma.XOR<Prisma.TrackFileCreateWithoutTrackInput, Prisma.TrackFileUncheckedCreateWithoutTrackInput>
}

export type TrackFileCreateManyTrackInputEnvelope = {
  data: Prisma.TrackFileCreateManyTrackInput | Prisma.TrackFileCreateManyTrackInput[]
  skipDuplicates?: boolean
}

export type TrackFileUpsertWithWhereUniqueWithoutTrackInput = {
  where: Prisma.TrackFileWhereUniqueInput
  update: Prisma.XOR<Prisma.TrackFileUpdateWithoutTrackInput, Prisma.TrackFileUncheckedUpdateWithoutTrackInput>
  create: Prisma.XOR<Prisma.TrackFileCreateWithoutTrackInput, Prisma.TrackFileUncheckedCreateWithoutTrackInput>
}

export type TrackFileUpdateWithWhereUniqueWithoutTrackInput = {
  where: Prisma.TrackFileWhereUniqueInput
  data: Prisma.XOR<Prisma.TrackFileUpdateWithoutTrackInput, Prisma.TrackFileUncheckedUpdateWithoutTrackInput>
}

export type TrackFileUpdateManyWithWhereWithoutTrackInput = {
  where: Prisma.TrackFileScalarWhereInput
  data: Prisma.XOR<Prisma.TrackFileUpdateManyMutationInput, Prisma.TrackFileUncheckedUpdateManyWithoutTrackInput>
}

export type TrackFileScalarWhereInput = {
  AND?: Prisma.TrackFileScalarWhereInput | Prisma.TrackFileScalarWhereInput[]
  OR?: Prisma.TrackFileScalarWhereInput[]
  NOT?: Prisma.TrackFileScalarWhereInput | Prisma.TrackFileScalarWhereInput[]
  id?: Prisma.StringFilter<"TrackFile"> | string
  trackId?: Prisma.StringFilter<"TrackFile"> | string
  fileUrl?: Prisma.StringFilter<"TrackFile"> | string
  fileKey?: Prisma.StringFilter<"TrackFile"> | string
  fileName?: Prisma.StringNullableFilter<"TrackFile"> | string | null
  fileSize?: Prisma.IntNullableFilter<"TrackFile"> | number | null
  mimeType?: Prisma.StringNullableFilter<"TrackFile"> | string | null
  duration?: Prisma.IntNullableFilter<"TrackFile"> | number | null
  createdAt?: Prisma.DateTimeFilter<"TrackFile"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"TrackFile"> | Date | string
}

export type TrackFileCreateManyTrackInput = {
  id?: string
  fileUrl: string
  fileKey: string
  fileName?: string | null
  fileSize?: number | null
  mimeType?: string | null
  duration?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type TrackFileUpdateWithoutTrackInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  fileUrl?: Prisma.StringFieldUpdateOperationsInput | string
  fileKey?: Prisma.StringFieldUpdateOperationsInput | string
  fileName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fileSize?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  mimeType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  duration?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TrackFileUncheckedUpdateWithoutTrackInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  fileUrl?: Prisma.StringFieldUpdateOperationsInput | string
  fileKey?: Prisma.StringFieldUpdateOperationsInput | string
  fileName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fileSize?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  mimeType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  duration?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TrackFileUncheckedUpdateManyWithoutTrackInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  fileUrl?: Prisma.StringFieldUpdateOperationsInput | string
  fileKey?: Prisma.StringFieldUpdateOperationsInput | string
  fileName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fileSize?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  mimeType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  duration?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type TrackFileSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  trackId?: boolean
  fileUrl?: boolean
  fileKey?: boolean
  fileName?: boolean
  fileSize?: boolean
  mimeType?: boolean
  duration?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  track?: boolean | Prisma.TrackDefaultArgs<ExtArgs>
}, ExtArgs["result"]["trackFile"]>

export type TrackFileSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  trackId?: boolean
  fileUrl?: boolean
  fileKey?: boolean
  fileName?: boolean
  fileSize?: boolean
  mimeType?: boolean
  duration?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  track?: boolean | Prisma.TrackDefaultArgs<ExtArgs>
}, ExtArgs["result"]["trackFile"]>

export type TrackFileSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  trackId?: boolean
  fileUrl?: boolean
  fileKey?: boolean
  fileName?: boolean
  fileSize?: boolean
  mimeType?: boolean
  duration?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  track?: boolean | Prisma.TrackDefaultArgs<ExtArgs>
}, ExtArgs["result"]["trackFile"]>

export type TrackFileSelectScalar = {
  id?: boolean
  trackId?: boolean
  fileUrl?: boolean
  fileKey?: boolean
  fileName?: boolean
  fileSize?: boolean
  mimeType?: boolean
  duration?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type TrackFileOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "trackId" | "fileUrl" | "fileKey" | "fileName" | "fileSize" | "mimeType" | "duration" | "createdAt" | "updatedAt", ExtArgs["result"]["trackFile"]>
export type TrackFileInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  track?: boolean | Prisma.TrackDefaultArgs<ExtArgs>
}
export type TrackFileIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  track?: boolean | Prisma.TrackDefaultArgs<ExtArgs>
}
export type TrackFileIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  track?: boolean | Prisma.TrackDefaultArgs<ExtArgs>
}

export type $TrackFilePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "TrackFile"
  objects: {
    track: Prisma.$TrackPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    trackId: string
    fileUrl: string
    fileKey: string
    fileName: string | null
    fileSize: number | null
    mimeType: string | null
    duration: number | null
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["trackFile"]>
  composites: {}
}

export type TrackFileGetPayload<S extends boolean | null | undefined | TrackFileDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$TrackFilePayload, S>

export type TrackFileCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<TrackFileFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: TrackFileCountAggregateInputType | true
  }

export interface TrackFileDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['TrackFile'], meta: { name: 'TrackFile' } }
  /**
   * Find zero or one TrackFile that matches the filter.
   * @param {TrackFileFindUniqueArgs} args - Arguments to find a TrackFile
   * @example
   * // Get one TrackFile
   * const trackFile = await prisma.trackFile.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends TrackFileFindUniqueArgs>(args: Prisma.SelectSubset<T, TrackFileFindUniqueArgs<ExtArgs>>): Prisma.Prisma__TrackFileClient<runtime.Types.Result.GetResult<Prisma.$TrackFilePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one TrackFile that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {TrackFileFindUniqueOrThrowArgs} args - Arguments to find a TrackFile
   * @example
   * // Get one TrackFile
   * const trackFile = await prisma.trackFile.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends TrackFileFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, TrackFileFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__TrackFileClient<runtime.Types.Result.GetResult<Prisma.$TrackFilePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first TrackFile that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackFileFindFirstArgs} args - Arguments to find a TrackFile
   * @example
   * // Get one TrackFile
   * const trackFile = await prisma.trackFile.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends TrackFileFindFirstArgs>(args?: Prisma.SelectSubset<T, TrackFileFindFirstArgs<ExtArgs>>): Prisma.Prisma__TrackFileClient<runtime.Types.Result.GetResult<Prisma.$TrackFilePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first TrackFile that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackFileFindFirstOrThrowArgs} args - Arguments to find a TrackFile
   * @example
   * // Get one TrackFile
   * const trackFile = await prisma.trackFile.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends TrackFileFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, TrackFileFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__TrackFileClient<runtime.Types.Result.GetResult<Prisma.$TrackFilePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more TrackFiles that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackFileFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all TrackFiles
   * const trackFiles = await prisma.trackFile.findMany()
   * 
   * // Get first 10 TrackFiles
   * const trackFiles = await prisma.trackFile.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const trackFileWithIdOnly = await prisma.trackFile.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends TrackFileFindManyArgs>(args?: Prisma.SelectSubset<T, TrackFileFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TrackFilePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a TrackFile.
   * @param {TrackFileCreateArgs} args - Arguments to create a TrackFile.
   * @example
   * // Create one TrackFile
   * const TrackFile = await prisma.trackFile.create({
   *   data: {
   *     // ... data to create a TrackFile
   *   }
   * })
   * 
   */
  create<T extends TrackFileCreateArgs>(args: Prisma.SelectSubset<T, TrackFileCreateArgs<ExtArgs>>): Prisma.Prisma__TrackFileClient<runtime.Types.Result.GetResult<Prisma.$TrackFilePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many TrackFiles.
   * @param {TrackFileCreateManyArgs} args - Arguments to create many TrackFiles.
   * @example
   * // Create many TrackFiles
   * const trackFile = await prisma.trackFile.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends TrackFileCreateManyArgs>(args?: Prisma.SelectSubset<T, TrackFileCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many TrackFiles and returns the data saved in the database.
   * @param {TrackFileCreateManyAndReturnArgs} args - Arguments to create many TrackFiles.
   * @example
   * // Create many TrackFiles
   * const trackFile = await prisma.trackFile.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many TrackFiles and only return the `id`
   * const trackFileWithIdOnly = await prisma.trackFile.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends TrackFileCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, TrackFileCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TrackFilePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a TrackFile.
   * @param {TrackFileDeleteArgs} args - Arguments to delete one TrackFile.
   * @example
   * // Delete one TrackFile
   * const TrackFile = await prisma.trackFile.delete({
   *   where: {
   *     // ... filter to delete one TrackFile
   *   }
   * })
   * 
   */
  delete<T extends TrackFileDeleteArgs>(args: Prisma.SelectSubset<T, TrackFileDeleteArgs<ExtArgs>>): Prisma.Prisma__TrackFileClient<runtime.Types.Result.GetResult<Prisma.$TrackFilePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one TrackFile.
   * @param {TrackFileUpdateArgs} args - Arguments to update one TrackFile.
   * @example
   * // Update one TrackFile
   * const trackFile = await prisma.trackFile.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends TrackFileUpdateArgs>(args: Prisma.SelectSubset<T, TrackFileUpdateArgs<ExtArgs>>): Prisma.Prisma__TrackFileClient<runtime.Types.Result.GetResult<Prisma.$TrackFilePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more TrackFiles.
   * @param {TrackFileDeleteManyArgs} args - Arguments to filter TrackFiles to delete.
   * @example
   * // Delete a few TrackFiles
   * const { count } = await prisma.trackFile.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends TrackFileDeleteManyArgs>(args?: Prisma.SelectSubset<T, TrackFileDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more TrackFiles.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackFileUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many TrackFiles
   * const trackFile = await prisma.trackFile.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends TrackFileUpdateManyArgs>(args: Prisma.SelectSubset<T, TrackFileUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more TrackFiles and returns the data updated in the database.
   * @param {TrackFileUpdateManyAndReturnArgs} args - Arguments to update many TrackFiles.
   * @example
   * // Update many TrackFiles
   * const trackFile = await prisma.trackFile.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more TrackFiles and only return the `id`
   * const trackFileWithIdOnly = await prisma.trackFile.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends TrackFileUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, TrackFileUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TrackFilePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one TrackFile.
   * @param {TrackFileUpsertArgs} args - Arguments to update or create a TrackFile.
   * @example
   * // Update or create a TrackFile
   * const trackFile = await prisma.trackFile.upsert({
   *   create: {
   *     // ... data to create a TrackFile
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the TrackFile we want to update
   *   }
   * })
   */
  upsert<T extends TrackFileUpsertArgs>(args: Prisma.SelectSubset<T, TrackFileUpsertArgs<ExtArgs>>): Prisma.Prisma__TrackFileClient<runtime.Types.Result.GetResult<Prisma.$TrackFilePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of TrackFiles.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackFileCountArgs} args - Arguments to filter TrackFiles to count.
   * @example
   * // Count the number of TrackFiles
   * const count = await prisma.trackFile.count({
   *   where: {
   *     // ... the filter for the TrackFiles we want to count
   *   }
   * })
  **/
  count<T extends TrackFileCountArgs>(
    args?: Prisma.Subset<T, TrackFileCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], TrackFileCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a TrackFile.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackFileAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends TrackFileAggregateArgs>(args: Prisma.Subset<T, TrackFileAggregateArgs>): Prisma.PrismaPromise<GetTrackFileAggregateType<T>>

  /**
   * Group by TrackFile.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackFileGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends TrackFileGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: TrackFileGroupByArgs['orderBy'] }
      : { orderBy?: TrackFileGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, TrackFileGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetTrackFileGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the TrackFile model
 */
readonly fields: TrackFileFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for TrackFile.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__TrackFileClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  track<T extends Prisma.TrackDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.TrackDefaultArgs<ExtArgs>>): Prisma.Prisma__TrackClient<runtime.Types.Result.GetResult<Prisma.$TrackPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the TrackFile model
 */
export interface TrackFileFieldRefs {
  readonly id: Prisma.FieldRef<"TrackFile", 'String'>
  readonly trackId: Prisma.FieldRef<"TrackFile", 'String'>
  readonly fileUrl: Prisma.FieldRef<"TrackFile", 'String'>
  readonly fileKey: Prisma.FieldRef<"TrackFile", 'String'>
  readonly fileName: Prisma.FieldRef<"TrackFile", 'String'>
  readonly fileSize: Prisma.FieldRef<"TrackFile", 'Int'>
  readonly mimeType: Prisma.FieldRef<"TrackFile", 'String'>
  readonly duration: Prisma.FieldRef<"TrackFile", 'Int'>
  readonly createdAt: Prisma.FieldRef<"TrackFile", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"TrackFile", 'DateTime'>
}
    

// Custom InputTypes
/**
 * TrackFile findUnique
 */
export type TrackFileFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackFile
   */
  select?: Prisma.TrackFileSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackFile
   */
  omit?: Prisma.TrackFileOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackFileInclude<ExtArgs> | null
  /**
   * Filter, which TrackFile to fetch.
   */
  where: Prisma.TrackFileWhereUniqueInput
}

/**
 * TrackFile findUniqueOrThrow
 */
export type TrackFileFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackFile
   */
  select?: Prisma.TrackFileSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackFile
   */
  omit?: Prisma.TrackFileOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackFileInclude<ExtArgs> | null
  /**
   * Filter, which TrackFile to fetch.
   */
  where: Prisma.TrackFileWhereUniqueInput
}

/**
 * TrackFile findFirst
 */
export type TrackFileFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackFile
   */
  select?: Prisma.TrackFileSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackFile
   */
  omit?: Prisma.TrackFileOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackFileInclude<ExtArgs> | null
  /**
   * Filter, which TrackFile to fetch.
   */
  where?: Prisma.TrackFileWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of TrackFiles to fetch.
   */
  orderBy?: Prisma.TrackFileOrderByWithRelationInput | Prisma.TrackFileOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for TrackFiles.
   */
  cursor?: Prisma.TrackFileWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` TrackFiles from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` TrackFiles.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of TrackFiles.
   */
  distinct?: Prisma.TrackFileScalarFieldEnum | Prisma.TrackFileScalarFieldEnum[]
}

/**
 * TrackFile findFirstOrThrow
 */
export type TrackFileFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackFile
   */
  select?: Prisma.TrackFileSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackFile
   */
  omit?: Prisma.TrackFileOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackFileInclude<ExtArgs> | null
  /**
   * Filter, which TrackFile to fetch.
   */
  where?: Prisma.TrackFileWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of TrackFiles to fetch.
   */
  orderBy?: Prisma.TrackFileOrderByWithRelationInput | Prisma.TrackFileOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for TrackFiles.
   */
  cursor?: Prisma.TrackFileWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` TrackFiles from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` TrackFiles.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of TrackFiles.
   */
  distinct?: Prisma.TrackFileScalarFieldEnum | Prisma.TrackFileScalarFieldEnum[]
}

/**
 * TrackFile findMany
 */
export type TrackFileFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackFile
   */
  select?: Prisma.TrackFileSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackFile
   */
  omit?: Prisma.TrackFileOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackFileInclude<ExtArgs> | null
  /**
   * Filter, which TrackFiles to fetch.
   */
  where?: Prisma.TrackFileWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of TrackFiles to fetch.
   */
  orderBy?: Prisma.TrackFileOrderByWithRelationInput | Prisma.TrackFileOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing TrackFiles.
   */
  cursor?: Prisma.TrackFileWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` TrackFiles from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` TrackFiles.
   */
  skip?: number
  distinct?: Prisma.TrackFileScalarFieldEnum | Prisma.TrackFileScalarFieldEnum[]
}

/**
 * TrackFile create
 */
export type TrackFileCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackFile
   */
  select?: Prisma.TrackFileSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackFile
   */
  omit?: Prisma.TrackFileOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackFileInclude<ExtArgs> | null
  /**
   * The data needed to create a TrackFile.
   */
  data: Prisma.XOR<Prisma.TrackFileCreateInput, Prisma.TrackFileUncheckedCreateInput>
}

/**
 * TrackFile createMany
 */
export type TrackFileCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many TrackFiles.
   */
  data: Prisma.TrackFileCreateManyInput | Prisma.TrackFileCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * TrackFile createManyAndReturn
 */
export type TrackFileCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackFile
   */
  select?: Prisma.TrackFileSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the TrackFile
   */
  omit?: Prisma.TrackFileOmit<ExtArgs> | null
  /**
   * The data used to create many TrackFiles.
   */
  data: Prisma.TrackFileCreateManyInput | Prisma.TrackFileCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackFileIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * TrackFile update
 */
export type TrackFileUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackFile
   */
  select?: Prisma.TrackFileSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackFile
   */
  omit?: Prisma.TrackFileOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackFileInclude<ExtArgs> | null
  /**
   * The data needed to update a TrackFile.
   */
  data: Prisma.XOR<Prisma.TrackFileUpdateInput, Prisma.TrackFileUncheckedUpdateInput>
  /**
   * Choose, which TrackFile to update.
   */
  where: Prisma.TrackFileWhereUniqueInput
}

/**
 * TrackFile updateMany
 */
export type TrackFileUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update TrackFiles.
   */
  data: Prisma.XOR<Prisma.TrackFileUpdateManyMutationInput, Prisma.TrackFileUncheckedUpdateManyInput>
  /**
   * Filter which TrackFiles to update
   */
  where?: Prisma.TrackFileWhereInput
  /**
   * Limit how many TrackFiles to update.
   */
  limit?: number
}

/**
 * TrackFile updateManyAndReturn
 */
export type TrackFileUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackFile
   */
  select?: Prisma.TrackFileSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the TrackFile
   */
  omit?: Prisma.TrackFileOmit<ExtArgs> | null
  /**
   * The data used to update TrackFiles.
   */
  data: Prisma.XOR<Prisma.TrackFileUpdateManyMutationInput, Prisma.TrackFileUncheckedUpdateManyInput>
  /**
   * Filter which TrackFiles to update
   */
  where?: Prisma.TrackFileWhereInput
  /**
   * Limit how many TrackFiles to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackFileIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * TrackFile upsert
 */
export type TrackFileUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackFile
   */
  select?: Prisma.TrackFileSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackFile
   */
  omit?: Prisma.TrackFileOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackFileInclude<ExtArgs> | null
  /**
   * The filter to search for the TrackFile to update in case it exists.
   */
  where: Prisma.TrackFileWhereUniqueInput
  /**
   * In case the TrackFile found by the `where` argument doesn't exist, create a new TrackFile with this data.
   */
  create: Prisma.XOR<Prisma.TrackFileCreateInput, Prisma.TrackFileUncheckedCreateInput>
  /**
   * In case the TrackFile was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.TrackFileUpdateInput, Prisma.TrackFileUncheckedUpdateInput>
}

/**
 * TrackFile delete
 */
export type TrackFileDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackFile
   */
  select?: Prisma.TrackFileSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackFile
   */
  omit?: Prisma.TrackFileOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackFileInclude<ExtArgs> | null
  /**
   * Filter which TrackFile to delete.
   */
  where: Prisma.TrackFileWhereUniqueInput
}

/**
 * TrackFile deleteMany
 */
export type TrackFileDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which TrackFiles to delete
   */
  where?: Prisma.TrackFileWhereInput
  /**
   * Limit how many TrackFiles to delete.
   */
  limit?: number
}

/**
 * TrackFile without action
 */
export type TrackFileDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackFile
   */
  select?: Prisma.TrackFileSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackFile
   */
  omit?: Prisma.TrackFileOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackFileInclude<ExtArgs> | null
}
