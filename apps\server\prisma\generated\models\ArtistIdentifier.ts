
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `ArtistIdentifier` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model ArtistIdentifier
 * 
 */
export type ArtistIdentifierModel = runtime.Types.Result.DefaultSelection<Prisma.$ArtistIdentifierPayload>

export type AggregateArtistIdentifier = {
  _count: ArtistIdentifierCountAggregateOutputType | null
  _min: ArtistIdentifierMinAggregateOutputType | null
  _max: ArtistIdentifierMaxAggregateOutputType | null
}

export type ArtistIdentifierMinAggregateOutputType = {
  artistId: string | null
  service: $Enums.Service | null
  identifier: string | null
}

export type ArtistIdentifierMaxAggregateOutputType = {
  artistId: string | null
  service: $Enums.Service | null
  identifier: string | null
}

export type ArtistIdentifierCountAggregateOutputType = {
  artistId: number
  service: number
  identifier: number
  _all: number
}


export type ArtistIdentifierMinAggregateInputType = {
  artistId?: true
  service?: true
  identifier?: true
}

export type ArtistIdentifierMaxAggregateInputType = {
  artistId?: true
  service?: true
  identifier?: true
}

export type ArtistIdentifierCountAggregateInputType = {
  artistId?: true
  service?: true
  identifier?: true
  _all?: true
}

export type ArtistIdentifierAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which ArtistIdentifier to aggregate.
   */
  where?: Prisma.ArtistIdentifierWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ArtistIdentifiers to fetch.
   */
  orderBy?: Prisma.ArtistIdentifierOrderByWithRelationInput | Prisma.ArtistIdentifierOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.ArtistIdentifierWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ArtistIdentifiers from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ArtistIdentifiers.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned ArtistIdentifiers
  **/
  _count?: true | ArtistIdentifierCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: ArtistIdentifierMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: ArtistIdentifierMaxAggregateInputType
}

export type GetArtistIdentifierAggregateType<T extends ArtistIdentifierAggregateArgs> = {
      [P in keyof T & keyof AggregateArtistIdentifier]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateArtistIdentifier[P]>
    : Prisma.GetScalarType<T[P], AggregateArtistIdentifier[P]>
}




export type ArtistIdentifierGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ArtistIdentifierWhereInput
  orderBy?: Prisma.ArtistIdentifierOrderByWithAggregationInput | Prisma.ArtistIdentifierOrderByWithAggregationInput[]
  by: Prisma.ArtistIdentifierScalarFieldEnum[] | Prisma.ArtistIdentifierScalarFieldEnum
  having?: Prisma.ArtistIdentifierScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: ArtistIdentifierCountAggregateInputType | true
  _min?: ArtistIdentifierMinAggregateInputType
  _max?: ArtistIdentifierMaxAggregateInputType
}

export type ArtistIdentifierGroupByOutputType = {
  artistId: string
  service: $Enums.Service
  identifier: string
  _count: ArtistIdentifierCountAggregateOutputType | null
  _min: ArtistIdentifierMinAggregateOutputType | null
  _max: ArtistIdentifierMaxAggregateOutputType | null
}

type GetArtistIdentifierGroupByPayload<T extends ArtistIdentifierGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<ArtistIdentifierGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof ArtistIdentifierGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], ArtistIdentifierGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], ArtistIdentifierGroupByOutputType[P]>
      }
    >
  > 



export type ArtistIdentifierWhereInput = {
  AND?: Prisma.ArtistIdentifierWhereInput | Prisma.ArtistIdentifierWhereInput[]
  OR?: Prisma.ArtistIdentifierWhereInput[]
  NOT?: Prisma.ArtistIdentifierWhereInput | Prisma.ArtistIdentifierWhereInput[]
  artistId?: Prisma.StringFilter<"ArtistIdentifier"> | string
  service?: Prisma.EnumServiceFilter<"ArtistIdentifier"> | $Enums.Service
  identifier?: Prisma.StringFilter<"ArtistIdentifier"> | string
  artist?: Prisma.XOR<Prisma.ArtistScalarRelationFilter, Prisma.ArtistWhereInput>
}

export type ArtistIdentifierOrderByWithRelationInput = {
  artistId?: Prisma.SortOrder
  service?: Prisma.SortOrder
  identifier?: Prisma.SortOrder
  artist?: Prisma.ArtistOrderByWithRelationInput
}

export type ArtistIdentifierWhereUniqueInput = Prisma.AtLeast<{
  identifier_service?: Prisma.ArtistIdentifierIdentifierServiceCompoundUniqueInput
  artistId_service?: Prisma.ArtistIdentifierArtistIdServiceCompoundUniqueInput
  AND?: Prisma.ArtistIdentifierWhereInput | Prisma.ArtistIdentifierWhereInput[]
  OR?: Prisma.ArtistIdentifierWhereInput[]
  NOT?: Prisma.ArtistIdentifierWhereInput | Prisma.ArtistIdentifierWhereInput[]
  artistId?: Prisma.StringFilter<"ArtistIdentifier"> | string
  service?: Prisma.EnumServiceFilter<"ArtistIdentifier"> | $Enums.Service
  identifier?: Prisma.StringFilter<"ArtistIdentifier"> | string
  artist?: Prisma.XOR<Prisma.ArtistScalarRelationFilter, Prisma.ArtistWhereInput>
}, "artistId_service" | "identifier_service">

export type ArtistIdentifierOrderByWithAggregationInput = {
  artistId?: Prisma.SortOrder
  service?: Prisma.SortOrder
  identifier?: Prisma.SortOrder
  _count?: Prisma.ArtistIdentifierCountOrderByAggregateInput
  _max?: Prisma.ArtistIdentifierMaxOrderByAggregateInput
  _min?: Prisma.ArtistIdentifierMinOrderByAggregateInput
}

export type ArtistIdentifierScalarWhereWithAggregatesInput = {
  AND?: Prisma.ArtistIdentifierScalarWhereWithAggregatesInput | Prisma.ArtistIdentifierScalarWhereWithAggregatesInput[]
  OR?: Prisma.ArtistIdentifierScalarWhereWithAggregatesInput[]
  NOT?: Prisma.ArtistIdentifierScalarWhereWithAggregatesInput | Prisma.ArtistIdentifierScalarWhereWithAggregatesInput[]
  artistId?: Prisma.StringWithAggregatesFilter<"ArtistIdentifier"> | string
  service?: Prisma.EnumServiceWithAggregatesFilter<"ArtistIdentifier"> | $Enums.Service
  identifier?: Prisma.StringWithAggregatesFilter<"ArtistIdentifier"> | string
}

export type ArtistIdentifierCreateInput = {
  service: $Enums.Service
  identifier: string
  artist: Prisma.ArtistCreateNestedOneWithoutIdentifiersInput
}

export type ArtistIdentifierUncheckedCreateInput = {
  artistId: string
  service: $Enums.Service
  identifier: string
}

export type ArtistIdentifierUpdateInput = {
  service?: Prisma.EnumServiceFieldUpdateOperationsInput | $Enums.Service
  identifier?: Prisma.StringFieldUpdateOperationsInput | string
  artist?: Prisma.ArtistUpdateOneRequiredWithoutIdentifiersNestedInput
}

export type ArtistIdentifierUncheckedUpdateInput = {
  artistId?: Prisma.StringFieldUpdateOperationsInput | string
  service?: Prisma.EnumServiceFieldUpdateOperationsInput | $Enums.Service
  identifier?: Prisma.StringFieldUpdateOperationsInput | string
}

export type ArtistIdentifierCreateManyInput = {
  artistId: string
  service: $Enums.Service
  identifier: string
}

export type ArtistIdentifierUpdateManyMutationInput = {
  service?: Prisma.EnumServiceFieldUpdateOperationsInput | $Enums.Service
  identifier?: Prisma.StringFieldUpdateOperationsInput | string
}

export type ArtistIdentifierUncheckedUpdateManyInput = {
  artistId?: Prisma.StringFieldUpdateOperationsInput | string
  service?: Prisma.EnumServiceFieldUpdateOperationsInput | $Enums.Service
  identifier?: Prisma.StringFieldUpdateOperationsInput | string
}

export type ArtistIdentifierListRelationFilter = {
  every?: Prisma.ArtistIdentifierWhereInput
  some?: Prisma.ArtistIdentifierWhereInput
  none?: Prisma.ArtistIdentifierWhereInput
}

export type ArtistIdentifierOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type ArtistIdentifierIdentifierServiceCompoundUniqueInput = {
  identifier: string
  service: $Enums.Service
}

export type ArtistIdentifierArtistIdServiceCompoundUniqueInput = {
  artistId: string
  service: $Enums.Service
}

export type ArtistIdentifierCountOrderByAggregateInput = {
  artistId?: Prisma.SortOrder
  service?: Prisma.SortOrder
  identifier?: Prisma.SortOrder
}

export type ArtistIdentifierMaxOrderByAggregateInput = {
  artistId?: Prisma.SortOrder
  service?: Prisma.SortOrder
  identifier?: Prisma.SortOrder
}

export type ArtistIdentifierMinOrderByAggregateInput = {
  artistId?: Prisma.SortOrder
  service?: Prisma.SortOrder
  identifier?: Prisma.SortOrder
}

export type ArtistIdentifierCreateNestedManyWithoutArtistInput = {
  create?: Prisma.XOR<Prisma.ArtistIdentifierCreateWithoutArtistInput, Prisma.ArtistIdentifierUncheckedCreateWithoutArtistInput> | Prisma.ArtistIdentifierCreateWithoutArtistInput[] | Prisma.ArtistIdentifierUncheckedCreateWithoutArtistInput[]
  connectOrCreate?: Prisma.ArtistIdentifierCreateOrConnectWithoutArtistInput | Prisma.ArtistIdentifierCreateOrConnectWithoutArtistInput[]
  createMany?: Prisma.ArtistIdentifierCreateManyArtistInputEnvelope
  connect?: Prisma.ArtistIdentifierWhereUniqueInput | Prisma.ArtistIdentifierWhereUniqueInput[]
}

export type ArtistIdentifierUncheckedCreateNestedManyWithoutArtistInput = {
  create?: Prisma.XOR<Prisma.ArtistIdentifierCreateWithoutArtistInput, Prisma.ArtistIdentifierUncheckedCreateWithoutArtistInput> | Prisma.ArtistIdentifierCreateWithoutArtistInput[] | Prisma.ArtistIdentifierUncheckedCreateWithoutArtistInput[]
  connectOrCreate?: Prisma.ArtistIdentifierCreateOrConnectWithoutArtistInput | Prisma.ArtistIdentifierCreateOrConnectWithoutArtistInput[]
  createMany?: Prisma.ArtistIdentifierCreateManyArtistInputEnvelope
  connect?: Prisma.ArtistIdentifierWhereUniqueInput | Prisma.ArtistIdentifierWhereUniqueInput[]
}

export type ArtistIdentifierUpdateManyWithoutArtistNestedInput = {
  create?: Prisma.XOR<Prisma.ArtistIdentifierCreateWithoutArtistInput, Prisma.ArtistIdentifierUncheckedCreateWithoutArtistInput> | Prisma.ArtistIdentifierCreateWithoutArtistInput[] | Prisma.ArtistIdentifierUncheckedCreateWithoutArtistInput[]
  connectOrCreate?: Prisma.ArtistIdentifierCreateOrConnectWithoutArtistInput | Prisma.ArtistIdentifierCreateOrConnectWithoutArtistInput[]
  upsert?: Prisma.ArtistIdentifierUpsertWithWhereUniqueWithoutArtistInput | Prisma.ArtistIdentifierUpsertWithWhereUniqueWithoutArtistInput[]
  createMany?: Prisma.ArtistIdentifierCreateManyArtistInputEnvelope
  set?: Prisma.ArtistIdentifierWhereUniqueInput | Prisma.ArtistIdentifierWhereUniqueInput[]
  disconnect?: Prisma.ArtistIdentifierWhereUniqueInput | Prisma.ArtistIdentifierWhereUniqueInput[]
  delete?: Prisma.ArtistIdentifierWhereUniqueInput | Prisma.ArtistIdentifierWhereUniqueInput[]
  connect?: Prisma.ArtistIdentifierWhereUniqueInput | Prisma.ArtistIdentifierWhereUniqueInput[]
  update?: Prisma.ArtistIdentifierUpdateWithWhereUniqueWithoutArtistInput | Prisma.ArtistIdentifierUpdateWithWhereUniqueWithoutArtistInput[]
  updateMany?: Prisma.ArtistIdentifierUpdateManyWithWhereWithoutArtistInput | Prisma.ArtistIdentifierUpdateManyWithWhereWithoutArtistInput[]
  deleteMany?: Prisma.ArtistIdentifierScalarWhereInput | Prisma.ArtistIdentifierScalarWhereInput[]
}

export type ArtistIdentifierUncheckedUpdateManyWithoutArtistNestedInput = {
  create?: Prisma.XOR<Prisma.ArtistIdentifierCreateWithoutArtistInput, Prisma.ArtistIdentifierUncheckedCreateWithoutArtistInput> | Prisma.ArtistIdentifierCreateWithoutArtistInput[] | Prisma.ArtistIdentifierUncheckedCreateWithoutArtistInput[]
  connectOrCreate?: Prisma.ArtistIdentifierCreateOrConnectWithoutArtistInput | Prisma.ArtistIdentifierCreateOrConnectWithoutArtistInput[]
  upsert?: Prisma.ArtistIdentifierUpsertWithWhereUniqueWithoutArtistInput | Prisma.ArtistIdentifierUpsertWithWhereUniqueWithoutArtistInput[]
  createMany?: Prisma.ArtistIdentifierCreateManyArtistInputEnvelope
  set?: Prisma.ArtistIdentifierWhereUniqueInput | Prisma.ArtistIdentifierWhereUniqueInput[]
  disconnect?: Prisma.ArtistIdentifierWhereUniqueInput | Prisma.ArtistIdentifierWhereUniqueInput[]
  delete?: Prisma.ArtistIdentifierWhereUniqueInput | Prisma.ArtistIdentifierWhereUniqueInput[]
  connect?: Prisma.ArtistIdentifierWhereUniqueInput | Prisma.ArtistIdentifierWhereUniqueInput[]
  update?: Prisma.ArtistIdentifierUpdateWithWhereUniqueWithoutArtistInput | Prisma.ArtistIdentifierUpdateWithWhereUniqueWithoutArtistInput[]
  updateMany?: Prisma.ArtistIdentifierUpdateManyWithWhereWithoutArtistInput | Prisma.ArtistIdentifierUpdateManyWithWhereWithoutArtistInput[]
  deleteMany?: Prisma.ArtistIdentifierScalarWhereInput | Prisma.ArtistIdentifierScalarWhereInput[]
}

export type EnumServiceFieldUpdateOperationsInput = {
  set?: $Enums.Service
}

export type ArtistIdentifierCreateWithoutArtistInput = {
  service: $Enums.Service
  identifier: string
}

export type ArtistIdentifierUncheckedCreateWithoutArtistInput = {
  service: $Enums.Service
  identifier: string
}

export type ArtistIdentifierCreateOrConnectWithoutArtistInput = {
  where: Prisma.ArtistIdentifierWhereUniqueInput
  create: Prisma.XOR<Prisma.ArtistIdentifierCreateWithoutArtistInput, Prisma.ArtistIdentifierUncheckedCreateWithoutArtistInput>
}

export type ArtistIdentifierCreateManyArtistInputEnvelope = {
  data: Prisma.ArtistIdentifierCreateManyArtistInput | Prisma.ArtistIdentifierCreateManyArtistInput[]
  skipDuplicates?: boolean
}

export type ArtistIdentifierUpsertWithWhereUniqueWithoutArtistInput = {
  where: Prisma.ArtistIdentifierWhereUniqueInput
  update: Prisma.XOR<Prisma.ArtistIdentifierUpdateWithoutArtistInput, Prisma.ArtistIdentifierUncheckedUpdateWithoutArtistInput>
  create: Prisma.XOR<Prisma.ArtistIdentifierCreateWithoutArtistInput, Prisma.ArtistIdentifierUncheckedCreateWithoutArtistInput>
}

export type ArtistIdentifierUpdateWithWhereUniqueWithoutArtistInput = {
  where: Prisma.ArtistIdentifierWhereUniqueInput
  data: Prisma.XOR<Prisma.ArtistIdentifierUpdateWithoutArtistInput, Prisma.ArtistIdentifierUncheckedUpdateWithoutArtistInput>
}

export type ArtistIdentifierUpdateManyWithWhereWithoutArtistInput = {
  where: Prisma.ArtistIdentifierScalarWhereInput
  data: Prisma.XOR<Prisma.ArtistIdentifierUpdateManyMutationInput, Prisma.ArtistIdentifierUncheckedUpdateManyWithoutArtistInput>
}

export type ArtistIdentifierScalarWhereInput = {
  AND?: Prisma.ArtistIdentifierScalarWhereInput | Prisma.ArtistIdentifierScalarWhereInput[]
  OR?: Prisma.ArtistIdentifierScalarWhereInput[]
  NOT?: Prisma.ArtistIdentifierScalarWhereInput | Prisma.ArtistIdentifierScalarWhereInput[]
  artistId?: Prisma.StringFilter<"ArtistIdentifier"> | string
  service?: Prisma.EnumServiceFilter<"ArtistIdentifier"> | $Enums.Service
  identifier?: Prisma.StringFilter<"ArtistIdentifier"> | string
}

export type ArtistIdentifierCreateManyArtistInput = {
  service: $Enums.Service
  identifier: string
}

export type ArtistIdentifierUpdateWithoutArtistInput = {
  service?: Prisma.EnumServiceFieldUpdateOperationsInput | $Enums.Service
  identifier?: Prisma.StringFieldUpdateOperationsInput | string
}

export type ArtistIdentifierUncheckedUpdateWithoutArtistInput = {
  service?: Prisma.EnumServiceFieldUpdateOperationsInput | $Enums.Service
  identifier?: Prisma.StringFieldUpdateOperationsInput | string
}

export type ArtistIdentifierUncheckedUpdateManyWithoutArtistInput = {
  service?: Prisma.EnumServiceFieldUpdateOperationsInput | $Enums.Service
  identifier?: Prisma.StringFieldUpdateOperationsInput | string
}



export type ArtistIdentifierSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  artistId?: boolean
  service?: boolean
  identifier?: boolean
  artist?: boolean | Prisma.ArtistDefaultArgs<ExtArgs>
}, ExtArgs["result"]["artistIdentifier"]>

export type ArtistIdentifierSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  artistId?: boolean
  service?: boolean
  identifier?: boolean
  artist?: boolean | Prisma.ArtistDefaultArgs<ExtArgs>
}, ExtArgs["result"]["artistIdentifier"]>

export type ArtistIdentifierSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  artistId?: boolean
  service?: boolean
  identifier?: boolean
  artist?: boolean | Prisma.ArtistDefaultArgs<ExtArgs>
}, ExtArgs["result"]["artistIdentifier"]>

export type ArtistIdentifierSelectScalar = {
  artistId?: boolean
  service?: boolean
  identifier?: boolean
}

export type ArtistIdentifierOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"artistId" | "service" | "identifier", ExtArgs["result"]["artistIdentifier"]>
export type ArtistIdentifierInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  artist?: boolean | Prisma.ArtistDefaultArgs<ExtArgs>
}
export type ArtistIdentifierIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  artist?: boolean | Prisma.ArtistDefaultArgs<ExtArgs>
}
export type ArtistIdentifierIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  artist?: boolean | Prisma.ArtistDefaultArgs<ExtArgs>
}

export type $ArtistIdentifierPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "ArtistIdentifier"
  objects: {
    artist: Prisma.$ArtistPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    artistId: string
    service: $Enums.Service
    identifier: string
  }, ExtArgs["result"]["artistIdentifier"]>
  composites: {}
}

export type ArtistIdentifierGetPayload<S extends boolean | null | undefined | ArtistIdentifierDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$ArtistIdentifierPayload, S>

export type ArtistIdentifierCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<ArtistIdentifierFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: ArtistIdentifierCountAggregateInputType | true
  }

export interface ArtistIdentifierDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['ArtistIdentifier'], meta: { name: 'ArtistIdentifier' } }
  /**
   * Find zero or one ArtistIdentifier that matches the filter.
   * @param {ArtistIdentifierFindUniqueArgs} args - Arguments to find a ArtistIdentifier
   * @example
   * // Get one ArtistIdentifier
   * const artistIdentifier = await prisma.artistIdentifier.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends ArtistIdentifierFindUniqueArgs>(args: Prisma.SelectSubset<T, ArtistIdentifierFindUniqueArgs<ExtArgs>>): Prisma.Prisma__ArtistIdentifierClient<runtime.Types.Result.GetResult<Prisma.$ArtistIdentifierPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one ArtistIdentifier that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {ArtistIdentifierFindUniqueOrThrowArgs} args - Arguments to find a ArtistIdentifier
   * @example
   * // Get one ArtistIdentifier
   * const artistIdentifier = await prisma.artistIdentifier.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends ArtistIdentifierFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, ArtistIdentifierFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__ArtistIdentifierClient<runtime.Types.Result.GetResult<Prisma.$ArtistIdentifierPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first ArtistIdentifier that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ArtistIdentifierFindFirstArgs} args - Arguments to find a ArtistIdentifier
   * @example
   * // Get one ArtistIdentifier
   * const artistIdentifier = await prisma.artistIdentifier.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends ArtistIdentifierFindFirstArgs>(args?: Prisma.SelectSubset<T, ArtistIdentifierFindFirstArgs<ExtArgs>>): Prisma.Prisma__ArtistIdentifierClient<runtime.Types.Result.GetResult<Prisma.$ArtistIdentifierPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first ArtistIdentifier that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ArtistIdentifierFindFirstOrThrowArgs} args - Arguments to find a ArtistIdentifier
   * @example
   * // Get one ArtistIdentifier
   * const artistIdentifier = await prisma.artistIdentifier.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends ArtistIdentifierFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, ArtistIdentifierFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__ArtistIdentifierClient<runtime.Types.Result.GetResult<Prisma.$ArtistIdentifierPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more ArtistIdentifiers that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ArtistIdentifierFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all ArtistIdentifiers
   * const artistIdentifiers = await prisma.artistIdentifier.findMany()
   * 
   * // Get first 10 ArtistIdentifiers
   * const artistIdentifiers = await prisma.artistIdentifier.findMany({ take: 10 })
   * 
   * // Only select the `artistId`
   * const artistIdentifierWithArtistIdOnly = await prisma.artistIdentifier.findMany({ select: { artistId: true } })
   * 
   */
  findMany<T extends ArtistIdentifierFindManyArgs>(args?: Prisma.SelectSubset<T, ArtistIdentifierFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ArtistIdentifierPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a ArtistIdentifier.
   * @param {ArtistIdentifierCreateArgs} args - Arguments to create a ArtistIdentifier.
   * @example
   * // Create one ArtistIdentifier
   * const ArtistIdentifier = await prisma.artistIdentifier.create({
   *   data: {
   *     // ... data to create a ArtistIdentifier
   *   }
   * })
   * 
   */
  create<T extends ArtistIdentifierCreateArgs>(args: Prisma.SelectSubset<T, ArtistIdentifierCreateArgs<ExtArgs>>): Prisma.Prisma__ArtistIdentifierClient<runtime.Types.Result.GetResult<Prisma.$ArtistIdentifierPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many ArtistIdentifiers.
   * @param {ArtistIdentifierCreateManyArgs} args - Arguments to create many ArtistIdentifiers.
   * @example
   * // Create many ArtistIdentifiers
   * const artistIdentifier = await prisma.artistIdentifier.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends ArtistIdentifierCreateManyArgs>(args?: Prisma.SelectSubset<T, ArtistIdentifierCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many ArtistIdentifiers and returns the data saved in the database.
   * @param {ArtistIdentifierCreateManyAndReturnArgs} args - Arguments to create many ArtistIdentifiers.
   * @example
   * // Create many ArtistIdentifiers
   * const artistIdentifier = await prisma.artistIdentifier.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many ArtistIdentifiers and only return the `artistId`
   * const artistIdentifierWithArtistIdOnly = await prisma.artistIdentifier.createManyAndReturn({
   *   select: { artistId: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends ArtistIdentifierCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, ArtistIdentifierCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ArtistIdentifierPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a ArtistIdentifier.
   * @param {ArtistIdentifierDeleteArgs} args - Arguments to delete one ArtistIdentifier.
   * @example
   * // Delete one ArtistIdentifier
   * const ArtistIdentifier = await prisma.artistIdentifier.delete({
   *   where: {
   *     // ... filter to delete one ArtistIdentifier
   *   }
   * })
   * 
   */
  delete<T extends ArtistIdentifierDeleteArgs>(args: Prisma.SelectSubset<T, ArtistIdentifierDeleteArgs<ExtArgs>>): Prisma.Prisma__ArtistIdentifierClient<runtime.Types.Result.GetResult<Prisma.$ArtistIdentifierPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one ArtistIdentifier.
   * @param {ArtistIdentifierUpdateArgs} args - Arguments to update one ArtistIdentifier.
   * @example
   * // Update one ArtistIdentifier
   * const artistIdentifier = await prisma.artistIdentifier.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends ArtistIdentifierUpdateArgs>(args: Prisma.SelectSubset<T, ArtistIdentifierUpdateArgs<ExtArgs>>): Prisma.Prisma__ArtistIdentifierClient<runtime.Types.Result.GetResult<Prisma.$ArtistIdentifierPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more ArtistIdentifiers.
   * @param {ArtistIdentifierDeleteManyArgs} args - Arguments to filter ArtistIdentifiers to delete.
   * @example
   * // Delete a few ArtistIdentifiers
   * const { count } = await prisma.artistIdentifier.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends ArtistIdentifierDeleteManyArgs>(args?: Prisma.SelectSubset<T, ArtistIdentifierDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more ArtistIdentifiers.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ArtistIdentifierUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many ArtistIdentifiers
   * const artistIdentifier = await prisma.artistIdentifier.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends ArtistIdentifierUpdateManyArgs>(args: Prisma.SelectSubset<T, ArtistIdentifierUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more ArtistIdentifiers and returns the data updated in the database.
   * @param {ArtistIdentifierUpdateManyAndReturnArgs} args - Arguments to update many ArtistIdentifiers.
   * @example
   * // Update many ArtistIdentifiers
   * const artistIdentifier = await prisma.artistIdentifier.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more ArtistIdentifiers and only return the `artistId`
   * const artistIdentifierWithArtistIdOnly = await prisma.artistIdentifier.updateManyAndReturn({
   *   select: { artistId: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends ArtistIdentifierUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, ArtistIdentifierUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ArtistIdentifierPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one ArtistIdentifier.
   * @param {ArtistIdentifierUpsertArgs} args - Arguments to update or create a ArtistIdentifier.
   * @example
   * // Update or create a ArtistIdentifier
   * const artistIdentifier = await prisma.artistIdentifier.upsert({
   *   create: {
   *     // ... data to create a ArtistIdentifier
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the ArtistIdentifier we want to update
   *   }
   * })
   */
  upsert<T extends ArtistIdentifierUpsertArgs>(args: Prisma.SelectSubset<T, ArtistIdentifierUpsertArgs<ExtArgs>>): Prisma.Prisma__ArtistIdentifierClient<runtime.Types.Result.GetResult<Prisma.$ArtistIdentifierPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of ArtistIdentifiers.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ArtistIdentifierCountArgs} args - Arguments to filter ArtistIdentifiers to count.
   * @example
   * // Count the number of ArtistIdentifiers
   * const count = await prisma.artistIdentifier.count({
   *   where: {
   *     // ... the filter for the ArtistIdentifiers we want to count
   *   }
   * })
  **/
  count<T extends ArtistIdentifierCountArgs>(
    args?: Prisma.Subset<T, ArtistIdentifierCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], ArtistIdentifierCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a ArtistIdentifier.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ArtistIdentifierAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends ArtistIdentifierAggregateArgs>(args: Prisma.Subset<T, ArtistIdentifierAggregateArgs>): Prisma.PrismaPromise<GetArtistIdentifierAggregateType<T>>

  /**
   * Group by ArtistIdentifier.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ArtistIdentifierGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends ArtistIdentifierGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: ArtistIdentifierGroupByArgs['orderBy'] }
      : { orderBy?: ArtistIdentifierGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, ArtistIdentifierGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetArtistIdentifierGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the ArtistIdentifier model
 */
readonly fields: ArtistIdentifierFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for ArtistIdentifier.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__ArtistIdentifierClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  artist<T extends Prisma.ArtistDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.ArtistDefaultArgs<ExtArgs>>): Prisma.Prisma__ArtistClient<runtime.Types.Result.GetResult<Prisma.$ArtistPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the ArtistIdentifier model
 */
export interface ArtistIdentifierFieldRefs {
  readonly artistId: Prisma.FieldRef<"ArtistIdentifier", 'String'>
  readonly service: Prisma.FieldRef<"ArtistIdentifier", 'Service'>
  readonly identifier: Prisma.FieldRef<"ArtistIdentifier", 'String'>
}
    

// Custom InputTypes
/**
 * ArtistIdentifier findUnique
 */
export type ArtistIdentifierFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ArtistIdentifier
   */
  select?: Prisma.ArtistIdentifierSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ArtistIdentifier
   */
  omit?: Prisma.ArtistIdentifierOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ArtistIdentifierInclude<ExtArgs> | null
  /**
   * Filter, which ArtistIdentifier to fetch.
   */
  where: Prisma.ArtistIdentifierWhereUniqueInput
}

/**
 * ArtistIdentifier findUniqueOrThrow
 */
export type ArtistIdentifierFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ArtistIdentifier
   */
  select?: Prisma.ArtistIdentifierSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ArtistIdentifier
   */
  omit?: Prisma.ArtistIdentifierOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ArtistIdentifierInclude<ExtArgs> | null
  /**
   * Filter, which ArtistIdentifier to fetch.
   */
  where: Prisma.ArtistIdentifierWhereUniqueInput
}

/**
 * ArtistIdentifier findFirst
 */
export type ArtistIdentifierFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ArtistIdentifier
   */
  select?: Prisma.ArtistIdentifierSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ArtistIdentifier
   */
  omit?: Prisma.ArtistIdentifierOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ArtistIdentifierInclude<ExtArgs> | null
  /**
   * Filter, which ArtistIdentifier to fetch.
   */
  where?: Prisma.ArtistIdentifierWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ArtistIdentifiers to fetch.
   */
  orderBy?: Prisma.ArtistIdentifierOrderByWithRelationInput | Prisma.ArtistIdentifierOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for ArtistIdentifiers.
   */
  cursor?: Prisma.ArtistIdentifierWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ArtistIdentifiers from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ArtistIdentifiers.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of ArtistIdentifiers.
   */
  distinct?: Prisma.ArtistIdentifierScalarFieldEnum | Prisma.ArtistIdentifierScalarFieldEnum[]
}

/**
 * ArtistIdentifier findFirstOrThrow
 */
export type ArtistIdentifierFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ArtistIdentifier
   */
  select?: Prisma.ArtistIdentifierSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ArtistIdentifier
   */
  omit?: Prisma.ArtistIdentifierOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ArtistIdentifierInclude<ExtArgs> | null
  /**
   * Filter, which ArtistIdentifier to fetch.
   */
  where?: Prisma.ArtistIdentifierWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ArtistIdentifiers to fetch.
   */
  orderBy?: Prisma.ArtistIdentifierOrderByWithRelationInput | Prisma.ArtistIdentifierOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for ArtistIdentifiers.
   */
  cursor?: Prisma.ArtistIdentifierWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ArtistIdentifiers from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ArtistIdentifiers.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of ArtistIdentifiers.
   */
  distinct?: Prisma.ArtistIdentifierScalarFieldEnum | Prisma.ArtistIdentifierScalarFieldEnum[]
}

/**
 * ArtistIdentifier findMany
 */
export type ArtistIdentifierFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ArtistIdentifier
   */
  select?: Prisma.ArtistIdentifierSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ArtistIdentifier
   */
  omit?: Prisma.ArtistIdentifierOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ArtistIdentifierInclude<ExtArgs> | null
  /**
   * Filter, which ArtistIdentifiers to fetch.
   */
  where?: Prisma.ArtistIdentifierWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ArtistIdentifiers to fetch.
   */
  orderBy?: Prisma.ArtistIdentifierOrderByWithRelationInput | Prisma.ArtistIdentifierOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing ArtistIdentifiers.
   */
  cursor?: Prisma.ArtistIdentifierWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ArtistIdentifiers from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ArtistIdentifiers.
   */
  skip?: number
  distinct?: Prisma.ArtistIdentifierScalarFieldEnum | Prisma.ArtistIdentifierScalarFieldEnum[]
}

/**
 * ArtistIdentifier create
 */
export type ArtistIdentifierCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ArtistIdentifier
   */
  select?: Prisma.ArtistIdentifierSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ArtistIdentifier
   */
  omit?: Prisma.ArtistIdentifierOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ArtistIdentifierInclude<ExtArgs> | null
  /**
   * The data needed to create a ArtistIdentifier.
   */
  data: Prisma.XOR<Prisma.ArtistIdentifierCreateInput, Prisma.ArtistIdentifierUncheckedCreateInput>
}

/**
 * ArtistIdentifier createMany
 */
export type ArtistIdentifierCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many ArtistIdentifiers.
   */
  data: Prisma.ArtistIdentifierCreateManyInput | Prisma.ArtistIdentifierCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * ArtistIdentifier createManyAndReturn
 */
export type ArtistIdentifierCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ArtistIdentifier
   */
  select?: Prisma.ArtistIdentifierSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the ArtistIdentifier
   */
  omit?: Prisma.ArtistIdentifierOmit<ExtArgs> | null
  /**
   * The data used to create many ArtistIdentifiers.
   */
  data: Prisma.ArtistIdentifierCreateManyInput | Prisma.ArtistIdentifierCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ArtistIdentifierIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * ArtistIdentifier update
 */
export type ArtistIdentifierUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ArtistIdentifier
   */
  select?: Prisma.ArtistIdentifierSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ArtistIdentifier
   */
  omit?: Prisma.ArtistIdentifierOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ArtistIdentifierInclude<ExtArgs> | null
  /**
   * The data needed to update a ArtistIdentifier.
   */
  data: Prisma.XOR<Prisma.ArtistIdentifierUpdateInput, Prisma.ArtistIdentifierUncheckedUpdateInput>
  /**
   * Choose, which ArtistIdentifier to update.
   */
  where: Prisma.ArtistIdentifierWhereUniqueInput
}

/**
 * ArtistIdentifier updateMany
 */
export type ArtistIdentifierUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update ArtistIdentifiers.
   */
  data: Prisma.XOR<Prisma.ArtistIdentifierUpdateManyMutationInput, Prisma.ArtistIdentifierUncheckedUpdateManyInput>
  /**
   * Filter which ArtistIdentifiers to update
   */
  where?: Prisma.ArtistIdentifierWhereInput
  /**
   * Limit how many ArtistIdentifiers to update.
   */
  limit?: number
}

/**
 * ArtistIdentifier updateManyAndReturn
 */
export type ArtistIdentifierUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ArtistIdentifier
   */
  select?: Prisma.ArtistIdentifierSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the ArtistIdentifier
   */
  omit?: Prisma.ArtistIdentifierOmit<ExtArgs> | null
  /**
   * The data used to update ArtistIdentifiers.
   */
  data: Prisma.XOR<Prisma.ArtistIdentifierUpdateManyMutationInput, Prisma.ArtistIdentifierUncheckedUpdateManyInput>
  /**
   * Filter which ArtistIdentifiers to update
   */
  where?: Prisma.ArtistIdentifierWhereInput
  /**
   * Limit how many ArtistIdentifiers to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ArtistIdentifierIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * ArtistIdentifier upsert
 */
export type ArtistIdentifierUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ArtistIdentifier
   */
  select?: Prisma.ArtistIdentifierSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ArtistIdentifier
   */
  omit?: Prisma.ArtistIdentifierOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ArtistIdentifierInclude<ExtArgs> | null
  /**
   * The filter to search for the ArtistIdentifier to update in case it exists.
   */
  where: Prisma.ArtistIdentifierWhereUniqueInput
  /**
   * In case the ArtistIdentifier found by the `where` argument doesn't exist, create a new ArtistIdentifier with this data.
   */
  create: Prisma.XOR<Prisma.ArtistIdentifierCreateInput, Prisma.ArtistIdentifierUncheckedCreateInput>
  /**
   * In case the ArtistIdentifier was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.ArtistIdentifierUpdateInput, Prisma.ArtistIdentifierUncheckedUpdateInput>
}

/**
 * ArtistIdentifier delete
 */
export type ArtistIdentifierDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ArtistIdentifier
   */
  select?: Prisma.ArtistIdentifierSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ArtistIdentifier
   */
  omit?: Prisma.ArtistIdentifierOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ArtistIdentifierInclude<ExtArgs> | null
  /**
   * Filter which ArtistIdentifier to delete.
   */
  where: Prisma.ArtistIdentifierWhereUniqueInput
}

/**
 * ArtistIdentifier deleteMany
 */
export type ArtistIdentifierDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which ArtistIdentifiers to delete
   */
  where?: Prisma.ArtistIdentifierWhereInput
  /**
   * Limit how many ArtistIdentifiers to delete.
   */
  limit?: number
}

/**
 * ArtistIdentifier without action
 */
export type ArtistIdentifierDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ArtistIdentifier
   */
  select?: Prisma.ArtistIdentifierSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ArtistIdentifier
   */
  omit?: Prisma.ArtistIdentifierOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ArtistIdentifierInclude<ExtArgs> | null
}
