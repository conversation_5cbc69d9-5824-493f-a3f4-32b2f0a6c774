
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports various common sort, input & filter types that are not directly linked to a particular model.
 *
 * 🟢 You can import this file directly.
 */

import * as runtime from "@prisma/client/runtime/library"
import * as $Enums from "./enums"
import type * as Prisma from "./internal/prismaNamespace"


export type StringFilter<$PrismaModel = never> = {
  equals?: string | Prisma.StringFieldRefInput<$PrismaModel>
  in?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel>
  notIn?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel>
  lt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  lte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  startsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  endsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  mode?: Prisma.QueryMode
  not?: Prisma.NestedStringFilter<$PrismaModel> | string
}

export type StringNullableFilter<$PrismaModel = never> = {
  equals?: string | Prisma.StringFieldRefInput<$PrismaModel> | null
  in?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel> | null
  notIn?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel> | null
  lt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  lte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  startsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  endsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  mode?: Prisma.QueryMode
  not?: Prisma.NestedStringNullableFilter<$PrismaModel> | string | null
}

export type DateTimeFilter<$PrismaModel = never> = {
  equals?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  in?: Date[] | string[] | Prisma.ListDateTimeFieldRefInput<$PrismaModel>
  notIn?: Date[] | string[] | Prisma.ListDateTimeFieldRefInput<$PrismaModel>
  lt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  lte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  not?: Prisma.NestedDateTimeFilter<$PrismaModel> | Date | string
}

export type SortOrderInput = {
  sort: Prisma.SortOrder
  nulls?: Prisma.NullsOrder
}

export type StringWithAggregatesFilter<$PrismaModel = never> = {
  equals?: string | Prisma.StringFieldRefInput<$PrismaModel>
  in?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel>
  notIn?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel>
  lt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  lte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  startsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  endsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  mode?: Prisma.QueryMode
  not?: Prisma.NestedStringWithAggregatesFilter<$PrismaModel> | string
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedStringFilter<$PrismaModel>
  _max?: Prisma.NestedStringFilter<$PrismaModel>
}

export type StringNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: string | Prisma.StringFieldRefInput<$PrismaModel> | null
  in?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel> | null
  notIn?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel> | null
  lt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  lte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  startsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  endsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  mode?: Prisma.QueryMode
  not?: Prisma.NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedStringNullableFilter<$PrismaModel>
  _max?: Prisma.NestedStringNullableFilter<$PrismaModel>
}

export type DateTimeWithAggregatesFilter<$PrismaModel = never> = {
  equals?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  in?: Date[] | string[] | Prisma.ListDateTimeFieldRefInput<$PrismaModel>
  notIn?: Date[] | string[] | Prisma.ListDateTimeFieldRefInput<$PrismaModel>
  lt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  lte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  not?: Prisma.NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedDateTimeFilter<$PrismaModel>
  _max?: Prisma.NestedDateTimeFilter<$PrismaModel>
}

export type EnumServiceFilter<$PrismaModel = never> = {
  equals?: $Enums.Service | Prisma.EnumServiceFieldRefInput<$PrismaModel>
  in?: $Enums.Service[] | Prisma.ListEnumServiceFieldRefInput<$PrismaModel>
  notIn?: $Enums.Service[] | Prisma.ListEnumServiceFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumServiceFilter<$PrismaModel> | $Enums.Service
}

export type EnumServiceWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.Service | Prisma.EnumServiceFieldRefInput<$PrismaModel>
  in?: $Enums.Service[] | Prisma.ListEnumServiceFieldRefInput<$PrismaModel>
  notIn?: $Enums.Service[] | Prisma.ListEnumServiceFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumServiceWithAggregatesFilter<$PrismaModel> | $Enums.Service
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumServiceFilter<$PrismaModel>
  _max?: Prisma.NestedEnumServiceFilter<$PrismaModel>
}

export type BoolFilter<$PrismaModel = never> = {
  equals?: boolean | Prisma.BooleanFieldRefInput<$PrismaModel>
  not?: Prisma.NestedBoolFilter<$PrismaModel> | boolean
}

export type BoolNullableFilter<$PrismaModel = never> = {
  equals?: boolean | Prisma.BooleanFieldRefInput<$PrismaModel> | null
  not?: Prisma.NestedBoolNullableFilter<$PrismaModel> | boolean | null
}

export type DateTimeNullableFilter<$PrismaModel = never> = {
  equals?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel> | null
  in?: Date[] | string[] | Prisma.ListDateTimeFieldRefInput<$PrismaModel> | null
  notIn?: Date[] | string[] | Prisma.ListDateTimeFieldRefInput<$PrismaModel> | null
  lt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  lte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  not?: Prisma.NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
}

export type BoolWithAggregatesFilter<$PrismaModel = never> = {
  equals?: boolean | Prisma.BooleanFieldRefInput<$PrismaModel>
  not?: Prisma.NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedBoolFilter<$PrismaModel>
  _max?: Prisma.NestedBoolFilter<$PrismaModel>
}

export type BoolNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: boolean | Prisma.BooleanFieldRefInput<$PrismaModel> | null
  not?: Prisma.NestedBoolNullableWithAggregatesFilter<$PrismaModel> | boolean | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedBoolNullableFilter<$PrismaModel>
  _max?: Prisma.NestedBoolNullableFilter<$PrismaModel>
}

export type DateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel> | null
  in?: Date[] | string[] | Prisma.ListDateTimeFieldRefInput<$PrismaModel> | null
  notIn?: Date[] | string[] | Prisma.ListDateTimeFieldRefInput<$PrismaModel> | null
  lt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  lte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  not?: Prisma.NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedDateTimeNullableFilter<$PrismaModel>
  _max?: Prisma.NestedDateTimeNullableFilter<$PrismaModel>
}

export type EnumReleaseFormatFilter<$PrismaModel = never> = {
  equals?: $Enums.ReleaseFormat | Prisma.EnumReleaseFormatFieldRefInput<$PrismaModel>
  in?: $Enums.ReleaseFormat[] | Prisma.ListEnumReleaseFormatFieldRefInput<$PrismaModel>
  notIn?: $Enums.ReleaseFormat[] | Prisma.ListEnumReleaseFormatFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumReleaseFormatFilter<$PrismaModel> | $Enums.ReleaseFormat
}

export type EnumExplicitContentFilter<$PrismaModel = never> = {
  equals?: $Enums.ExplicitContent | Prisma.EnumExplicitContentFieldRefInput<$PrismaModel>
  in?: $Enums.ExplicitContent[] | Prisma.ListEnumExplicitContentFieldRefInput<$PrismaModel>
  notIn?: $Enums.ExplicitContent[] | Prisma.ListEnumExplicitContentFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumExplicitContentFilter<$PrismaModel> | $Enums.ExplicitContent
}

export type IntFilter<$PrismaModel = never> = {
  equals?: number | Prisma.IntFieldRefInput<$PrismaModel>
  in?: number[] | Prisma.ListIntFieldRefInput<$PrismaModel>
  notIn?: number[] | Prisma.ListIntFieldRefInput<$PrismaModel>
  lt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  lte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  not?: Prisma.NestedIntFilter<$PrismaModel> | number
}

export type EnumReleaseStatusFilter<$PrismaModel = never> = {
  equals?: $Enums.ReleaseStatus | Prisma.EnumReleaseStatusFieldRefInput<$PrismaModel>
  in?: $Enums.ReleaseStatus[] | Prisma.ListEnumReleaseStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.ReleaseStatus[] | Prisma.ListEnumReleaseStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumReleaseStatusFilter<$PrismaModel> | $Enums.ReleaseStatus
}

export type EnumReleaseFormatWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.ReleaseFormat | Prisma.EnumReleaseFormatFieldRefInput<$PrismaModel>
  in?: $Enums.ReleaseFormat[] | Prisma.ListEnumReleaseFormatFieldRefInput<$PrismaModel>
  notIn?: $Enums.ReleaseFormat[] | Prisma.ListEnumReleaseFormatFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumReleaseFormatWithAggregatesFilter<$PrismaModel> | $Enums.ReleaseFormat
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumReleaseFormatFilter<$PrismaModel>
  _max?: Prisma.NestedEnumReleaseFormatFilter<$PrismaModel>
}

export type EnumExplicitContentWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.ExplicitContent | Prisma.EnumExplicitContentFieldRefInput<$PrismaModel>
  in?: $Enums.ExplicitContent[] | Prisma.ListEnumExplicitContentFieldRefInput<$PrismaModel>
  notIn?: $Enums.ExplicitContent[] | Prisma.ListEnumExplicitContentFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumExplicitContentWithAggregatesFilter<$PrismaModel> | $Enums.ExplicitContent
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumExplicitContentFilter<$PrismaModel>
  _max?: Prisma.NestedEnumExplicitContentFilter<$PrismaModel>
}

export type IntWithAggregatesFilter<$PrismaModel = never> = {
  equals?: number | Prisma.IntFieldRefInput<$PrismaModel>
  in?: number[] | Prisma.ListIntFieldRefInput<$PrismaModel>
  notIn?: number[] | Prisma.ListIntFieldRefInput<$PrismaModel>
  lt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  lte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  not?: Prisma.NestedIntWithAggregatesFilter<$PrismaModel> | number
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _avg?: Prisma.NestedFloatFilter<$PrismaModel>
  _sum?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedIntFilter<$PrismaModel>
  _max?: Prisma.NestedIntFilter<$PrismaModel>
}

export type EnumReleaseStatusWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.ReleaseStatus | Prisma.EnumReleaseStatusFieldRefInput<$PrismaModel>
  in?: $Enums.ReleaseStatus[] | Prisma.ListEnumReleaseStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.ReleaseStatus[] | Prisma.ListEnumReleaseStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumReleaseStatusWithAggregatesFilter<$PrismaModel> | $Enums.ReleaseStatus
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumReleaseStatusFilter<$PrismaModel>
  _max?: Prisma.NestedEnumReleaseStatusFilter<$PrismaModel>
}

export type EnumArtistRoleFilter<$PrismaModel = never> = {
  equals?: $Enums.ArtistRole | Prisma.EnumArtistRoleFieldRefInput<$PrismaModel>
  in?: $Enums.ArtistRole[] | Prisma.ListEnumArtistRoleFieldRefInput<$PrismaModel>
  notIn?: $Enums.ArtistRole[] | Prisma.ListEnumArtistRoleFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumArtistRoleFilter<$PrismaModel> | $Enums.ArtistRole
}

export type EnumArtistRoleWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.ArtistRole | Prisma.EnumArtistRoleFieldRefInput<$PrismaModel>
  in?: $Enums.ArtistRole[] | Prisma.ListEnumArtistRoleFieldRefInput<$PrismaModel>
  notIn?: $Enums.ArtistRole[] | Prisma.ListEnumArtistRoleFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumArtistRoleWithAggregatesFilter<$PrismaModel> | $Enums.ArtistRole
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumArtistRoleFilter<$PrismaModel>
  _max?: Prisma.NestedEnumArtistRoleFilter<$PrismaModel>
}

export type IntNullableFilter<$PrismaModel = never> = {
  equals?: number | Prisma.IntFieldRefInput<$PrismaModel> | null
  in?: number[] | Prisma.ListIntFieldRefInput<$PrismaModel> | null
  notIn?: number[] | Prisma.ListIntFieldRefInput<$PrismaModel> | null
  lt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  lte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  not?: Prisma.NestedIntNullableFilter<$PrismaModel> | number | null
}

export type IntNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: number | Prisma.IntFieldRefInput<$PrismaModel> | null
  in?: number[] | Prisma.ListIntFieldRefInput<$PrismaModel> | null
  notIn?: number[] | Prisma.ListIntFieldRefInput<$PrismaModel> | null
  lt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  lte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  not?: Prisma.NestedIntNullableWithAggregatesFilter<$PrismaModel> | number | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _avg?: Prisma.NestedFloatNullableFilter<$PrismaModel>
  _sum?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _max?: Prisma.NestedIntNullableFilter<$PrismaModel>
}

export type EnumRightsClaimFilter<$PrismaModel = never> = {
  equals?: $Enums.RightsClaim | Prisma.EnumRightsClaimFieldRefInput<$PrismaModel>
  in?: $Enums.RightsClaim[] | Prisma.ListEnumRightsClaimFieldRefInput<$PrismaModel>
  notIn?: $Enums.RightsClaim[] | Prisma.ListEnumRightsClaimFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumRightsClaimFilter<$PrismaModel> | $Enums.RightsClaim
}

export type EnumTrackStatusFilter<$PrismaModel = never> = {
  equals?: $Enums.TrackStatus | Prisma.EnumTrackStatusFieldRefInput<$PrismaModel>
  in?: $Enums.TrackStatus[] | Prisma.ListEnumTrackStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.TrackStatus[] | Prisma.ListEnumTrackStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumTrackStatusFilter<$PrismaModel> | $Enums.TrackStatus
}

export type EnumRightsClaimWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.RightsClaim | Prisma.EnumRightsClaimFieldRefInput<$PrismaModel>
  in?: $Enums.RightsClaim[] | Prisma.ListEnumRightsClaimFieldRefInput<$PrismaModel>
  notIn?: $Enums.RightsClaim[] | Prisma.ListEnumRightsClaimFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumRightsClaimWithAggregatesFilter<$PrismaModel> | $Enums.RightsClaim
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumRightsClaimFilter<$PrismaModel>
  _max?: Prisma.NestedEnumRightsClaimFilter<$PrismaModel>
}

export type EnumTrackStatusWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.TrackStatus | Prisma.EnumTrackStatusFieldRefInput<$PrismaModel>
  in?: $Enums.TrackStatus[] | Prisma.ListEnumTrackStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.TrackStatus[] | Prisma.ListEnumTrackStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumTrackStatusWithAggregatesFilter<$PrismaModel> | $Enums.TrackStatus
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumTrackStatusFilter<$PrismaModel>
  _max?: Prisma.NestedEnumTrackStatusFilter<$PrismaModel>
}

export type NestedStringFilter<$PrismaModel = never> = {
  equals?: string | Prisma.StringFieldRefInput<$PrismaModel>
  in?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel>
  notIn?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel>
  lt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  lte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  startsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  endsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  not?: Prisma.NestedStringFilter<$PrismaModel> | string
}

export type NestedStringNullableFilter<$PrismaModel = never> = {
  equals?: string | Prisma.StringFieldRefInput<$PrismaModel> | null
  in?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel> | null
  notIn?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel> | null
  lt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  lte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  startsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  endsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  not?: Prisma.NestedStringNullableFilter<$PrismaModel> | string | null
}

export type NestedDateTimeFilter<$PrismaModel = never> = {
  equals?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  in?: Date[] | string[] | Prisma.ListDateTimeFieldRefInput<$PrismaModel>
  notIn?: Date[] | string[] | Prisma.ListDateTimeFieldRefInput<$PrismaModel>
  lt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  lte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  not?: Prisma.NestedDateTimeFilter<$PrismaModel> | Date | string
}

export type NestedStringWithAggregatesFilter<$PrismaModel = never> = {
  equals?: string | Prisma.StringFieldRefInput<$PrismaModel>
  in?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel>
  notIn?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel>
  lt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  lte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  startsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  endsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  not?: Prisma.NestedStringWithAggregatesFilter<$PrismaModel> | string
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedStringFilter<$PrismaModel>
  _max?: Prisma.NestedStringFilter<$PrismaModel>
}

export type NestedIntFilter<$PrismaModel = never> = {
  equals?: number | Prisma.IntFieldRefInput<$PrismaModel>
  in?: number[] | Prisma.ListIntFieldRefInput<$PrismaModel>
  notIn?: number[] | Prisma.ListIntFieldRefInput<$PrismaModel>
  lt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  lte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  not?: Prisma.NestedIntFilter<$PrismaModel> | number
}

export type NestedStringNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: string | Prisma.StringFieldRefInput<$PrismaModel> | null
  in?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel> | null
  notIn?: string[] | Prisma.ListStringFieldRefInput<$PrismaModel> | null
  lt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  lte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gt?: string | Prisma.StringFieldRefInput<$PrismaModel>
  gte?: string | Prisma.StringFieldRefInput<$PrismaModel>
  contains?: string | Prisma.StringFieldRefInput<$PrismaModel>
  startsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  endsWith?: string | Prisma.StringFieldRefInput<$PrismaModel>
  not?: Prisma.NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedStringNullableFilter<$PrismaModel>
  _max?: Prisma.NestedStringNullableFilter<$PrismaModel>
}

export type NestedIntNullableFilter<$PrismaModel = never> = {
  equals?: number | Prisma.IntFieldRefInput<$PrismaModel> | null
  in?: number[] | Prisma.ListIntFieldRefInput<$PrismaModel> | null
  notIn?: number[] | Prisma.ListIntFieldRefInput<$PrismaModel> | null
  lt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  lte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  not?: Prisma.NestedIntNullableFilter<$PrismaModel> | number | null
}

export type NestedDateTimeWithAggregatesFilter<$PrismaModel = never> = {
  equals?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  in?: Date[] | string[] | Prisma.ListDateTimeFieldRefInput<$PrismaModel>
  notIn?: Date[] | string[] | Prisma.ListDateTimeFieldRefInput<$PrismaModel>
  lt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  lte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  not?: Prisma.NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedDateTimeFilter<$PrismaModel>
  _max?: Prisma.NestedDateTimeFilter<$PrismaModel>
}

export type NestedEnumServiceFilter<$PrismaModel = never> = {
  equals?: $Enums.Service | Prisma.EnumServiceFieldRefInput<$PrismaModel>
  in?: $Enums.Service[] | Prisma.ListEnumServiceFieldRefInput<$PrismaModel>
  notIn?: $Enums.Service[] | Prisma.ListEnumServiceFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumServiceFilter<$PrismaModel> | $Enums.Service
}

export type NestedEnumServiceWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.Service | Prisma.EnumServiceFieldRefInput<$PrismaModel>
  in?: $Enums.Service[] | Prisma.ListEnumServiceFieldRefInput<$PrismaModel>
  notIn?: $Enums.Service[] | Prisma.ListEnumServiceFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumServiceWithAggregatesFilter<$PrismaModel> | $Enums.Service
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumServiceFilter<$PrismaModel>
  _max?: Prisma.NestedEnumServiceFilter<$PrismaModel>
}

export type NestedBoolFilter<$PrismaModel = never> = {
  equals?: boolean | Prisma.BooleanFieldRefInput<$PrismaModel>
  not?: Prisma.NestedBoolFilter<$PrismaModel> | boolean
}

export type NestedBoolNullableFilter<$PrismaModel = never> = {
  equals?: boolean | Prisma.BooleanFieldRefInput<$PrismaModel> | null
  not?: Prisma.NestedBoolNullableFilter<$PrismaModel> | boolean | null
}

export type NestedDateTimeNullableFilter<$PrismaModel = never> = {
  equals?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel> | null
  in?: Date[] | string[] | Prisma.ListDateTimeFieldRefInput<$PrismaModel> | null
  notIn?: Date[] | string[] | Prisma.ListDateTimeFieldRefInput<$PrismaModel> | null
  lt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  lte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  not?: Prisma.NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
}

export type NestedBoolWithAggregatesFilter<$PrismaModel = never> = {
  equals?: boolean | Prisma.BooleanFieldRefInput<$PrismaModel>
  not?: Prisma.NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedBoolFilter<$PrismaModel>
  _max?: Prisma.NestedBoolFilter<$PrismaModel>
}

export type NestedBoolNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: boolean | Prisma.BooleanFieldRefInput<$PrismaModel> | null
  not?: Prisma.NestedBoolNullableWithAggregatesFilter<$PrismaModel> | boolean | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedBoolNullableFilter<$PrismaModel>
  _max?: Prisma.NestedBoolNullableFilter<$PrismaModel>
}

export type NestedDateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel> | null
  in?: Date[] | string[] | Prisma.ListDateTimeFieldRefInput<$PrismaModel> | null
  notIn?: Date[] | string[] | Prisma.ListDateTimeFieldRefInput<$PrismaModel> | null
  lt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  lte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gt?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  gte?: Date | string | Prisma.DateTimeFieldRefInput<$PrismaModel>
  not?: Prisma.NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedDateTimeNullableFilter<$PrismaModel>
  _max?: Prisma.NestedDateTimeNullableFilter<$PrismaModel>
}

export type NestedEnumReleaseFormatFilter<$PrismaModel = never> = {
  equals?: $Enums.ReleaseFormat | Prisma.EnumReleaseFormatFieldRefInput<$PrismaModel>
  in?: $Enums.ReleaseFormat[] | Prisma.ListEnumReleaseFormatFieldRefInput<$PrismaModel>
  notIn?: $Enums.ReleaseFormat[] | Prisma.ListEnumReleaseFormatFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumReleaseFormatFilter<$PrismaModel> | $Enums.ReleaseFormat
}

export type NestedEnumExplicitContentFilter<$PrismaModel = never> = {
  equals?: $Enums.ExplicitContent | Prisma.EnumExplicitContentFieldRefInput<$PrismaModel>
  in?: $Enums.ExplicitContent[] | Prisma.ListEnumExplicitContentFieldRefInput<$PrismaModel>
  notIn?: $Enums.ExplicitContent[] | Prisma.ListEnumExplicitContentFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumExplicitContentFilter<$PrismaModel> | $Enums.ExplicitContent
}

export type NestedEnumReleaseStatusFilter<$PrismaModel = never> = {
  equals?: $Enums.ReleaseStatus | Prisma.EnumReleaseStatusFieldRefInput<$PrismaModel>
  in?: $Enums.ReleaseStatus[] | Prisma.ListEnumReleaseStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.ReleaseStatus[] | Prisma.ListEnumReleaseStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumReleaseStatusFilter<$PrismaModel> | $Enums.ReleaseStatus
}

export type NestedEnumReleaseFormatWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.ReleaseFormat | Prisma.EnumReleaseFormatFieldRefInput<$PrismaModel>
  in?: $Enums.ReleaseFormat[] | Prisma.ListEnumReleaseFormatFieldRefInput<$PrismaModel>
  notIn?: $Enums.ReleaseFormat[] | Prisma.ListEnumReleaseFormatFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumReleaseFormatWithAggregatesFilter<$PrismaModel> | $Enums.ReleaseFormat
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumReleaseFormatFilter<$PrismaModel>
  _max?: Prisma.NestedEnumReleaseFormatFilter<$PrismaModel>
}

export type NestedEnumExplicitContentWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.ExplicitContent | Prisma.EnumExplicitContentFieldRefInput<$PrismaModel>
  in?: $Enums.ExplicitContent[] | Prisma.ListEnumExplicitContentFieldRefInput<$PrismaModel>
  notIn?: $Enums.ExplicitContent[] | Prisma.ListEnumExplicitContentFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumExplicitContentWithAggregatesFilter<$PrismaModel> | $Enums.ExplicitContent
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumExplicitContentFilter<$PrismaModel>
  _max?: Prisma.NestedEnumExplicitContentFilter<$PrismaModel>
}

export type NestedIntWithAggregatesFilter<$PrismaModel = never> = {
  equals?: number | Prisma.IntFieldRefInput<$PrismaModel>
  in?: number[] | Prisma.ListIntFieldRefInput<$PrismaModel>
  notIn?: number[] | Prisma.ListIntFieldRefInput<$PrismaModel>
  lt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  lte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  not?: Prisma.NestedIntWithAggregatesFilter<$PrismaModel> | number
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _avg?: Prisma.NestedFloatFilter<$PrismaModel>
  _sum?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedIntFilter<$PrismaModel>
  _max?: Prisma.NestedIntFilter<$PrismaModel>
}

export type NestedFloatFilter<$PrismaModel = never> = {
  equals?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  in?: number[] | Prisma.ListFloatFieldRefInput<$PrismaModel>
  notIn?: number[] | Prisma.ListFloatFieldRefInput<$PrismaModel>
  lt?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  lte?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  gt?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  gte?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  not?: Prisma.NestedFloatFilter<$PrismaModel> | number
}

export type NestedEnumReleaseStatusWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.ReleaseStatus | Prisma.EnumReleaseStatusFieldRefInput<$PrismaModel>
  in?: $Enums.ReleaseStatus[] | Prisma.ListEnumReleaseStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.ReleaseStatus[] | Prisma.ListEnumReleaseStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumReleaseStatusWithAggregatesFilter<$PrismaModel> | $Enums.ReleaseStatus
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumReleaseStatusFilter<$PrismaModel>
  _max?: Prisma.NestedEnumReleaseStatusFilter<$PrismaModel>
}

export type NestedEnumArtistRoleFilter<$PrismaModel = never> = {
  equals?: $Enums.ArtistRole | Prisma.EnumArtistRoleFieldRefInput<$PrismaModel>
  in?: $Enums.ArtistRole[] | Prisma.ListEnumArtistRoleFieldRefInput<$PrismaModel>
  notIn?: $Enums.ArtistRole[] | Prisma.ListEnumArtistRoleFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumArtistRoleFilter<$PrismaModel> | $Enums.ArtistRole
}

export type NestedEnumArtistRoleWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.ArtistRole | Prisma.EnumArtistRoleFieldRefInput<$PrismaModel>
  in?: $Enums.ArtistRole[] | Prisma.ListEnumArtistRoleFieldRefInput<$PrismaModel>
  notIn?: $Enums.ArtistRole[] | Prisma.ListEnumArtistRoleFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumArtistRoleWithAggregatesFilter<$PrismaModel> | $Enums.ArtistRole
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumArtistRoleFilter<$PrismaModel>
  _max?: Prisma.NestedEnumArtistRoleFilter<$PrismaModel>
}

export type NestedIntNullableWithAggregatesFilter<$PrismaModel = never> = {
  equals?: number | Prisma.IntFieldRefInput<$PrismaModel> | null
  in?: number[] | Prisma.ListIntFieldRefInput<$PrismaModel> | null
  notIn?: number[] | Prisma.ListIntFieldRefInput<$PrismaModel> | null
  lt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  lte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gt?: number | Prisma.IntFieldRefInput<$PrismaModel>
  gte?: number | Prisma.IntFieldRefInput<$PrismaModel>
  not?: Prisma.NestedIntNullableWithAggregatesFilter<$PrismaModel> | number | null
  _count?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _avg?: Prisma.NestedFloatNullableFilter<$PrismaModel>
  _sum?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _min?: Prisma.NestedIntNullableFilter<$PrismaModel>
  _max?: Prisma.NestedIntNullableFilter<$PrismaModel>
}

export type NestedFloatNullableFilter<$PrismaModel = never> = {
  equals?: number | Prisma.FloatFieldRefInput<$PrismaModel> | null
  in?: number[] | Prisma.ListFloatFieldRefInput<$PrismaModel> | null
  notIn?: number[] | Prisma.ListFloatFieldRefInput<$PrismaModel> | null
  lt?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  lte?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  gt?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  gte?: number | Prisma.FloatFieldRefInput<$PrismaModel>
  not?: Prisma.NestedFloatNullableFilter<$PrismaModel> | number | null
}

export type NestedEnumRightsClaimFilter<$PrismaModel = never> = {
  equals?: $Enums.RightsClaim | Prisma.EnumRightsClaimFieldRefInput<$PrismaModel>
  in?: $Enums.RightsClaim[] | Prisma.ListEnumRightsClaimFieldRefInput<$PrismaModel>
  notIn?: $Enums.RightsClaim[] | Prisma.ListEnumRightsClaimFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumRightsClaimFilter<$PrismaModel> | $Enums.RightsClaim
}

export type NestedEnumTrackStatusFilter<$PrismaModel = never> = {
  equals?: $Enums.TrackStatus | Prisma.EnumTrackStatusFieldRefInput<$PrismaModel>
  in?: $Enums.TrackStatus[] | Prisma.ListEnumTrackStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.TrackStatus[] | Prisma.ListEnumTrackStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumTrackStatusFilter<$PrismaModel> | $Enums.TrackStatus
}

export type NestedEnumRightsClaimWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.RightsClaim | Prisma.EnumRightsClaimFieldRefInput<$PrismaModel>
  in?: $Enums.RightsClaim[] | Prisma.ListEnumRightsClaimFieldRefInput<$PrismaModel>
  notIn?: $Enums.RightsClaim[] | Prisma.ListEnumRightsClaimFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumRightsClaimWithAggregatesFilter<$PrismaModel> | $Enums.RightsClaim
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumRightsClaimFilter<$PrismaModel>
  _max?: Prisma.NestedEnumRightsClaimFilter<$PrismaModel>
}

export type NestedEnumTrackStatusWithAggregatesFilter<$PrismaModel = never> = {
  equals?: $Enums.TrackStatus | Prisma.EnumTrackStatusFieldRefInput<$PrismaModel>
  in?: $Enums.TrackStatus[] | Prisma.ListEnumTrackStatusFieldRefInput<$PrismaModel>
  notIn?: $Enums.TrackStatus[] | Prisma.ListEnumTrackStatusFieldRefInput<$PrismaModel>
  not?: Prisma.NestedEnumTrackStatusWithAggregatesFilter<$PrismaModel> | $Enums.TrackStatus
  _count?: Prisma.NestedIntFilter<$PrismaModel>
  _min?: Prisma.NestedEnumTrackStatusFilter<$PrismaModel>
  _max?: Prisma.NestedEnumTrackStatusFilter<$PrismaModel>
}


