import { protectedProcedure, router } from "@/lib/trpc";
import { z } from "zod";
import prisma from "../../prisma/index";
import { TRPCError } from "@trpc/server";

// Validation schemas
const CreateLabelSchema = z.object({
  name: z.string().min(1, "Label name is required").max(100, "Name too long"),
});

const UpdateLabelSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1, "Label name is required").max(100, "Name too long"),
});

const GetLabelSchema = z.object({
  id: z.string().uuid(),
});

const GetLabelsSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  search: z.string().optional(),
});

const DeleteLabelSchema = z.object({
  id: z.string().uuid(),
});

export const labelRouter = router({
  // Create a new label
  create: protectedProcedure
    .input(CreateLabelSchema)
    .mutation(async ({ ctx, input }) => {
      const { session } = ctx;
      const isAdmin = session.user.role === "admin";

      try {
        // Check if label name already exists for this user
        const existingLabel = await prisma.label.findFirst({
          where: {
            userId: session.user.id,
            name: input.name,
          },
        });

        if (existingLabel) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "You already have a label with this name",
          });
        }

        const label = await prisma.label.create({
          data: {
            name: input.name,
            userId: session.user.id,
          },
          include: {
            user: true,
            artists: true,
          },
        });

        return label;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create label",
          cause: error,
        });
      }
    }),

  // Get a single label by ID
  getById: protectedProcedure
    .input(GetLabelSchema)
    .query(async ({ ctx, input }) => {
      const { session } = ctx;
      const isAdmin = session.user.role === "admin";

      try {
        const label = await prisma.label.findUnique({
          where: {
            id: input.id,
          },
          include: {
            user: true,
            artists: true,
          },
        });

        if (!label) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Label not found",
          });
        }

        // Check if user owns this label
        if (label.userId !== session.user.id && !isAdmin) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "Not authorized to view this label",
          });
        }

        return label;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to get label",
          cause: error,
        });
      }
    }),

  // Get all labels for the current user with pagination and filtering
  getAll: protectedProcedure
    .input(GetLabelsSchema)
    .query(async ({ ctx, input }) => {
      const { session } = ctx;
      const isAdmin = session.user.role === "admin";
      const { page, limit, search } = input;
      const skip = (page - 1) * limit;

      try {
        const where = {
          ...(isAdmin ? {} : { userId: session.user.id }),
          ...(search && {
            name: {
              contains: search,
              mode: "insensitive" as const,
            },
          }),
        };

        const [labels, total] = await Promise.all([
          prisma.label.findMany({
            where,
            include: {
              user: true,
              artists: true,
            },
            orderBy: {
              createdAt: "desc",
            },
            skip,
            take: limit,
          }),
          prisma.label.count({ where }),
        ]);

        return {
          labels,
          pagination: {
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
          },
        };
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to get labels",
          cause: error,
        });
      }
    }),

  // Update a label
  update: protectedProcedure
    .input(UpdateLabelSchema)
    .mutation(async ({ ctx, input }) => {
      const { session } = ctx;
      const isAdmin = session.user.role === "admin";
      const { id, name } = input;

      try {
        // Check if label exists and belongs to user
        const existingLabel = await prisma.label.findUnique({
          where: { id },
        });

        if (!existingLabel) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Label not found",
          });
        }

        if (existingLabel.userId !== session.user.id && !isAdmin) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "Not authorized to update this label",
          });
        }

        // Check for name conflicts if name is being updated
        if (name !== existingLabel.name) {
          const nameConflict = await prisma.label.findFirst({
            where: {
              userId: session.user.id,
              name: name,
              id: { not: id },
            },
          });

          if (nameConflict) {
            throw new TRPCError({
              code: "CONFLICT",
              message: "You already have a label with this name",
            });
          }
        }

        const updatedLabel = await prisma.label.update({
          where: { id },
          data: { name },
          include: {
            user: true,
            artists: true,
          },
        });

        return updatedLabel;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update label",
          cause: error,
        });
      }
    }),

  // Delete a label
  delete: protectedProcedure
    .input(DeleteLabelSchema)
    .mutation(async ({ ctx, input }) => {
      const { session } = ctx;
      const isAdmin = session.user.role === "admin";

      try {
        // Check if label exists and belongs to user
        const existingLabel = await prisma.label.findUnique({
          where: { id: input.id },
          include: {
            artists: true,
          },
        });

        if (!existingLabel) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Label not found",
          });
        }

        if (existingLabel.userId !== session.user.id && !isAdmin) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "Not authorized to delete this label",
          });
        }

        // Note: Artists associated with this label will have their labelId set to null
        // due to the onDelete: SetNull constraint in the database schema

        // Delete label
        await prisma.label.delete({
          where: { id: input.id },
        });

        return { success: true, message: "Label deleted successfully" };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete label",
          cause: error,
        });
      }
    }),

  // Get all labels for dropdown/select (minimal data)
  getForSelect: protectedProcedure
    .input(
      z
        .object({
          artistId: z.string().uuid().optional(),
        })
        .optional()
    )
    .query(async ({ ctx, input }) => {
      const { session } = ctx;
      const isAdmin = session.user.role === "admin";

      try {
        let targetUserId = session.user.id;

        // If admin is editing an artist, show only the artist owner's labels
        if (isAdmin && input?.artistId) {
          const artist = await prisma.artist.findUnique({
            where: { id: input.artistId },
            select: { userId: true },
          });

          if (!artist) {
            throw new TRPCError({
              code: "NOT_FOUND",
              message: "Artist not found",
            });
          }

          targetUserId = artist.userId;
        }

        const labels = await prisma.label.findMany({
          where: {
            userId: targetUserId,
          },
          select: {
            id: true,
            name: true,
          },
          orderBy: {
            name: "asc",
          },
        });

        return labels;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to get labels for select",
          cause: error,
        });
      }
    }),
});
