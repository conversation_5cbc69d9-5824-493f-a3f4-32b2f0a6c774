import type { Row } from "@tanstack/react-table";
import {
  El<PERSON>sisIcon,
  TrashIcon,
  CircleAlertIcon,
  EditIcon,
  EyeIcon,
} from "lucide-react";
import { toast } from "sonner";
import { useMutation } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";

import { useTRPCClient } from "@/utils/trpc";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { UpdateArtistDialog } from "@/routes/dashboard/artist/components/update-artist-dialog";
import type { ArtistTableItem } from "@/routes/dashboard/artist/components/artist-table";

interface RowActionsProps {
  row: Row<ArtistTableItem>;
  onArtistDeleted: () => void;
}

export function RowActions({ row, onArtistDeleted }: RowActionsProps) {
  const trpcClient = useTRPCClient();
  const navigate = useNavigate();

  const deleteArtistMutation = useMutation({
    mutationFn: async () => {
      return trpcClient.artist.delete.mutate({
        id: row.original.id,
      });
    },
    onSuccess: () => {
      toast.success(`Successfully deleted artist ${row.original.name}`);
      onArtistDeleted();
    },
    onError: (error: any) => {
      console.error("Failed to delete artist:", error);
      toast.error(
        "Failed to delete artist: " + (error.message || "Unknown error")
      );
    },
  });

  const handleDeleteArtist = async () => {
    try {
      await deleteArtistMutation.mutateAsync();
    } catch (error) {
      // Error handling is done in the mutation
    }
  };

  const handleViewDetails = () => {
    navigate({
      to: "/dashboard/artist/$id",
      params: { id: row.original.id },
    });
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="flex justify-end">
          <Button
            size="icon"
            variant="ghost"
            className="shadow-none cursor-pointer"
            aria-label="Edit item"
          >
            <EllipsisIcon size={16} aria-hidden="true" />
          </Button>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuGroup>
          <DropdownMenuItem
            onClick={handleViewDetails}
            className="cursor-pointer"
          >
            <EyeIcon className="h-4 w-4" />
            <span>View Details</span>
          </DropdownMenuItem>
          <UpdateArtistDialog
            artist={row.original}
            onArtistUpdated={onArtistDeleted}
          >
            <DropdownMenuItem
              onSelect={(e) => e.preventDefault()}
              className="cursor-pointer"
            >
              <EditIcon className="h-4 w-4" />
              <span>Edit Artist</span>
            </DropdownMenuItem>
          </UpdateArtistDialog>
        </DropdownMenuGroup>
        <DropdownMenuSeparator className="h-[2px]" />
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <DropdownMenuItem
              className="text-destructive focus:text-destructive cursor-pointer"
              onSelect={(e) => e.preventDefault()}
            >
              <div className="flex items-center gap-2">
                <TrashIcon
                  size={16}
                  className="text-destructive focus:text-destructive"
                />
                <span>Delete</span>
              </div>
            </DropdownMenuItem>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <div className="flex flex-col gap-2 max-sm:items-center sm:flex-row sm:gap-4">
              <div
                className="flex size-9 shrink-0 items-center justify-center rounded-full border"
                aria-hidden="true"
              >
                <CircleAlertIcon className="opacity-80" size={16} />
              </div>
              <AlertDialogHeader>
                <AlertDialogTitle>Delete Artist</AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to delete{" "}
                  <strong>{row.original.name}</strong>? This action cannot be
                  undone and will permanently remove the artist and all
                  associated data.
                </AlertDialogDescription>
              </AlertDialogHeader>
            </div>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDeleteArtist}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                disabled={deleteArtistMutation.isPending}
              >
                {deleteArtistMutation.isPending
                  ? "Deleting..."
                  : "Delete Artist"}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
