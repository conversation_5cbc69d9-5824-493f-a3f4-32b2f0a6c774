{"name": "web", "private": true, "type": "module", "scripts": {"build": "vite build", "serve": "vite preview", "dev": "vite dev --port=3001"}, "dependencies": {"@edgestore/react": "^0.5.3", "@edgestore/server": "^0.5.3", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.1.8", "@tanstack/react-form": "^1.0.5", "@tanstack/react-query": "^5.80.5", "@tanstack/react-router": "^1.121.0-alpha.27", "@tanstack/react-router-with-query": "^1.121.0", "@tanstack/react-start": "^1.121.0-alpha.27", "@tanstack/react-table": "^8.21.3", "@tanstack/router-plugin": "^1.121.0", "@trpc/client": "^11.0.0", "@trpc/server": "^11.0.0", "@trpc/tanstack-react-query": "^11.0.0", "better-auth": "^1.2.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "country-data-list": "^1.4.1", "lucide-react": "^0.473.0", "next-themes": "^0.4.6", "radix-ui": "^1.4.2", "react": "19.0.0", "react-circle-flags": "^0.0.23", "react-dom": "19.0.0", "react-dropzone": "^14.3.8", "sonner": "^2.0.3", "tailwind-merge": "^2.6.0", "tailwindcss": "^4.1.3", "tw-animate-css": "^1.2.5", "vite-tsconfig-paths": "^5.1.4", "zod": "^3.25.76"}, "devDependencies": {"@stagewise-plugins/react": "^0.4.8", "@stagewise/toolbar-react": "^0.4.8", "@tanstack/react-query-devtools": "^5.80.5", "@tanstack/react-router-devtools": "^1.121.0-alpha.27", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.2.0", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.5.2", "jsdom": "^26.0.0", "typescript": "^5.7.2", "vite": "^6.3.5", "web-vitals": "^4.2.4"}}