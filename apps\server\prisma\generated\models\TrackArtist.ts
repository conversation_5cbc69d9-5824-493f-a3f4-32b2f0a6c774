
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `TrackArtist` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model TrackArtist
 * 
 */
export type TrackArtistModel = runtime.Types.Result.DefaultSelection<Prisma.$TrackArtistPayload>

export type AggregateTrackArtist = {
  _count: TrackArtistCountAggregateOutputType | null
  _min: TrackArtistMinAggregateOutputType | null
  _max: TrackArtistMaxAggregateOutputType | null
}

export type TrackArtistMinAggregateOutputType = {
  trackId: string | null
  artistId: string | null
  role: $Enums.ArtistRole | null
}

export type TrackArtistMaxAggregateOutputType = {
  trackId: string | null
  artistId: string | null
  role: $Enums.ArtistRole | null
}

export type TrackArtistCountAggregateOutputType = {
  trackId: number
  artistId: number
  role: number
  _all: number
}


export type TrackArtistMinAggregateInputType = {
  trackId?: true
  artistId?: true
  role?: true
}

export type TrackArtistMaxAggregateInputType = {
  trackId?: true
  artistId?: true
  role?: true
}

export type TrackArtistCountAggregateInputType = {
  trackId?: true
  artistId?: true
  role?: true
  _all?: true
}

export type TrackArtistAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which TrackArtist to aggregate.
   */
  where?: Prisma.TrackArtistWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of TrackArtists to fetch.
   */
  orderBy?: Prisma.TrackArtistOrderByWithRelationInput | Prisma.TrackArtistOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.TrackArtistWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` TrackArtists from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` TrackArtists.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned TrackArtists
  **/
  _count?: true | TrackArtistCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: TrackArtistMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: TrackArtistMaxAggregateInputType
}

export type GetTrackArtistAggregateType<T extends TrackArtistAggregateArgs> = {
      [P in keyof T & keyof AggregateTrackArtist]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateTrackArtist[P]>
    : Prisma.GetScalarType<T[P], AggregateTrackArtist[P]>
}




export type TrackArtistGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.TrackArtistWhereInput
  orderBy?: Prisma.TrackArtistOrderByWithAggregationInput | Prisma.TrackArtistOrderByWithAggregationInput[]
  by: Prisma.TrackArtistScalarFieldEnum[] | Prisma.TrackArtistScalarFieldEnum
  having?: Prisma.TrackArtistScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: TrackArtistCountAggregateInputType | true
  _min?: TrackArtistMinAggregateInputType
  _max?: TrackArtistMaxAggregateInputType
}

export type TrackArtistGroupByOutputType = {
  trackId: string
  artistId: string
  role: $Enums.ArtistRole
  _count: TrackArtistCountAggregateOutputType | null
  _min: TrackArtistMinAggregateOutputType | null
  _max: TrackArtistMaxAggregateOutputType | null
}

type GetTrackArtistGroupByPayload<T extends TrackArtistGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<TrackArtistGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof TrackArtistGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], TrackArtistGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], TrackArtistGroupByOutputType[P]>
      }
    >
  > 



export type TrackArtistWhereInput = {
  AND?: Prisma.TrackArtistWhereInput | Prisma.TrackArtistWhereInput[]
  OR?: Prisma.TrackArtistWhereInput[]
  NOT?: Prisma.TrackArtistWhereInput | Prisma.TrackArtistWhereInput[]
  trackId?: Prisma.StringFilter<"TrackArtist"> | string
  artistId?: Prisma.StringFilter<"TrackArtist"> | string
  role?: Prisma.EnumArtistRoleFilter<"TrackArtist"> | $Enums.ArtistRole
  track?: Prisma.XOR<Prisma.TrackScalarRelationFilter, Prisma.TrackWhereInput>
  artist?: Prisma.XOR<Prisma.ArtistScalarRelationFilter, Prisma.ArtistWhereInput>
}

export type TrackArtistOrderByWithRelationInput = {
  trackId?: Prisma.SortOrder
  artistId?: Prisma.SortOrder
  role?: Prisma.SortOrder
  track?: Prisma.TrackOrderByWithRelationInput
  artist?: Prisma.ArtistOrderByWithRelationInput
}

export type TrackArtistWhereUniqueInput = Prisma.AtLeast<{
  trackId_artistId?: Prisma.TrackArtistTrackIdArtistIdCompoundUniqueInput
  AND?: Prisma.TrackArtistWhereInput | Prisma.TrackArtistWhereInput[]
  OR?: Prisma.TrackArtistWhereInput[]
  NOT?: Prisma.TrackArtistWhereInput | Prisma.TrackArtistWhereInput[]
  trackId?: Prisma.StringFilter<"TrackArtist"> | string
  artistId?: Prisma.StringFilter<"TrackArtist"> | string
  role?: Prisma.EnumArtistRoleFilter<"TrackArtist"> | $Enums.ArtistRole
  track?: Prisma.XOR<Prisma.TrackScalarRelationFilter, Prisma.TrackWhereInput>
  artist?: Prisma.XOR<Prisma.ArtistScalarRelationFilter, Prisma.ArtistWhereInput>
}, "trackId_artistId">

export type TrackArtistOrderByWithAggregationInput = {
  trackId?: Prisma.SortOrder
  artistId?: Prisma.SortOrder
  role?: Prisma.SortOrder
  _count?: Prisma.TrackArtistCountOrderByAggregateInput
  _max?: Prisma.TrackArtistMaxOrderByAggregateInput
  _min?: Prisma.TrackArtistMinOrderByAggregateInput
}

export type TrackArtistScalarWhereWithAggregatesInput = {
  AND?: Prisma.TrackArtistScalarWhereWithAggregatesInput | Prisma.TrackArtistScalarWhereWithAggregatesInput[]
  OR?: Prisma.TrackArtistScalarWhereWithAggregatesInput[]
  NOT?: Prisma.TrackArtistScalarWhereWithAggregatesInput | Prisma.TrackArtistScalarWhereWithAggregatesInput[]
  trackId?: Prisma.StringWithAggregatesFilter<"TrackArtist"> | string
  artistId?: Prisma.StringWithAggregatesFilter<"TrackArtist"> | string
  role?: Prisma.EnumArtistRoleWithAggregatesFilter<"TrackArtist"> | $Enums.ArtistRole
}

export type TrackArtistCreateInput = {
  role: $Enums.ArtistRole
  track: Prisma.TrackCreateNestedOneWithoutArtistsInput
  artist: Prisma.ArtistCreateNestedOneWithoutTracksInput
}

export type TrackArtistUncheckedCreateInput = {
  trackId: string
  artistId: string
  role: $Enums.ArtistRole
}

export type TrackArtistUpdateInput = {
  role?: Prisma.EnumArtistRoleFieldUpdateOperationsInput | $Enums.ArtistRole
  track?: Prisma.TrackUpdateOneRequiredWithoutArtistsNestedInput
  artist?: Prisma.ArtistUpdateOneRequiredWithoutTracksNestedInput
}

export type TrackArtistUncheckedUpdateInput = {
  trackId?: Prisma.StringFieldUpdateOperationsInput | string
  artistId?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.EnumArtistRoleFieldUpdateOperationsInput | $Enums.ArtistRole
}

export type TrackArtistCreateManyInput = {
  trackId: string
  artistId: string
  role: $Enums.ArtistRole
}

export type TrackArtistUpdateManyMutationInput = {
  role?: Prisma.EnumArtistRoleFieldUpdateOperationsInput | $Enums.ArtistRole
}

export type TrackArtistUncheckedUpdateManyInput = {
  trackId?: Prisma.StringFieldUpdateOperationsInput | string
  artistId?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.EnumArtistRoleFieldUpdateOperationsInput | $Enums.ArtistRole
}

export type TrackArtistListRelationFilter = {
  every?: Prisma.TrackArtistWhereInput
  some?: Prisma.TrackArtistWhereInput
  none?: Prisma.TrackArtistWhereInput
}

export type TrackArtistOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type TrackArtistTrackIdArtistIdCompoundUniqueInput = {
  trackId: string
  artistId: string
}

export type TrackArtistCountOrderByAggregateInput = {
  trackId?: Prisma.SortOrder
  artistId?: Prisma.SortOrder
  role?: Prisma.SortOrder
}

export type TrackArtistMaxOrderByAggregateInput = {
  trackId?: Prisma.SortOrder
  artistId?: Prisma.SortOrder
  role?: Prisma.SortOrder
}

export type TrackArtistMinOrderByAggregateInput = {
  trackId?: Prisma.SortOrder
  artistId?: Prisma.SortOrder
  role?: Prisma.SortOrder
}

export type TrackArtistCreateNestedManyWithoutArtistInput = {
  create?: Prisma.XOR<Prisma.TrackArtistCreateWithoutArtistInput, Prisma.TrackArtistUncheckedCreateWithoutArtistInput> | Prisma.TrackArtistCreateWithoutArtistInput[] | Prisma.TrackArtistUncheckedCreateWithoutArtistInput[]
  connectOrCreate?: Prisma.TrackArtistCreateOrConnectWithoutArtistInput | Prisma.TrackArtistCreateOrConnectWithoutArtistInput[]
  createMany?: Prisma.TrackArtistCreateManyArtistInputEnvelope
  connect?: Prisma.TrackArtistWhereUniqueInput | Prisma.TrackArtistWhereUniqueInput[]
}

export type TrackArtistUncheckedCreateNestedManyWithoutArtistInput = {
  create?: Prisma.XOR<Prisma.TrackArtistCreateWithoutArtistInput, Prisma.TrackArtistUncheckedCreateWithoutArtistInput> | Prisma.TrackArtistCreateWithoutArtistInput[] | Prisma.TrackArtistUncheckedCreateWithoutArtistInput[]
  connectOrCreate?: Prisma.TrackArtistCreateOrConnectWithoutArtistInput | Prisma.TrackArtistCreateOrConnectWithoutArtistInput[]
  createMany?: Prisma.TrackArtistCreateManyArtistInputEnvelope
  connect?: Prisma.TrackArtistWhereUniqueInput | Prisma.TrackArtistWhereUniqueInput[]
}

export type TrackArtistUpdateManyWithoutArtistNestedInput = {
  create?: Prisma.XOR<Prisma.TrackArtistCreateWithoutArtistInput, Prisma.TrackArtistUncheckedCreateWithoutArtistInput> | Prisma.TrackArtistCreateWithoutArtistInput[] | Prisma.TrackArtistUncheckedCreateWithoutArtistInput[]
  connectOrCreate?: Prisma.TrackArtistCreateOrConnectWithoutArtistInput | Prisma.TrackArtistCreateOrConnectWithoutArtistInput[]
  upsert?: Prisma.TrackArtistUpsertWithWhereUniqueWithoutArtistInput | Prisma.TrackArtistUpsertWithWhereUniqueWithoutArtistInput[]
  createMany?: Prisma.TrackArtistCreateManyArtistInputEnvelope
  set?: Prisma.TrackArtistWhereUniqueInput | Prisma.TrackArtistWhereUniqueInput[]
  disconnect?: Prisma.TrackArtistWhereUniqueInput | Prisma.TrackArtistWhereUniqueInput[]
  delete?: Prisma.TrackArtistWhereUniqueInput | Prisma.TrackArtistWhereUniqueInput[]
  connect?: Prisma.TrackArtistWhereUniqueInput | Prisma.TrackArtistWhereUniqueInput[]
  update?: Prisma.TrackArtistUpdateWithWhereUniqueWithoutArtistInput | Prisma.TrackArtistUpdateWithWhereUniqueWithoutArtistInput[]
  updateMany?: Prisma.TrackArtistUpdateManyWithWhereWithoutArtistInput | Prisma.TrackArtistUpdateManyWithWhereWithoutArtistInput[]
  deleteMany?: Prisma.TrackArtistScalarWhereInput | Prisma.TrackArtistScalarWhereInput[]
}

export type TrackArtistUncheckedUpdateManyWithoutArtistNestedInput = {
  create?: Prisma.XOR<Prisma.TrackArtistCreateWithoutArtistInput, Prisma.TrackArtistUncheckedCreateWithoutArtistInput> | Prisma.TrackArtistCreateWithoutArtistInput[] | Prisma.TrackArtistUncheckedCreateWithoutArtistInput[]
  connectOrCreate?: Prisma.TrackArtistCreateOrConnectWithoutArtistInput | Prisma.TrackArtistCreateOrConnectWithoutArtistInput[]
  upsert?: Prisma.TrackArtistUpsertWithWhereUniqueWithoutArtistInput | Prisma.TrackArtistUpsertWithWhereUniqueWithoutArtistInput[]
  createMany?: Prisma.TrackArtistCreateManyArtistInputEnvelope
  set?: Prisma.TrackArtistWhereUniqueInput | Prisma.TrackArtistWhereUniqueInput[]
  disconnect?: Prisma.TrackArtistWhereUniqueInput | Prisma.TrackArtistWhereUniqueInput[]
  delete?: Prisma.TrackArtistWhereUniqueInput | Prisma.TrackArtistWhereUniqueInput[]
  connect?: Prisma.TrackArtistWhereUniqueInput | Prisma.TrackArtistWhereUniqueInput[]
  update?: Prisma.TrackArtistUpdateWithWhereUniqueWithoutArtistInput | Prisma.TrackArtistUpdateWithWhereUniqueWithoutArtistInput[]
  updateMany?: Prisma.TrackArtistUpdateManyWithWhereWithoutArtistInput | Prisma.TrackArtistUpdateManyWithWhereWithoutArtistInput[]
  deleteMany?: Prisma.TrackArtistScalarWhereInput | Prisma.TrackArtistScalarWhereInput[]
}

export type TrackArtistCreateNestedManyWithoutTrackInput = {
  create?: Prisma.XOR<Prisma.TrackArtistCreateWithoutTrackInput, Prisma.TrackArtistUncheckedCreateWithoutTrackInput> | Prisma.TrackArtistCreateWithoutTrackInput[] | Prisma.TrackArtistUncheckedCreateWithoutTrackInput[]
  connectOrCreate?: Prisma.TrackArtistCreateOrConnectWithoutTrackInput | Prisma.TrackArtistCreateOrConnectWithoutTrackInput[]
  createMany?: Prisma.TrackArtistCreateManyTrackInputEnvelope
  connect?: Prisma.TrackArtistWhereUniqueInput | Prisma.TrackArtistWhereUniqueInput[]
}

export type TrackArtistUncheckedCreateNestedManyWithoutTrackInput = {
  create?: Prisma.XOR<Prisma.TrackArtistCreateWithoutTrackInput, Prisma.TrackArtistUncheckedCreateWithoutTrackInput> | Prisma.TrackArtistCreateWithoutTrackInput[] | Prisma.TrackArtistUncheckedCreateWithoutTrackInput[]
  connectOrCreate?: Prisma.TrackArtistCreateOrConnectWithoutTrackInput | Prisma.TrackArtistCreateOrConnectWithoutTrackInput[]
  createMany?: Prisma.TrackArtistCreateManyTrackInputEnvelope
  connect?: Prisma.TrackArtistWhereUniqueInput | Prisma.TrackArtistWhereUniqueInput[]
}

export type TrackArtistUpdateManyWithoutTrackNestedInput = {
  create?: Prisma.XOR<Prisma.TrackArtistCreateWithoutTrackInput, Prisma.TrackArtistUncheckedCreateWithoutTrackInput> | Prisma.TrackArtistCreateWithoutTrackInput[] | Prisma.TrackArtistUncheckedCreateWithoutTrackInput[]
  connectOrCreate?: Prisma.TrackArtistCreateOrConnectWithoutTrackInput | Prisma.TrackArtistCreateOrConnectWithoutTrackInput[]
  upsert?: Prisma.TrackArtistUpsertWithWhereUniqueWithoutTrackInput | Prisma.TrackArtistUpsertWithWhereUniqueWithoutTrackInput[]
  createMany?: Prisma.TrackArtistCreateManyTrackInputEnvelope
  set?: Prisma.TrackArtistWhereUniqueInput | Prisma.TrackArtistWhereUniqueInput[]
  disconnect?: Prisma.TrackArtistWhereUniqueInput | Prisma.TrackArtistWhereUniqueInput[]
  delete?: Prisma.TrackArtistWhereUniqueInput | Prisma.TrackArtistWhereUniqueInput[]
  connect?: Prisma.TrackArtistWhereUniqueInput | Prisma.TrackArtistWhereUniqueInput[]
  update?: Prisma.TrackArtistUpdateWithWhereUniqueWithoutTrackInput | Prisma.TrackArtistUpdateWithWhereUniqueWithoutTrackInput[]
  updateMany?: Prisma.TrackArtistUpdateManyWithWhereWithoutTrackInput | Prisma.TrackArtistUpdateManyWithWhereWithoutTrackInput[]
  deleteMany?: Prisma.TrackArtistScalarWhereInput | Prisma.TrackArtistScalarWhereInput[]
}

export type TrackArtistUncheckedUpdateManyWithoutTrackNestedInput = {
  create?: Prisma.XOR<Prisma.TrackArtistCreateWithoutTrackInput, Prisma.TrackArtistUncheckedCreateWithoutTrackInput> | Prisma.TrackArtistCreateWithoutTrackInput[] | Prisma.TrackArtistUncheckedCreateWithoutTrackInput[]
  connectOrCreate?: Prisma.TrackArtistCreateOrConnectWithoutTrackInput | Prisma.TrackArtistCreateOrConnectWithoutTrackInput[]
  upsert?: Prisma.TrackArtistUpsertWithWhereUniqueWithoutTrackInput | Prisma.TrackArtistUpsertWithWhereUniqueWithoutTrackInput[]
  createMany?: Prisma.TrackArtistCreateManyTrackInputEnvelope
  set?: Prisma.TrackArtistWhereUniqueInput | Prisma.TrackArtistWhereUniqueInput[]
  disconnect?: Prisma.TrackArtistWhereUniqueInput | Prisma.TrackArtistWhereUniqueInput[]
  delete?: Prisma.TrackArtistWhereUniqueInput | Prisma.TrackArtistWhereUniqueInput[]
  connect?: Prisma.TrackArtistWhereUniqueInput | Prisma.TrackArtistWhereUniqueInput[]
  update?: Prisma.TrackArtistUpdateWithWhereUniqueWithoutTrackInput | Prisma.TrackArtistUpdateWithWhereUniqueWithoutTrackInput[]
  updateMany?: Prisma.TrackArtistUpdateManyWithWhereWithoutTrackInput | Prisma.TrackArtistUpdateManyWithWhereWithoutTrackInput[]
  deleteMany?: Prisma.TrackArtistScalarWhereInput | Prisma.TrackArtistScalarWhereInput[]
}

export type TrackArtistCreateWithoutArtistInput = {
  role: $Enums.ArtistRole
  track: Prisma.TrackCreateNestedOneWithoutArtistsInput
}

export type TrackArtistUncheckedCreateWithoutArtistInput = {
  trackId: string
  role: $Enums.ArtistRole
}

export type TrackArtistCreateOrConnectWithoutArtistInput = {
  where: Prisma.TrackArtistWhereUniqueInput
  create: Prisma.XOR<Prisma.TrackArtistCreateWithoutArtistInput, Prisma.TrackArtistUncheckedCreateWithoutArtistInput>
}

export type TrackArtistCreateManyArtistInputEnvelope = {
  data: Prisma.TrackArtistCreateManyArtistInput | Prisma.TrackArtistCreateManyArtistInput[]
  skipDuplicates?: boolean
}

export type TrackArtistUpsertWithWhereUniqueWithoutArtistInput = {
  where: Prisma.TrackArtistWhereUniqueInput
  update: Prisma.XOR<Prisma.TrackArtistUpdateWithoutArtistInput, Prisma.TrackArtistUncheckedUpdateWithoutArtistInput>
  create: Prisma.XOR<Prisma.TrackArtistCreateWithoutArtistInput, Prisma.TrackArtistUncheckedCreateWithoutArtistInput>
}

export type TrackArtistUpdateWithWhereUniqueWithoutArtistInput = {
  where: Prisma.TrackArtistWhereUniqueInput
  data: Prisma.XOR<Prisma.TrackArtistUpdateWithoutArtistInput, Prisma.TrackArtistUncheckedUpdateWithoutArtistInput>
}

export type TrackArtistUpdateManyWithWhereWithoutArtistInput = {
  where: Prisma.TrackArtistScalarWhereInput
  data: Prisma.XOR<Prisma.TrackArtistUpdateManyMutationInput, Prisma.TrackArtistUncheckedUpdateManyWithoutArtistInput>
}

export type TrackArtistScalarWhereInput = {
  AND?: Prisma.TrackArtistScalarWhereInput | Prisma.TrackArtistScalarWhereInput[]
  OR?: Prisma.TrackArtistScalarWhereInput[]
  NOT?: Prisma.TrackArtistScalarWhereInput | Prisma.TrackArtistScalarWhereInput[]
  trackId?: Prisma.StringFilter<"TrackArtist"> | string
  artistId?: Prisma.StringFilter<"TrackArtist"> | string
  role?: Prisma.EnumArtistRoleFilter<"TrackArtist"> | $Enums.ArtistRole
}

export type TrackArtistCreateWithoutTrackInput = {
  role: $Enums.ArtistRole
  artist: Prisma.ArtistCreateNestedOneWithoutTracksInput
}

export type TrackArtistUncheckedCreateWithoutTrackInput = {
  artistId: string
  role: $Enums.ArtistRole
}

export type TrackArtistCreateOrConnectWithoutTrackInput = {
  where: Prisma.TrackArtistWhereUniqueInput
  create: Prisma.XOR<Prisma.TrackArtistCreateWithoutTrackInput, Prisma.TrackArtistUncheckedCreateWithoutTrackInput>
}

export type TrackArtistCreateManyTrackInputEnvelope = {
  data: Prisma.TrackArtistCreateManyTrackInput | Prisma.TrackArtistCreateManyTrackInput[]
  skipDuplicates?: boolean
}

export type TrackArtistUpsertWithWhereUniqueWithoutTrackInput = {
  where: Prisma.TrackArtistWhereUniqueInput
  update: Prisma.XOR<Prisma.TrackArtistUpdateWithoutTrackInput, Prisma.TrackArtistUncheckedUpdateWithoutTrackInput>
  create: Prisma.XOR<Prisma.TrackArtistCreateWithoutTrackInput, Prisma.TrackArtistUncheckedCreateWithoutTrackInput>
}

export type TrackArtistUpdateWithWhereUniqueWithoutTrackInput = {
  where: Prisma.TrackArtistWhereUniqueInput
  data: Prisma.XOR<Prisma.TrackArtistUpdateWithoutTrackInput, Prisma.TrackArtistUncheckedUpdateWithoutTrackInput>
}

export type TrackArtistUpdateManyWithWhereWithoutTrackInput = {
  where: Prisma.TrackArtistScalarWhereInput
  data: Prisma.XOR<Prisma.TrackArtistUpdateManyMutationInput, Prisma.TrackArtistUncheckedUpdateManyWithoutTrackInput>
}

export type TrackArtistCreateManyArtistInput = {
  trackId: string
  role: $Enums.ArtistRole
}

export type TrackArtistUpdateWithoutArtistInput = {
  role?: Prisma.EnumArtistRoleFieldUpdateOperationsInput | $Enums.ArtistRole
  track?: Prisma.TrackUpdateOneRequiredWithoutArtistsNestedInput
}

export type TrackArtistUncheckedUpdateWithoutArtistInput = {
  trackId?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.EnumArtistRoleFieldUpdateOperationsInput | $Enums.ArtistRole
}

export type TrackArtistUncheckedUpdateManyWithoutArtistInput = {
  trackId?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.EnumArtistRoleFieldUpdateOperationsInput | $Enums.ArtistRole
}

export type TrackArtistCreateManyTrackInput = {
  artistId: string
  role: $Enums.ArtistRole
}

export type TrackArtistUpdateWithoutTrackInput = {
  role?: Prisma.EnumArtistRoleFieldUpdateOperationsInput | $Enums.ArtistRole
  artist?: Prisma.ArtistUpdateOneRequiredWithoutTracksNestedInput
}

export type TrackArtistUncheckedUpdateWithoutTrackInput = {
  artistId?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.EnumArtistRoleFieldUpdateOperationsInput | $Enums.ArtistRole
}

export type TrackArtistUncheckedUpdateManyWithoutTrackInput = {
  artistId?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.EnumArtistRoleFieldUpdateOperationsInput | $Enums.ArtistRole
}



export type TrackArtistSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  trackId?: boolean
  artistId?: boolean
  role?: boolean
  track?: boolean | Prisma.TrackDefaultArgs<ExtArgs>
  artist?: boolean | Prisma.ArtistDefaultArgs<ExtArgs>
}, ExtArgs["result"]["trackArtist"]>

export type TrackArtistSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  trackId?: boolean
  artistId?: boolean
  role?: boolean
  track?: boolean | Prisma.TrackDefaultArgs<ExtArgs>
  artist?: boolean | Prisma.ArtistDefaultArgs<ExtArgs>
}, ExtArgs["result"]["trackArtist"]>

export type TrackArtistSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  trackId?: boolean
  artistId?: boolean
  role?: boolean
  track?: boolean | Prisma.TrackDefaultArgs<ExtArgs>
  artist?: boolean | Prisma.ArtistDefaultArgs<ExtArgs>
}, ExtArgs["result"]["trackArtist"]>

export type TrackArtistSelectScalar = {
  trackId?: boolean
  artistId?: boolean
  role?: boolean
}

export type TrackArtistOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"trackId" | "artistId" | "role", ExtArgs["result"]["trackArtist"]>
export type TrackArtistInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  track?: boolean | Prisma.TrackDefaultArgs<ExtArgs>
  artist?: boolean | Prisma.ArtistDefaultArgs<ExtArgs>
}
export type TrackArtistIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  track?: boolean | Prisma.TrackDefaultArgs<ExtArgs>
  artist?: boolean | Prisma.ArtistDefaultArgs<ExtArgs>
}
export type TrackArtistIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  track?: boolean | Prisma.TrackDefaultArgs<ExtArgs>
  artist?: boolean | Prisma.ArtistDefaultArgs<ExtArgs>
}

export type $TrackArtistPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "TrackArtist"
  objects: {
    track: Prisma.$TrackPayload<ExtArgs>
    artist: Prisma.$ArtistPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    trackId: string
    artistId: string
    role: $Enums.ArtistRole
  }, ExtArgs["result"]["trackArtist"]>
  composites: {}
}

export type TrackArtistGetPayload<S extends boolean | null | undefined | TrackArtistDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$TrackArtistPayload, S>

export type TrackArtistCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<TrackArtistFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: TrackArtistCountAggregateInputType | true
  }

export interface TrackArtistDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['TrackArtist'], meta: { name: 'TrackArtist' } }
  /**
   * Find zero or one TrackArtist that matches the filter.
   * @param {TrackArtistFindUniqueArgs} args - Arguments to find a TrackArtist
   * @example
   * // Get one TrackArtist
   * const trackArtist = await prisma.trackArtist.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends TrackArtistFindUniqueArgs>(args: Prisma.SelectSubset<T, TrackArtistFindUniqueArgs<ExtArgs>>): Prisma.Prisma__TrackArtistClient<runtime.Types.Result.GetResult<Prisma.$TrackArtistPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one TrackArtist that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {TrackArtistFindUniqueOrThrowArgs} args - Arguments to find a TrackArtist
   * @example
   * // Get one TrackArtist
   * const trackArtist = await prisma.trackArtist.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends TrackArtistFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, TrackArtistFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__TrackArtistClient<runtime.Types.Result.GetResult<Prisma.$TrackArtistPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first TrackArtist that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackArtistFindFirstArgs} args - Arguments to find a TrackArtist
   * @example
   * // Get one TrackArtist
   * const trackArtist = await prisma.trackArtist.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends TrackArtistFindFirstArgs>(args?: Prisma.SelectSubset<T, TrackArtistFindFirstArgs<ExtArgs>>): Prisma.Prisma__TrackArtistClient<runtime.Types.Result.GetResult<Prisma.$TrackArtistPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first TrackArtist that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackArtistFindFirstOrThrowArgs} args - Arguments to find a TrackArtist
   * @example
   * // Get one TrackArtist
   * const trackArtist = await prisma.trackArtist.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends TrackArtistFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, TrackArtistFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__TrackArtistClient<runtime.Types.Result.GetResult<Prisma.$TrackArtistPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more TrackArtists that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackArtistFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all TrackArtists
   * const trackArtists = await prisma.trackArtist.findMany()
   * 
   * // Get first 10 TrackArtists
   * const trackArtists = await prisma.trackArtist.findMany({ take: 10 })
   * 
   * // Only select the `trackId`
   * const trackArtistWithTrackIdOnly = await prisma.trackArtist.findMany({ select: { trackId: true } })
   * 
   */
  findMany<T extends TrackArtistFindManyArgs>(args?: Prisma.SelectSubset<T, TrackArtistFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TrackArtistPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a TrackArtist.
   * @param {TrackArtistCreateArgs} args - Arguments to create a TrackArtist.
   * @example
   * // Create one TrackArtist
   * const TrackArtist = await prisma.trackArtist.create({
   *   data: {
   *     // ... data to create a TrackArtist
   *   }
   * })
   * 
   */
  create<T extends TrackArtistCreateArgs>(args: Prisma.SelectSubset<T, TrackArtistCreateArgs<ExtArgs>>): Prisma.Prisma__TrackArtistClient<runtime.Types.Result.GetResult<Prisma.$TrackArtistPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many TrackArtists.
   * @param {TrackArtistCreateManyArgs} args - Arguments to create many TrackArtists.
   * @example
   * // Create many TrackArtists
   * const trackArtist = await prisma.trackArtist.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends TrackArtistCreateManyArgs>(args?: Prisma.SelectSubset<T, TrackArtistCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many TrackArtists and returns the data saved in the database.
   * @param {TrackArtistCreateManyAndReturnArgs} args - Arguments to create many TrackArtists.
   * @example
   * // Create many TrackArtists
   * const trackArtist = await prisma.trackArtist.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many TrackArtists and only return the `trackId`
   * const trackArtistWithTrackIdOnly = await prisma.trackArtist.createManyAndReturn({
   *   select: { trackId: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends TrackArtistCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, TrackArtistCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TrackArtistPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a TrackArtist.
   * @param {TrackArtistDeleteArgs} args - Arguments to delete one TrackArtist.
   * @example
   * // Delete one TrackArtist
   * const TrackArtist = await prisma.trackArtist.delete({
   *   where: {
   *     // ... filter to delete one TrackArtist
   *   }
   * })
   * 
   */
  delete<T extends TrackArtistDeleteArgs>(args: Prisma.SelectSubset<T, TrackArtistDeleteArgs<ExtArgs>>): Prisma.Prisma__TrackArtistClient<runtime.Types.Result.GetResult<Prisma.$TrackArtistPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one TrackArtist.
   * @param {TrackArtistUpdateArgs} args - Arguments to update one TrackArtist.
   * @example
   * // Update one TrackArtist
   * const trackArtist = await prisma.trackArtist.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends TrackArtistUpdateArgs>(args: Prisma.SelectSubset<T, TrackArtistUpdateArgs<ExtArgs>>): Prisma.Prisma__TrackArtistClient<runtime.Types.Result.GetResult<Prisma.$TrackArtistPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more TrackArtists.
   * @param {TrackArtistDeleteManyArgs} args - Arguments to filter TrackArtists to delete.
   * @example
   * // Delete a few TrackArtists
   * const { count } = await prisma.trackArtist.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends TrackArtistDeleteManyArgs>(args?: Prisma.SelectSubset<T, TrackArtistDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more TrackArtists.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackArtistUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many TrackArtists
   * const trackArtist = await prisma.trackArtist.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends TrackArtistUpdateManyArgs>(args: Prisma.SelectSubset<T, TrackArtistUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more TrackArtists and returns the data updated in the database.
   * @param {TrackArtistUpdateManyAndReturnArgs} args - Arguments to update many TrackArtists.
   * @example
   * // Update many TrackArtists
   * const trackArtist = await prisma.trackArtist.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more TrackArtists and only return the `trackId`
   * const trackArtistWithTrackIdOnly = await prisma.trackArtist.updateManyAndReturn({
   *   select: { trackId: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends TrackArtistUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, TrackArtistUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TrackArtistPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one TrackArtist.
   * @param {TrackArtistUpsertArgs} args - Arguments to update or create a TrackArtist.
   * @example
   * // Update or create a TrackArtist
   * const trackArtist = await prisma.trackArtist.upsert({
   *   create: {
   *     // ... data to create a TrackArtist
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the TrackArtist we want to update
   *   }
   * })
   */
  upsert<T extends TrackArtistUpsertArgs>(args: Prisma.SelectSubset<T, TrackArtistUpsertArgs<ExtArgs>>): Prisma.Prisma__TrackArtistClient<runtime.Types.Result.GetResult<Prisma.$TrackArtistPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of TrackArtists.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackArtistCountArgs} args - Arguments to filter TrackArtists to count.
   * @example
   * // Count the number of TrackArtists
   * const count = await prisma.trackArtist.count({
   *   where: {
   *     // ... the filter for the TrackArtists we want to count
   *   }
   * })
  **/
  count<T extends TrackArtistCountArgs>(
    args?: Prisma.Subset<T, TrackArtistCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], TrackArtistCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a TrackArtist.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackArtistAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends TrackArtistAggregateArgs>(args: Prisma.Subset<T, TrackArtistAggregateArgs>): Prisma.PrismaPromise<GetTrackArtistAggregateType<T>>

  /**
   * Group by TrackArtist.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackArtistGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends TrackArtistGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: TrackArtistGroupByArgs['orderBy'] }
      : { orderBy?: TrackArtistGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, TrackArtistGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetTrackArtistGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the TrackArtist model
 */
readonly fields: TrackArtistFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for TrackArtist.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__TrackArtistClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  track<T extends Prisma.TrackDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.TrackDefaultArgs<ExtArgs>>): Prisma.Prisma__TrackClient<runtime.Types.Result.GetResult<Prisma.$TrackPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  artist<T extends Prisma.ArtistDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.ArtistDefaultArgs<ExtArgs>>): Prisma.Prisma__ArtistClient<runtime.Types.Result.GetResult<Prisma.$ArtistPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the TrackArtist model
 */
export interface TrackArtistFieldRefs {
  readonly trackId: Prisma.FieldRef<"TrackArtist", 'String'>
  readonly artistId: Prisma.FieldRef<"TrackArtist", 'String'>
  readonly role: Prisma.FieldRef<"TrackArtist", 'ArtistRole'>
}
    

// Custom InputTypes
/**
 * TrackArtist findUnique
 */
export type TrackArtistFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackArtist
   */
  select?: Prisma.TrackArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackArtist
   */
  omit?: Prisma.TrackArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackArtistInclude<ExtArgs> | null
  /**
   * Filter, which TrackArtist to fetch.
   */
  where: Prisma.TrackArtistWhereUniqueInput
}

/**
 * TrackArtist findUniqueOrThrow
 */
export type TrackArtistFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackArtist
   */
  select?: Prisma.TrackArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackArtist
   */
  omit?: Prisma.TrackArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackArtistInclude<ExtArgs> | null
  /**
   * Filter, which TrackArtist to fetch.
   */
  where: Prisma.TrackArtistWhereUniqueInput
}

/**
 * TrackArtist findFirst
 */
export type TrackArtistFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackArtist
   */
  select?: Prisma.TrackArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackArtist
   */
  omit?: Prisma.TrackArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackArtistInclude<ExtArgs> | null
  /**
   * Filter, which TrackArtist to fetch.
   */
  where?: Prisma.TrackArtistWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of TrackArtists to fetch.
   */
  orderBy?: Prisma.TrackArtistOrderByWithRelationInput | Prisma.TrackArtistOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for TrackArtists.
   */
  cursor?: Prisma.TrackArtistWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` TrackArtists from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` TrackArtists.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of TrackArtists.
   */
  distinct?: Prisma.TrackArtistScalarFieldEnum | Prisma.TrackArtistScalarFieldEnum[]
}

/**
 * TrackArtist findFirstOrThrow
 */
export type TrackArtistFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackArtist
   */
  select?: Prisma.TrackArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackArtist
   */
  omit?: Prisma.TrackArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackArtistInclude<ExtArgs> | null
  /**
   * Filter, which TrackArtist to fetch.
   */
  where?: Prisma.TrackArtistWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of TrackArtists to fetch.
   */
  orderBy?: Prisma.TrackArtistOrderByWithRelationInput | Prisma.TrackArtistOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for TrackArtists.
   */
  cursor?: Prisma.TrackArtistWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` TrackArtists from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` TrackArtists.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of TrackArtists.
   */
  distinct?: Prisma.TrackArtistScalarFieldEnum | Prisma.TrackArtistScalarFieldEnum[]
}

/**
 * TrackArtist findMany
 */
export type TrackArtistFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackArtist
   */
  select?: Prisma.TrackArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackArtist
   */
  omit?: Prisma.TrackArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackArtistInclude<ExtArgs> | null
  /**
   * Filter, which TrackArtists to fetch.
   */
  where?: Prisma.TrackArtistWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of TrackArtists to fetch.
   */
  orderBy?: Prisma.TrackArtistOrderByWithRelationInput | Prisma.TrackArtistOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing TrackArtists.
   */
  cursor?: Prisma.TrackArtistWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` TrackArtists from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` TrackArtists.
   */
  skip?: number
  distinct?: Prisma.TrackArtistScalarFieldEnum | Prisma.TrackArtistScalarFieldEnum[]
}

/**
 * TrackArtist create
 */
export type TrackArtistCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackArtist
   */
  select?: Prisma.TrackArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackArtist
   */
  omit?: Prisma.TrackArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackArtistInclude<ExtArgs> | null
  /**
   * The data needed to create a TrackArtist.
   */
  data: Prisma.XOR<Prisma.TrackArtistCreateInput, Prisma.TrackArtistUncheckedCreateInput>
}

/**
 * TrackArtist createMany
 */
export type TrackArtistCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many TrackArtists.
   */
  data: Prisma.TrackArtistCreateManyInput | Prisma.TrackArtistCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * TrackArtist createManyAndReturn
 */
export type TrackArtistCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackArtist
   */
  select?: Prisma.TrackArtistSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the TrackArtist
   */
  omit?: Prisma.TrackArtistOmit<ExtArgs> | null
  /**
   * The data used to create many TrackArtists.
   */
  data: Prisma.TrackArtistCreateManyInput | Prisma.TrackArtistCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackArtistIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * TrackArtist update
 */
export type TrackArtistUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackArtist
   */
  select?: Prisma.TrackArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackArtist
   */
  omit?: Prisma.TrackArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackArtistInclude<ExtArgs> | null
  /**
   * The data needed to update a TrackArtist.
   */
  data: Prisma.XOR<Prisma.TrackArtistUpdateInput, Prisma.TrackArtistUncheckedUpdateInput>
  /**
   * Choose, which TrackArtist to update.
   */
  where: Prisma.TrackArtistWhereUniqueInput
}

/**
 * TrackArtist updateMany
 */
export type TrackArtistUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update TrackArtists.
   */
  data: Prisma.XOR<Prisma.TrackArtistUpdateManyMutationInput, Prisma.TrackArtistUncheckedUpdateManyInput>
  /**
   * Filter which TrackArtists to update
   */
  where?: Prisma.TrackArtistWhereInput
  /**
   * Limit how many TrackArtists to update.
   */
  limit?: number
}

/**
 * TrackArtist updateManyAndReturn
 */
export type TrackArtistUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackArtist
   */
  select?: Prisma.TrackArtistSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the TrackArtist
   */
  omit?: Prisma.TrackArtistOmit<ExtArgs> | null
  /**
   * The data used to update TrackArtists.
   */
  data: Prisma.XOR<Prisma.TrackArtistUpdateManyMutationInput, Prisma.TrackArtistUncheckedUpdateManyInput>
  /**
   * Filter which TrackArtists to update
   */
  where?: Prisma.TrackArtistWhereInput
  /**
   * Limit how many TrackArtists to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackArtistIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * TrackArtist upsert
 */
export type TrackArtistUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackArtist
   */
  select?: Prisma.TrackArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackArtist
   */
  omit?: Prisma.TrackArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackArtistInclude<ExtArgs> | null
  /**
   * The filter to search for the TrackArtist to update in case it exists.
   */
  where: Prisma.TrackArtistWhereUniqueInput
  /**
   * In case the TrackArtist found by the `where` argument doesn't exist, create a new TrackArtist with this data.
   */
  create: Prisma.XOR<Prisma.TrackArtistCreateInput, Prisma.TrackArtistUncheckedCreateInput>
  /**
   * In case the TrackArtist was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.TrackArtistUpdateInput, Prisma.TrackArtistUncheckedUpdateInput>
}

/**
 * TrackArtist delete
 */
export type TrackArtistDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackArtist
   */
  select?: Prisma.TrackArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackArtist
   */
  omit?: Prisma.TrackArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackArtistInclude<ExtArgs> | null
  /**
   * Filter which TrackArtist to delete.
   */
  where: Prisma.TrackArtistWhereUniqueInput
}

/**
 * TrackArtist deleteMany
 */
export type TrackArtistDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which TrackArtists to delete
   */
  where?: Prisma.TrackArtistWhereInput
  /**
   * Limit how many TrackArtists to delete.
   */
  limit?: number
}

/**
 * TrackArtist without action
 */
export type TrackArtistDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackArtist
   */
  select?: Prisma.TrackArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackArtist
   */
  omit?: Prisma.TrackArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackArtistInclude<ExtArgs> | null
}
