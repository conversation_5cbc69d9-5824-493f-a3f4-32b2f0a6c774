
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Release` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Release
 * 
 */
export type ReleaseModel = runtime.Types.Result.DefaultSelection<Prisma.$ReleasePayload>

export type AggregateRelease = {
  _count: ReleaseCountAggregateOutputType | null
  _avg: ReleaseAvgAggregateOutputType | null
  _sum: ReleaseSumAggregateOutputType | null
  _min: ReleaseMinAggregateOutputType | null
  _max: ReleaseMaxAggregateOutputType | null
}

export type ReleaseAvgAggregateOutputType = {
  recordingYear: number | null
  copyrightYear: number | null
  publishingYear: number | null
}

export type ReleaseSumAggregateOutputType = {
  recordingYear: number | null
  copyrightYear: number | null
  publishingYear: number | null
}

export type ReleaseMinAggregateOutputType = {
  id: string | null
  title: string | null
  releaseVersion: string | null
  label: string | null
  format: $Enums.ReleaseFormat | null
  compilation: boolean | null
  explicit: $Enums.ExplicitContent | null
  upc: string | null
  catalogNumber: string | null
  releaseDate: Date | null
  preorderDate: Date | null
  originalReleaseDate: Date | null
  recordingYear: number | null
  recordingCountry: string | null
  copyrightYear: number | null
  copyrightHolder: string | null
  publishingYear: number | null
  publishingHolder: string | null
  courtesyLine: string | null
  genre: string | null
  subGenre: string | null
  language: string | null
  status: $Enums.ReleaseStatus | null
  submittedAt: Date | null
  approvedAt: Date | null
  rejectedAt: Date | null
  rejectedReason: string | null
  userId: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type ReleaseMaxAggregateOutputType = {
  id: string | null
  title: string | null
  releaseVersion: string | null
  label: string | null
  format: $Enums.ReleaseFormat | null
  compilation: boolean | null
  explicit: $Enums.ExplicitContent | null
  upc: string | null
  catalogNumber: string | null
  releaseDate: Date | null
  preorderDate: Date | null
  originalReleaseDate: Date | null
  recordingYear: number | null
  recordingCountry: string | null
  copyrightYear: number | null
  copyrightHolder: string | null
  publishingYear: number | null
  publishingHolder: string | null
  courtesyLine: string | null
  genre: string | null
  subGenre: string | null
  language: string | null
  status: $Enums.ReleaseStatus | null
  submittedAt: Date | null
  approvedAt: Date | null
  rejectedAt: Date | null
  rejectedReason: string | null
  userId: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type ReleaseCountAggregateOutputType = {
  id: number
  title: number
  releaseVersion: number
  label: number
  format: number
  compilation: number
  explicit: number
  upc: number
  catalogNumber: number
  releaseDate: number
  preorderDate: number
  originalReleaseDate: number
  recordingYear: number
  recordingCountry: number
  copyrightYear: number
  copyrightHolder: number
  publishingYear: number
  publishingHolder: number
  courtesyLine: number
  genre: number
  subGenre: number
  language: number
  status: number
  submittedAt: number
  approvedAt: number
  rejectedAt: number
  rejectedReason: number
  userId: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type ReleaseAvgAggregateInputType = {
  recordingYear?: true
  copyrightYear?: true
  publishingYear?: true
}

export type ReleaseSumAggregateInputType = {
  recordingYear?: true
  copyrightYear?: true
  publishingYear?: true
}

export type ReleaseMinAggregateInputType = {
  id?: true
  title?: true
  releaseVersion?: true
  label?: true
  format?: true
  compilation?: true
  explicit?: true
  upc?: true
  catalogNumber?: true
  releaseDate?: true
  preorderDate?: true
  originalReleaseDate?: true
  recordingYear?: true
  recordingCountry?: true
  copyrightYear?: true
  copyrightHolder?: true
  publishingYear?: true
  publishingHolder?: true
  courtesyLine?: true
  genre?: true
  subGenre?: true
  language?: true
  status?: true
  submittedAt?: true
  approvedAt?: true
  rejectedAt?: true
  rejectedReason?: true
  userId?: true
  createdAt?: true
  updatedAt?: true
}

export type ReleaseMaxAggregateInputType = {
  id?: true
  title?: true
  releaseVersion?: true
  label?: true
  format?: true
  compilation?: true
  explicit?: true
  upc?: true
  catalogNumber?: true
  releaseDate?: true
  preorderDate?: true
  originalReleaseDate?: true
  recordingYear?: true
  recordingCountry?: true
  copyrightYear?: true
  copyrightHolder?: true
  publishingYear?: true
  publishingHolder?: true
  courtesyLine?: true
  genre?: true
  subGenre?: true
  language?: true
  status?: true
  submittedAt?: true
  approvedAt?: true
  rejectedAt?: true
  rejectedReason?: true
  userId?: true
  createdAt?: true
  updatedAt?: true
}

export type ReleaseCountAggregateInputType = {
  id?: true
  title?: true
  releaseVersion?: true
  label?: true
  format?: true
  compilation?: true
  explicit?: true
  upc?: true
  catalogNumber?: true
  releaseDate?: true
  preorderDate?: true
  originalReleaseDate?: true
  recordingYear?: true
  recordingCountry?: true
  copyrightYear?: true
  copyrightHolder?: true
  publishingYear?: true
  publishingHolder?: true
  courtesyLine?: true
  genre?: true
  subGenre?: true
  language?: true
  status?: true
  submittedAt?: true
  approvedAt?: true
  rejectedAt?: true
  rejectedReason?: true
  userId?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type ReleaseAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Release to aggregate.
   */
  where?: Prisma.ReleaseWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Releases to fetch.
   */
  orderBy?: Prisma.ReleaseOrderByWithRelationInput | Prisma.ReleaseOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.ReleaseWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Releases from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Releases.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Releases
  **/
  _count?: true | ReleaseCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: ReleaseAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: ReleaseSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: ReleaseMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: ReleaseMaxAggregateInputType
}

export type GetReleaseAggregateType<T extends ReleaseAggregateArgs> = {
      [P in keyof T & keyof AggregateRelease]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateRelease[P]>
    : Prisma.GetScalarType<T[P], AggregateRelease[P]>
}




export type ReleaseGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ReleaseWhereInput
  orderBy?: Prisma.ReleaseOrderByWithAggregationInput | Prisma.ReleaseOrderByWithAggregationInput[]
  by: Prisma.ReleaseScalarFieldEnum[] | Prisma.ReleaseScalarFieldEnum
  having?: Prisma.ReleaseScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: ReleaseCountAggregateInputType | true
  _avg?: ReleaseAvgAggregateInputType
  _sum?: ReleaseSumAggregateInputType
  _min?: ReleaseMinAggregateInputType
  _max?: ReleaseMaxAggregateInputType
}

export type ReleaseGroupByOutputType = {
  id: string
  title: string
  releaseVersion: string | null
  label: string
  format: $Enums.ReleaseFormat
  compilation: boolean
  explicit: $Enums.ExplicitContent
  upc: string | null
  catalogNumber: string
  releaseDate: Date
  preorderDate: Date | null
  originalReleaseDate: Date | null
  recordingYear: number
  recordingCountry: string
  copyrightYear: number
  copyrightHolder: string
  publishingYear: number
  publishingHolder: string
  courtesyLine: string | null
  genre: string
  subGenre: string | null
  language: string
  status: $Enums.ReleaseStatus
  submittedAt: Date | null
  approvedAt: Date | null
  rejectedAt: Date | null
  rejectedReason: string | null
  userId: string
  createdAt: Date
  updatedAt: Date
  _count: ReleaseCountAggregateOutputType | null
  _avg: ReleaseAvgAggregateOutputType | null
  _sum: ReleaseSumAggregateOutputType | null
  _min: ReleaseMinAggregateOutputType | null
  _max: ReleaseMaxAggregateOutputType | null
}

type GetReleaseGroupByPayload<T extends ReleaseGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<ReleaseGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof ReleaseGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], ReleaseGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], ReleaseGroupByOutputType[P]>
      }
    >
  > 



export type ReleaseWhereInput = {
  AND?: Prisma.ReleaseWhereInput | Prisma.ReleaseWhereInput[]
  OR?: Prisma.ReleaseWhereInput[]
  NOT?: Prisma.ReleaseWhereInput | Prisma.ReleaseWhereInput[]
  id?: Prisma.StringFilter<"Release"> | string
  title?: Prisma.StringFilter<"Release"> | string
  releaseVersion?: Prisma.StringNullableFilter<"Release"> | string | null
  label?: Prisma.StringFilter<"Release"> | string
  format?: Prisma.EnumReleaseFormatFilter<"Release"> | $Enums.ReleaseFormat
  compilation?: Prisma.BoolFilter<"Release"> | boolean
  explicit?: Prisma.EnumExplicitContentFilter<"Release"> | $Enums.ExplicitContent
  upc?: Prisma.StringNullableFilter<"Release"> | string | null
  catalogNumber?: Prisma.StringFilter<"Release"> | string
  releaseDate?: Prisma.DateTimeFilter<"Release"> | Date | string
  preorderDate?: Prisma.DateTimeNullableFilter<"Release"> | Date | string | null
  originalReleaseDate?: Prisma.DateTimeNullableFilter<"Release"> | Date | string | null
  recordingYear?: Prisma.IntFilter<"Release"> | number
  recordingCountry?: Prisma.StringFilter<"Release"> | string
  copyrightYear?: Prisma.IntFilter<"Release"> | number
  copyrightHolder?: Prisma.StringFilter<"Release"> | string
  publishingYear?: Prisma.IntFilter<"Release"> | number
  publishingHolder?: Prisma.StringFilter<"Release"> | string
  courtesyLine?: Prisma.StringNullableFilter<"Release"> | string | null
  genre?: Prisma.StringFilter<"Release"> | string
  subGenre?: Prisma.StringNullableFilter<"Release"> | string | null
  language?: Prisma.StringFilter<"Release"> | string
  status?: Prisma.EnumReleaseStatusFilter<"Release"> | $Enums.ReleaseStatus
  submittedAt?: Prisma.DateTimeNullableFilter<"Release"> | Date | string | null
  approvedAt?: Prisma.DateTimeNullableFilter<"Release"> | Date | string | null
  rejectedAt?: Prisma.DateTimeNullableFilter<"Release"> | Date | string | null
  rejectedReason?: Prisma.StringNullableFilter<"Release"> | string | null
  userId?: Prisma.StringFilter<"Release"> | string
  createdAt?: Prisma.DateTimeFilter<"Release"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Release"> | Date | string
  coverArts?: Prisma.ReleaseCoverArtListRelationFilter
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
  artists?: Prisma.ReleaseArtistListRelationFilter
  tracks?: Prisma.ReleaseTrackListRelationFilter
}

export type ReleaseOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  releaseVersion?: Prisma.SortOrderInput | Prisma.SortOrder
  label?: Prisma.SortOrder
  format?: Prisma.SortOrder
  compilation?: Prisma.SortOrder
  explicit?: Prisma.SortOrder
  upc?: Prisma.SortOrderInput | Prisma.SortOrder
  catalogNumber?: Prisma.SortOrder
  releaseDate?: Prisma.SortOrder
  preorderDate?: Prisma.SortOrderInput | Prisma.SortOrder
  originalReleaseDate?: Prisma.SortOrderInput | Prisma.SortOrder
  recordingYear?: Prisma.SortOrder
  recordingCountry?: Prisma.SortOrder
  copyrightYear?: Prisma.SortOrder
  copyrightHolder?: Prisma.SortOrder
  publishingYear?: Prisma.SortOrder
  publishingHolder?: Prisma.SortOrder
  courtesyLine?: Prisma.SortOrderInput | Prisma.SortOrder
  genre?: Prisma.SortOrder
  subGenre?: Prisma.SortOrderInput | Prisma.SortOrder
  language?: Prisma.SortOrder
  status?: Prisma.SortOrder
  submittedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  approvedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  rejectedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  rejectedReason?: Prisma.SortOrderInput | Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  coverArts?: Prisma.ReleaseCoverArtOrderByRelationAggregateInput
  user?: Prisma.UserOrderByWithRelationInput
  artists?: Prisma.ReleaseArtistOrderByRelationAggregateInput
  tracks?: Prisma.ReleaseTrackOrderByRelationAggregateInput
}

export type ReleaseWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.ReleaseWhereInput | Prisma.ReleaseWhereInput[]
  OR?: Prisma.ReleaseWhereInput[]
  NOT?: Prisma.ReleaseWhereInput | Prisma.ReleaseWhereInput[]
  title?: Prisma.StringFilter<"Release"> | string
  releaseVersion?: Prisma.StringNullableFilter<"Release"> | string | null
  label?: Prisma.StringFilter<"Release"> | string
  format?: Prisma.EnumReleaseFormatFilter<"Release"> | $Enums.ReleaseFormat
  compilation?: Prisma.BoolFilter<"Release"> | boolean
  explicit?: Prisma.EnumExplicitContentFilter<"Release"> | $Enums.ExplicitContent
  upc?: Prisma.StringNullableFilter<"Release"> | string | null
  catalogNumber?: Prisma.StringFilter<"Release"> | string
  releaseDate?: Prisma.DateTimeFilter<"Release"> | Date | string
  preorderDate?: Prisma.DateTimeNullableFilter<"Release"> | Date | string | null
  originalReleaseDate?: Prisma.DateTimeNullableFilter<"Release"> | Date | string | null
  recordingYear?: Prisma.IntFilter<"Release"> | number
  recordingCountry?: Prisma.StringFilter<"Release"> | string
  copyrightYear?: Prisma.IntFilter<"Release"> | number
  copyrightHolder?: Prisma.StringFilter<"Release"> | string
  publishingYear?: Prisma.IntFilter<"Release"> | number
  publishingHolder?: Prisma.StringFilter<"Release"> | string
  courtesyLine?: Prisma.StringNullableFilter<"Release"> | string | null
  genre?: Prisma.StringFilter<"Release"> | string
  subGenre?: Prisma.StringNullableFilter<"Release"> | string | null
  language?: Prisma.StringFilter<"Release"> | string
  status?: Prisma.EnumReleaseStatusFilter<"Release"> | $Enums.ReleaseStatus
  submittedAt?: Prisma.DateTimeNullableFilter<"Release"> | Date | string | null
  approvedAt?: Prisma.DateTimeNullableFilter<"Release"> | Date | string | null
  rejectedAt?: Prisma.DateTimeNullableFilter<"Release"> | Date | string | null
  rejectedReason?: Prisma.StringNullableFilter<"Release"> | string | null
  userId?: Prisma.StringFilter<"Release"> | string
  createdAt?: Prisma.DateTimeFilter<"Release"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Release"> | Date | string
  coverArts?: Prisma.ReleaseCoverArtListRelationFilter
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
  artists?: Prisma.ReleaseArtistListRelationFilter
  tracks?: Prisma.ReleaseTrackListRelationFilter
}, "id">

export type ReleaseOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  releaseVersion?: Prisma.SortOrderInput | Prisma.SortOrder
  label?: Prisma.SortOrder
  format?: Prisma.SortOrder
  compilation?: Prisma.SortOrder
  explicit?: Prisma.SortOrder
  upc?: Prisma.SortOrderInput | Prisma.SortOrder
  catalogNumber?: Prisma.SortOrder
  releaseDate?: Prisma.SortOrder
  preorderDate?: Prisma.SortOrderInput | Prisma.SortOrder
  originalReleaseDate?: Prisma.SortOrderInput | Prisma.SortOrder
  recordingYear?: Prisma.SortOrder
  recordingCountry?: Prisma.SortOrder
  copyrightYear?: Prisma.SortOrder
  copyrightHolder?: Prisma.SortOrder
  publishingYear?: Prisma.SortOrder
  publishingHolder?: Prisma.SortOrder
  courtesyLine?: Prisma.SortOrderInput | Prisma.SortOrder
  genre?: Prisma.SortOrder
  subGenre?: Prisma.SortOrderInput | Prisma.SortOrder
  language?: Prisma.SortOrder
  status?: Prisma.SortOrder
  submittedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  approvedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  rejectedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  rejectedReason?: Prisma.SortOrderInput | Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.ReleaseCountOrderByAggregateInput
  _avg?: Prisma.ReleaseAvgOrderByAggregateInput
  _max?: Prisma.ReleaseMaxOrderByAggregateInput
  _min?: Prisma.ReleaseMinOrderByAggregateInput
  _sum?: Prisma.ReleaseSumOrderByAggregateInput
}

export type ReleaseScalarWhereWithAggregatesInput = {
  AND?: Prisma.ReleaseScalarWhereWithAggregatesInput | Prisma.ReleaseScalarWhereWithAggregatesInput[]
  OR?: Prisma.ReleaseScalarWhereWithAggregatesInput[]
  NOT?: Prisma.ReleaseScalarWhereWithAggregatesInput | Prisma.ReleaseScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Release"> | string
  title?: Prisma.StringWithAggregatesFilter<"Release"> | string
  releaseVersion?: Prisma.StringNullableWithAggregatesFilter<"Release"> | string | null
  label?: Prisma.StringWithAggregatesFilter<"Release"> | string
  format?: Prisma.EnumReleaseFormatWithAggregatesFilter<"Release"> | $Enums.ReleaseFormat
  compilation?: Prisma.BoolWithAggregatesFilter<"Release"> | boolean
  explicit?: Prisma.EnumExplicitContentWithAggregatesFilter<"Release"> | $Enums.ExplicitContent
  upc?: Prisma.StringNullableWithAggregatesFilter<"Release"> | string | null
  catalogNumber?: Prisma.StringWithAggregatesFilter<"Release"> | string
  releaseDate?: Prisma.DateTimeWithAggregatesFilter<"Release"> | Date | string
  preorderDate?: Prisma.DateTimeNullableWithAggregatesFilter<"Release"> | Date | string | null
  originalReleaseDate?: Prisma.DateTimeNullableWithAggregatesFilter<"Release"> | Date | string | null
  recordingYear?: Prisma.IntWithAggregatesFilter<"Release"> | number
  recordingCountry?: Prisma.StringWithAggregatesFilter<"Release"> | string
  copyrightYear?: Prisma.IntWithAggregatesFilter<"Release"> | number
  copyrightHolder?: Prisma.StringWithAggregatesFilter<"Release"> | string
  publishingYear?: Prisma.IntWithAggregatesFilter<"Release"> | number
  publishingHolder?: Prisma.StringWithAggregatesFilter<"Release"> | string
  courtesyLine?: Prisma.StringNullableWithAggregatesFilter<"Release"> | string | null
  genre?: Prisma.StringWithAggregatesFilter<"Release"> | string
  subGenre?: Prisma.StringNullableWithAggregatesFilter<"Release"> | string | null
  language?: Prisma.StringWithAggregatesFilter<"Release"> | string
  status?: Prisma.EnumReleaseStatusWithAggregatesFilter<"Release"> | $Enums.ReleaseStatus
  submittedAt?: Prisma.DateTimeNullableWithAggregatesFilter<"Release"> | Date | string | null
  approvedAt?: Prisma.DateTimeNullableWithAggregatesFilter<"Release"> | Date | string | null
  rejectedAt?: Prisma.DateTimeNullableWithAggregatesFilter<"Release"> | Date | string | null
  rejectedReason?: Prisma.StringNullableWithAggregatesFilter<"Release"> | string | null
  userId?: Prisma.StringWithAggregatesFilter<"Release"> | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Release"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"Release"> | Date | string
}

export type ReleaseCreateInput = {
  id?: string
  title: string
  releaseVersion?: string | null
  label: string
  format: $Enums.ReleaseFormat
  compilation: boolean
  explicit: $Enums.ExplicitContent
  upc?: string | null
  catalogNumber: string
  releaseDate: Date | string
  preorderDate?: Date | string | null
  originalReleaseDate?: Date | string | null
  recordingYear: number
  recordingCountry: string
  copyrightYear: number
  copyrightHolder: string
  publishingYear: number
  publishingHolder: string
  courtesyLine?: string | null
  genre: string
  subGenre?: string | null
  language: string
  status?: $Enums.ReleaseStatus
  submittedAt?: Date | string | null
  approvedAt?: Date | string | null
  rejectedAt?: Date | string | null
  rejectedReason?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  coverArts?: Prisma.ReleaseCoverArtCreateNestedManyWithoutReleaseInput
  user: Prisma.UserCreateNestedOneWithoutReleasesInput
  artists?: Prisma.ReleaseArtistCreateNestedManyWithoutReleaseInput
  tracks?: Prisma.ReleaseTrackCreateNestedManyWithoutReleaseInput
}

export type ReleaseUncheckedCreateInput = {
  id?: string
  title: string
  releaseVersion?: string | null
  label: string
  format: $Enums.ReleaseFormat
  compilation: boolean
  explicit: $Enums.ExplicitContent
  upc?: string | null
  catalogNumber: string
  releaseDate: Date | string
  preorderDate?: Date | string | null
  originalReleaseDate?: Date | string | null
  recordingYear: number
  recordingCountry: string
  copyrightYear: number
  copyrightHolder: string
  publishingYear: number
  publishingHolder: string
  courtesyLine?: string | null
  genre: string
  subGenre?: string | null
  language: string
  status?: $Enums.ReleaseStatus
  submittedAt?: Date | string | null
  approvedAt?: Date | string | null
  rejectedAt?: Date | string | null
  rejectedReason?: string | null
  userId: string
  createdAt?: Date | string
  updatedAt?: Date | string
  coverArts?: Prisma.ReleaseCoverArtUncheckedCreateNestedManyWithoutReleaseInput
  artists?: Prisma.ReleaseArtistUncheckedCreateNestedManyWithoutReleaseInput
  tracks?: Prisma.ReleaseTrackUncheckedCreateNestedManyWithoutReleaseInput
}

export type ReleaseUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  releaseVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  label?: Prisma.StringFieldUpdateOperationsInput | string
  format?: Prisma.EnumReleaseFormatFieldUpdateOperationsInput | $Enums.ReleaseFormat
  compilation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  explicit?: Prisma.EnumExplicitContentFieldUpdateOperationsInput | $Enums.ExplicitContent
  upc?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  catalogNumber?: Prisma.StringFieldUpdateOperationsInput | string
  releaseDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  preorderDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  originalReleaseDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  recordingYear?: Prisma.IntFieldUpdateOperationsInput | number
  recordingCountry?: Prisma.StringFieldUpdateOperationsInput | string
  copyrightYear?: Prisma.IntFieldUpdateOperationsInput | number
  copyrightHolder?: Prisma.StringFieldUpdateOperationsInput | string
  publishingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingHolder?: Prisma.StringFieldUpdateOperationsInput | string
  courtesyLine?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  genre?: Prisma.StringFieldUpdateOperationsInput | string
  subGenre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  language?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumReleaseStatusFieldUpdateOperationsInput | $Enums.ReleaseStatus
  submittedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  approvedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejectedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejectedReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  coverArts?: Prisma.ReleaseCoverArtUpdateManyWithoutReleaseNestedInput
  user?: Prisma.UserUpdateOneRequiredWithoutReleasesNestedInput
  artists?: Prisma.ReleaseArtistUpdateManyWithoutReleaseNestedInput
  tracks?: Prisma.ReleaseTrackUpdateManyWithoutReleaseNestedInput
}

export type ReleaseUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  releaseVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  label?: Prisma.StringFieldUpdateOperationsInput | string
  format?: Prisma.EnumReleaseFormatFieldUpdateOperationsInput | $Enums.ReleaseFormat
  compilation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  explicit?: Prisma.EnumExplicitContentFieldUpdateOperationsInput | $Enums.ExplicitContent
  upc?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  catalogNumber?: Prisma.StringFieldUpdateOperationsInput | string
  releaseDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  preorderDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  originalReleaseDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  recordingYear?: Prisma.IntFieldUpdateOperationsInput | number
  recordingCountry?: Prisma.StringFieldUpdateOperationsInput | string
  copyrightYear?: Prisma.IntFieldUpdateOperationsInput | number
  copyrightHolder?: Prisma.StringFieldUpdateOperationsInput | string
  publishingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingHolder?: Prisma.StringFieldUpdateOperationsInput | string
  courtesyLine?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  genre?: Prisma.StringFieldUpdateOperationsInput | string
  subGenre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  language?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumReleaseStatusFieldUpdateOperationsInput | $Enums.ReleaseStatus
  submittedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  approvedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejectedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejectedReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  coverArts?: Prisma.ReleaseCoverArtUncheckedUpdateManyWithoutReleaseNestedInput
  artists?: Prisma.ReleaseArtistUncheckedUpdateManyWithoutReleaseNestedInput
  tracks?: Prisma.ReleaseTrackUncheckedUpdateManyWithoutReleaseNestedInput
}

export type ReleaseCreateManyInput = {
  id?: string
  title: string
  releaseVersion?: string | null
  label: string
  format: $Enums.ReleaseFormat
  compilation: boolean
  explicit: $Enums.ExplicitContent
  upc?: string | null
  catalogNumber: string
  releaseDate: Date | string
  preorderDate?: Date | string | null
  originalReleaseDate?: Date | string | null
  recordingYear: number
  recordingCountry: string
  copyrightYear: number
  copyrightHolder: string
  publishingYear: number
  publishingHolder: string
  courtesyLine?: string | null
  genre: string
  subGenre?: string | null
  language: string
  status?: $Enums.ReleaseStatus
  submittedAt?: Date | string | null
  approvedAt?: Date | string | null
  rejectedAt?: Date | string | null
  rejectedReason?: string | null
  userId: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type ReleaseUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  releaseVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  label?: Prisma.StringFieldUpdateOperationsInput | string
  format?: Prisma.EnumReleaseFormatFieldUpdateOperationsInput | $Enums.ReleaseFormat
  compilation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  explicit?: Prisma.EnumExplicitContentFieldUpdateOperationsInput | $Enums.ExplicitContent
  upc?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  catalogNumber?: Prisma.StringFieldUpdateOperationsInput | string
  releaseDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  preorderDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  originalReleaseDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  recordingYear?: Prisma.IntFieldUpdateOperationsInput | number
  recordingCountry?: Prisma.StringFieldUpdateOperationsInput | string
  copyrightYear?: Prisma.IntFieldUpdateOperationsInput | number
  copyrightHolder?: Prisma.StringFieldUpdateOperationsInput | string
  publishingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingHolder?: Prisma.StringFieldUpdateOperationsInput | string
  courtesyLine?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  genre?: Prisma.StringFieldUpdateOperationsInput | string
  subGenre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  language?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumReleaseStatusFieldUpdateOperationsInput | $Enums.ReleaseStatus
  submittedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  approvedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejectedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejectedReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ReleaseUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  releaseVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  label?: Prisma.StringFieldUpdateOperationsInput | string
  format?: Prisma.EnumReleaseFormatFieldUpdateOperationsInput | $Enums.ReleaseFormat
  compilation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  explicit?: Prisma.EnumExplicitContentFieldUpdateOperationsInput | $Enums.ExplicitContent
  upc?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  catalogNumber?: Prisma.StringFieldUpdateOperationsInput | string
  releaseDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  preorderDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  originalReleaseDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  recordingYear?: Prisma.IntFieldUpdateOperationsInput | number
  recordingCountry?: Prisma.StringFieldUpdateOperationsInput | string
  copyrightYear?: Prisma.IntFieldUpdateOperationsInput | number
  copyrightHolder?: Prisma.StringFieldUpdateOperationsInput | string
  publishingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingHolder?: Prisma.StringFieldUpdateOperationsInput | string
  courtesyLine?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  genre?: Prisma.StringFieldUpdateOperationsInput | string
  subGenre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  language?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumReleaseStatusFieldUpdateOperationsInput | $Enums.ReleaseStatus
  submittedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  approvedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejectedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejectedReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ReleaseListRelationFilter = {
  every?: Prisma.ReleaseWhereInput
  some?: Prisma.ReleaseWhereInput
  none?: Prisma.ReleaseWhereInput
}

export type ReleaseOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type ReleaseCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  releaseVersion?: Prisma.SortOrder
  label?: Prisma.SortOrder
  format?: Prisma.SortOrder
  compilation?: Prisma.SortOrder
  explicit?: Prisma.SortOrder
  upc?: Prisma.SortOrder
  catalogNumber?: Prisma.SortOrder
  releaseDate?: Prisma.SortOrder
  preorderDate?: Prisma.SortOrder
  originalReleaseDate?: Prisma.SortOrder
  recordingYear?: Prisma.SortOrder
  recordingCountry?: Prisma.SortOrder
  copyrightYear?: Prisma.SortOrder
  copyrightHolder?: Prisma.SortOrder
  publishingYear?: Prisma.SortOrder
  publishingHolder?: Prisma.SortOrder
  courtesyLine?: Prisma.SortOrder
  genre?: Prisma.SortOrder
  subGenre?: Prisma.SortOrder
  language?: Prisma.SortOrder
  status?: Prisma.SortOrder
  submittedAt?: Prisma.SortOrder
  approvedAt?: Prisma.SortOrder
  rejectedAt?: Prisma.SortOrder
  rejectedReason?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type ReleaseAvgOrderByAggregateInput = {
  recordingYear?: Prisma.SortOrder
  copyrightYear?: Prisma.SortOrder
  publishingYear?: Prisma.SortOrder
}

export type ReleaseMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  releaseVersion?: Prisma.SortOrder
  label?: Prisma.SortOrder
  format?: Prisma.SortOrder
  compilation?: Prisma.SortOrder
  explicit?: Prisma.SortOrder
  upc?: Prisma.SortOrder
  catalogNumber?: Prisma.SortOrder
  releaseDate?: Prisma.SortOrder
  preorderDate?: Prisma.SortOrder
  originalReleaseDate?: Prisma.SortOrder
  recordingYear?: Prisma.SortOrder
  recordingCountry?: Prisma.SortOrder
  copyrightYear?: Prisma.SortOrder
  copyrightHolder?: Prisma.SortOrder
  publishingYear?: Prisma.SortOrder
  publishingHolder?: Prisma.SortOrder
  courtesyLine?: Prisma.SortOrder
  genre?: Prisma.SortOrder
  subGenre?: Prisma.SortOrder
  language?: Prisma.SortOrder
  status?: Prisma.SortOrder
  submittedAt?: Prisma.SortOrder
  approvedAt?: Prisma.SortOrder
  rejectedAt?: Prisma.SortOrder
  rejectedReason?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type ReleaseMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  releaseVersion?: Prisma.SortOrder
  label?: Prisma.SortOrder
  format?: Prisma.SortOrder
  compilation?: Prisma.SortOrder
  explicit?: Prisma.SortOrder
  upc?: Prisma.SortOrder
  catalogNumber?: Prisma.SortOrder
  releaseDate?: Prisma.SortOrder
  preorderDate?: Prisma.SortOrder
  originalReleaseDate?: Prisma.SortOrder
  recordingYear?: Prisma.SortOrder
  recordingCountry?: Prisma.SortOrder
  copyrightYear?: Prisma.SortOrder
  copyrightHolder?: Prisma.SortOrder
  publishingYear?: Prisma.SortOrder
  publishingHolder?: Prisma.SortOrder
  courtesyLine?: Prisma.SortOrder
  genre?: Prisma.SortOrder
  subGenre?: Prisma.SortOrder
  language?: Prisma.SortOrder
  status?: Prisma.SortOrder
  submittedAt?: Prisma.SortOrder
  approvedAt?: Prisma.SortOrder
  rejectedAt?: Prisma.SortOrder
  rejectedReason?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type ReleaseSumOrderByAggregateInput = {
  recordingYear?: Prisma.SortOrder
  copyrightYear?: Prisma.SortOrder
  publishingYear?: Prisma.SortOrder
}

export type ReleaseScalarRelationFilter = {
  is?: Prisma.ReleaseWhereInput
  isNot?: Prisma.ReleaseWhereInput
}

export type ReleaseCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.ReleaseCreateWithoutUserInput, Prisma.ReleaseUncheckedCreateWithoutUserInput> | Prisma.ReleaseCreateWithoutUserInput[] | Prisma.ReleaseUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.ReleaseCreateOrConnectWithoutUserInput | Prisma.ReleaseCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.ReleaseCreateManyUserInputEnvelope
  connect?: Prisma.ReleaseWhereUniqueInput | Prisma.ReleaseWhereUniqueInput[]
}

export type ReleaseUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.ReleaseCreateWithoutUserInput, Prisma.ReleaseUncheckedCreateWithoutUserInput> | Prisma.ReleaseCreateWithoutUserInput[] | Prisma.ReleaseUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.ReleaseCreateOrConnectWithoutUserInput | Prisma.ReleaseCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.ReleaseCreateManyUserInputEnvelope
  connect?: Prisma.ReleaseWhereUniqueInput | Prisma.ReleaseWhereUniqueInput[]
}

export type ReleaseUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.ReleaseCreateWithoutUserInput, Prisma.ReleaseUncheckedCreateWithoutUserInput> | Prisma.ReleaseCreateWithoutUserInput[] | Prisma.ReleaseUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.ReleaseCreateOrConnectWithoutUserInput | Prisma.ReleaseCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.ReleaseUpsertWithWhereUniqueWithoutUserInput | Prisma.ReleaseUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.ReleaseCreateManyUserInputEnvelope
  set?: Prisma.ReleaseWhereUniqueInput | Prisma.ReleaseWhereUniqueInput[]
  disconnect?: Prisma.ReleaseWhereUniqueInput | Prisma.ReleaseWhereUniqueInput[]
  delete?: Prisma.ReleaseWhereUniqueInput | Prisma.ReleaseWhereUniqueInput[]
  connect?: Prisma.ReleaseWhereUniqueInput | Prisma.ReleaseWhereUniqueInput[]
  update?: Prisma.ReleaseUpdateWithWhereUniqueWithoutUserInput | Prisma.ReleaseUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.ReleaseUpdateManyWithWhereWithoutUserInput | Prisma.ReleaseUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.ReleaseScalarWhereInput | Prisma.ReleaseScalarWhereInput[]
}

export type ReleaseUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.ReleaseCreateWithoutUserInput, Prisma.ReleaseUncheckedCreateWithoutUserInput> | Prisma.ReleaseCreateWithoutUserInput[] | Prisma.ReleaseUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.ReleaseCreateOrConnectWithoutUserInput | Prisma.ReleaseCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.ReleaseUpsertWithWhereUniqueWithoutUserInput | Prisma.ReleaseUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.ReleaseCreateManyUserInputEnvelope
  set?: Prisma.ReleaseWhereUniqueInput | Prisma.ReleaseWhereUniqueInput[]
  disconnect?: Prisma.ReleaseWhereUniqueInput | Prisma.ReleaseWhereUniqueInput[]
  delete?: Prisma.ReleaseWhereUniqueInput | Prisma.ReleaseWhereUniqueInput[]
  connect?: Prisma.ReleaseWhereUniqueInput | Prisma.ReleaseWhereUniqueInput[]
  update?: Prisma.ReleaseUpdateWithWhereUniqueWithoutUserInput | Prisma.ReleaseUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.ReleaseUpdateManyWithWhereWithoutUserInput | Prisma.ReleaseUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.ReleaseScalarWhereInput | Prisma.ReleaseScalarWhereInput[]
}

export type EnumReleaseFormatFieldUpdateOperationsInput = {
  set?: $Enums.ReleaseFormat
}

export type EnumExplicitContentFieldUpdateOperationsInput = {
  set?: $Enums.ExplicitContent
}

export type IntFieldUpdateOperationsInput = {
  set?: number
  increment?: number
  decrement?: number
  multiply?: number
  divide?: number
}

export type EnumReleaseStatusFieldUpdateOperationsInput = {
  set?: $Enums.ReleaseStatus
}

export type ReleaseCreateNestedOneWithoutArtistsInput = {
  create?: Prisma.XOR<Prisma.ReleaseCreateWithoutArtistsInput, Prisma.ReleaseUncheckedCreateWithoutArtistsInput>
  connectOrCreate?: Prisma.ReleaseCreateOrConnectWithoutArtistsInput
  connect?: Prisma.ReleaseWhereUniqueInput
}

export type ReleaseUpdateOneRequiredWithoutArtistsNestedInput = {
  create?: Prisma.XOR<Prisma.ReleaseCreateWithoutArtistsInput, Prisma.ReleaseUncheckedCreateWithoutArtistsInput>
  connectOrCreate?: Prisma.ReleaseCreateOrConnectWithoutArtistsInput
  upsert?: Prisma.ReleaseUpsertWithoutArtistsInput
  connect?: Prisma.ReleaseWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.ReleaseUpdateToOneWithWhereWithoutArtistsInput, Prisma.ReleaseUpdateWithoutArtistsInput>, Prisma.ReleaseUncheckedUpdateWithoutArtistsInput>
}

export type ReleaseCreateNestedOneWithoutTracksInput = {
  create?: Prisma.XOR<Prisma.ReleaseCreateWithoutTracksInput, Prisma.ReleaseUncheckedCreateWithoutTracksInput>
  connectOrCreate?: Prisma.ReleaseCreateOrConnectWithoutTracksInput
  connect?: Prisma.ReleaseWhereUniqueInput
}

export type ReleaseUpdateOneRequiredWithoutTracksNestedInput = {
  create?: Prisma.XOR<Prisma.ReleaseCreateWithoutTracksInput, Prisma.ReleaseUncheckedCreateWithoutTracksInput>
  connectOrCreate?: Prisma.ReleaseCreateOrConnectWithoutTracksInput
  upsert?: Prisma.ReleaseUpsertWithoutTracksInput
  connect?: Prisma.ReleaseWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.ReleaseUpdateToOneWithWhereWithoutTracksInput, Prisma.ReleaseUpdateWithoutTracksInput>, Prisma.ReleaseUncheckedUpdateWithoutTracksInput>
}

export type ReleaseCreateNestedOneWithoutCoverArtsInput = {
  create?: Prisma.XOR<Prisma.ReleaseCreateWithoutCoverArtsInput, Prisma.ReleaseUncheckedCreateWithoutCoverArtsInput>
  connectOrCreate?: Prisma.ReleaseCreateOrConnectWithoutCoverArtsInput
  connect?: Prisma.ReleaseWhereUniqueInput
}

export type ReleaseUpdateOneRequiredWithoutCoverArtsNestedInput = {
  create?: Prisma.XOR<Prisma.ReleaseCreateWithoutCoverArtsInput, Prisma.ReleaseUncheckedCreateWithoutCoverArtsInput>
  connectOrCreate?: Prisma.ReleaseCreateOrConnectWithoutCoverArtsInput
  upsert?: Prisma.ReleaseUpsertWithoutCoverArtsInput
  connect?: Prisma.ReleaseWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.ReleaseUpdateToOneWithWhereWithoutCoverArtsInput, Prisma.ReleaseUpdateWithoutCoverArtsInput>, Prisma.ReleaseUncheckedUpdateWithoutCoverArtsInput>
}

export type ReleaseCreateWithoutUserInput = {
  id?: string
  title: string
  releaseVersion?: string | null
  label: string
  format: $Enums.ReleaseFormat
  compilation: boolean
  explicit: $Enums.ExplicitContent
  upc?: string | null
  catalogNumber: string
  releaseDate: Date | string
  preorderDate?: Date | string | null
  originalReleaseDate?: Date | string | null
  recordingYear: number
  recordingCountry: string
  copyrightYear: number
  copyrightHolder: string
  publishingYear: number
  publishingHolder: string
  courtesyLine?: string | null
  genre: string
  subGenre?: string | null
  language: string
  status?: $Enums.ReleaseStatus
  submittedAt?: Date | string | null
  approvedAt?: Date | string | null
  rejectedAt?: Date | string | null
  rejectedReason?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  coverArts?: Prisma.ReleaseCoverArtCreateNestedManyWithoutReleaseInput
  artists?: Prisma.ReleaseArtistCreateNestedManyWithoutReleaseInput
  tracks?: Prisma.ReleaseTrackCreateNestedManyWithoutReleaseInput
}

export type ReleaseUncheckedCreateWithoutUserInput = {
  id?: string
  title: string
  releaseVersion?: string | null
  label: string
  format: $Enums.ReleaseFormat
  compilation: boolean
  explicit: $Enums.ExplicitContent
  upc?: string | null
  catalogNumber: string
  releaseDate: Date | string
  preorderDate?: Date | string | null
  originalReleaseDate?: Date | string | null
  recordingYear: number
  recordingCountry: string
  copyrightYear: number
  copyrightHolder: string
  publishingYear: number
  publishingHolder: string
  courtesyLine?: string | null
  genre: string
  subGenre?: string | null
  language: string
  status?: $Enums.ReleaseStatus
  submittedAt?: Date | string | null
  approvedAt?: Date | string | null
  rejectedAt?: Date | string | null
  rejectedReason?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  coverArts?: Prisma.ReleaseCoverArtUncheckedCreateNestedManyWithoutReleaseInput
  artists?: Prisma.ReleaseArtistUncheckedCreateNestedManyWithoutReleaseInput
  tracks?: Prisma.ReleaseTrackUncheckedCreateNestedManyWithoutReleaseInput
}

export type ReleaseCreateOrConnectWithoutUserInput = {
  where: Prisma.ReleaseWhereUniqueInput
  create: Prisma.XOR<Prisma.ReleaseCreateWithoutUserInput, Prisma.ReleaseUncheckedCreateWithoutUserInput>
}

export type ReleaseCreateManyUserInputEnvelope = {
  data: Prisma.ReleaseCreateManyUserInput | Prisma.ReleaseCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type ReleaseUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.ReleaseWhereUniqueInput
  update: Prisma.XOR<Prisma.ReleaseUpdateWithoutUserInput, Prisma.ReleaseUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.ReleaseCreateWithoutUserInput, Prisma.ReleaseUncheckedCreateWithoutUserInput>
}

export type ReleaseUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.ReleaseWhereUniqueInput
  data: Prisma.XOR<Prisma.ReleaseUpdateWithoutUserInput, Prisma.ReleaseUncheckedUpdateWithoutUserInput>
}

export type ReleaseUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.ReleaseScalarWhereInput
  data: Prisma.XOR<Prisma.ReleaseUpdateManyMutationInput, Prisma.ReleaseUncheckedUpdateManyWithoutUserInput>
}

export type ReleaseScalarWhereInput = {
  AND?: Prisma.ReleaseScalarWhereInput | Prisma.ReleaseScalarWhereInput[]
  OR?: Prisma.ReleaseScalarWhereInput[]
  NOT?: Prisma.ReleaseScalarWhereInput | Prisma.ReleaseScalarWhereInput[]
  id?: Prisma.StringFilter<"Release"> | string
  title?: Prisma.StringFilter<"Release"> | string
  releaseVersion?: Prisma.StringNullableFilter<"Release"> | string | null
  label?: Prisma.StringFilter<"Release"> | string
  format?: Prisma.EnumReleaseFormatFilter<"Release"> | $Enums.ReleaseFormat
  compilation?: Prisma.BoolFilter<"Release"> | boolean
  explicit?: Prisma.EnumExplicitContentFilter<"Release"> | $Enums.ExplicitContent
  upc?: Prisma.StringNullableFilter<"Release"> | string | null
  catalogNumber?: Prisma.StringFilter<"Release"> | string
  releaseDate?: Prisma.DateTimeFilter<"Release"> | Date | string
  preorderDate?: Prisma.DateTimeNullableFilter<"Release"> | Date | string | null
  originalReleaseDate?: Prisma.DateTimeNullableFilter<"Release"> | Date | string | null
  recordingYear?: Prisma.IntFilter<"Release"> | number
  recordingCountry?: Prisma.StringFilter<"Release"> | string
  copyrightYear?: Prisma.IntFilter<"Release"> | number
  copyrightHolder?: Prisma.StringFilter<"Release"> | string
  publishingYear?: Prisma.IntFilter<"Release"> | number
  publishingHolder?: Prisma.StringFilter<"Release"> | string
  courtesyLine?: Prisma.StringNullableFilter<"Release"> | string | null
  genre?: Prisma.StringFilter<"Release"> | string
  subGenre?: Prisma.StringNullableFilter<"Release"> | string | null
  language?: Prisma.StringFilter<"Release"> | string
  status?: Prisma.EnumReleaseStatusFilter<"Release"> | $Enums.ReleaseStatus
  submittedAt?: Prisma.DateTimeNullableFilter<"Release"> | Date | string | null
  approvedAt?: Prisma.DateTimeNullableFilter<"Release"> | Date | string | null
  rejectedAt?: Prisma.DateTimeNullableFilter<"Release"> | Date | string | null
  rejectedReason?: Prisma.StringNullableFilter<"Release"> | string | null
  userId?: Prisma.StringFilter<"Release"> | string
  createdAt?: Prisma.DateTimeFilter<"Release"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Release"> | Date | string
}

export type ReleaseCreateWithoutArtistsInput = {
  id?: string
  title: string
  releaseVersion?: string | null
  label: string
  format: $Enums.ReleaseFormat
  compilation: boolean
  explicit: $Enums.ExplicitContent
  upc?: string | null
  catalogNumber: string
  releaseDate: Date | string
  preorderDate?: Date | string | null
  originalReleaseDate?: Date | string | null
  recordingYear: number
  recordingCountry: string
  copyrightYear: number
  copyrightHolder: string
  publishingYear: number
  publishingHolder: string
  courtesyLine?: string | null
  genre: string
  subGenre?: string | null
  language: string
  status?: $Enums.ReleaseStatus
  submittedAt?: Date | string | null
  approvedAt?: Date | string | null
  rejectedAt?: Date | string | null
  rejectedReason?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  coverArts?: Prisma.ReleaseCoverArtCreateNestedManyWithoutReleaseInput
  user: Prisma.UserCreateNestedOneWithoutReleasesInput
  tracks?: Prisma.ReleaseTrackCreateNestedManyWithoutReleaseInput
}

export type ReleaseUncheckedCreateWithoutArtistsInput = {
  id?: string
  title: string
  releaseVersion?: string | null
  label: string
  format: $Enums.ReleaseFormat
  compilation: boolean
  explicit: $Enums.ExplicitContent
  upc?: string | null
  catalogNumber: string
  releaseDate: Date | string
  preorderDate?: Date | string | null
  originalReleaseDate?: Date | string | null
  recordingYear: number
  recordingCountry: string
  copyrightYear: number
  copyrightHolder: string
  publishingYear: number
  publishingHolder: string
  courtesyLine?: string | null
  genre: string
  subGenre?: string | null
  language: string
  status?: $Enums.ReleaseStatus
  submittedAt?: Date | string | null
  approvedAt?: Date | string | null
  rejectedAt?: Date | string | null
  rejectedReason?: string | null
  userId: string
  createdAt?: Date | string
  updatedAt?: Date | string
  coverArts?: Prisma.ReleaseCoverArtUncheckedCreateNestedManyWithoutReleaseInput
  tracks?: Prisma.ReleaseTrackUncheckedCreateNestedManyWithoutReleaseInput
}

export type ReleaseCreateOrConnectWithoutArtistsInput = {
  where: Prisma.ReleaseWhereUniqueInput
  create: Prisma.XOR<Prisma.ReleaseCreateWithoutArtistsInput, Prisma.ReleaseUncheckedCreateWithoutArtistsInput>
}

export type ReleaseUpsertWithoutArtistsInput = {
  update: Prisma.XOR<Prisma.ReleaseUpdateWithoutArtistsInput, Prisma.ReleaseUncheckedUpdateWithoutArtistsInput>
  create: Prisma.XOR<Prisma.ReleaseCreateWithoutArtistsInput, Prisma.ReleaseUncheckedCreateWithoutArtistsInput>
  where?: Prisma.ReleaseWhereInput
}

export type ReleaseUpdateToOneWithWhereWithoutArtistsInput = {
  where?: Prisma.ReleaseWhereInput
  data: Prisma.XOR<Prisma.ReleaseUpdateWithoutArtistsInput, Prisma.ReleaseUncheckedUpdateWithoutArtistsInput>
}

export type ReleaseUpdateWithoutArtistsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  releaseVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  label?: Prisma.StringFieldUpdateOperationsInput | string
  format?: Prisma.EnumReleaseFormatFieldUpdateOperationsInput | $Enums.ReleaseFormat
  compilation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  explicit?: Prisma.EnumExplicitContentFieldUpdateOperationsInput | $Enums.ExplicitContent
  upc?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  catalogNumber?: Prisma.StringFieldUpdateOperationsInput | string
  releaseDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  preorderDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  originalReleaseDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  recordingYear?: Prisma.IntFieldUpdateOperationsInput | number
  recordingCountry?: Prisma.StringFieldUpdateOperationsInput | string
  copyrightYear?: Prisma.IntFieldUpdateOperationsInput | number
  copyrightHolder?: Prisma.StringFieldUpdateOperationsInput | string
  publishingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingHolder?: Prisma.StringFieldUpdateOperationsInput | string
  courtesyLine?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  genre?: Prisma.StringFieldUpdateOperationsInput | string
  subGenre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  language?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumReleaseStatusFieldUpdateOperationsInput | $Enums.ReleaseStatus
  submittedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  approvedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejectedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejectedReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  coverArts?: Prisma.ReleaseCoverArtUpdateManyWithoutReleaseNestedInput
  user?: Prisma.UserUpdateOneRequiredWithoutReleasesNestedInput
  tracks?: Prisma.ReleaseTrackUpdateManyWithoutReleaseNestedInput
}

export type ReleaseUncheckedUpdateWithoutArtistsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  releaseVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  label?: Prisma.StringFieldUpdateOperationsInput | string
  format?: Prisma.EnumReleaseFormatFieldUpdateOperationsInput | $Enums.ReleaseFormat
  compilation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  explicit?: Prisma.EnumExplicitContentFieldUpdateOperationsInput | $Enums.ExplicitContent
  upc?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  catalogNumber?: Prisma.StringFieldUpdateOperationsInput | string
  releaseDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  preorderDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  originalReleaseDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  recordingYear?: Prisma.IntFieldUpdateOperationsInput | number
  recordingCountry?: Prisma.StringFieldUpdateOperationsInput | string
  copyrightYear?: Prisma.IntFieldUpdateOperationsInput | number
  copyrightHolder?: Prisma.StringFieldUpdateOperationsInput | string
  publishingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingHolder?: Prisma.StringFieldUpdateOperationsInput | string
  courtesyLine?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  genre?: Prisma.StringFieldUpdateOperationsInput | string
  subGenre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  language?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumReleaseStatusFieldUpdateOperationsInput | $Enums.ReleaseStatus
  submittedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  approvedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejectedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejectedReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  coverArts?: Prisma.ReleaseCoverArtUncheckedUpdateManyWithoutReleaseNestedInput
  tracks?: Prisma.ReleaseTrackUncheckedUpdateManyWithoutReleaseNestedInput
}

export type ReleaseCreateWithoutTracksInput = {
  id?: string
  title: string
  releaseVersion?: string | null
  label: string
  format: $Enums.ReleaseFormat
  compilation: boolean
  explicit: $Enums.ExplicitContent
  upc?: string | null
  catalogNumber: string
  releaseDate: Date | string
  preorderDate?: Date | string | null
  originalReleaseDate?: Date | string | null
  recordingYear: number
  recordingCountry: string
  copyrightYear: number
  copyrightHolder: string
  publishingYear: number
  publishingHolder: string
  courtesyLine?: string | null
  genre: string
  subGenre?: string | null
  language: string
  status?: $Enums.ReleaseStatus
  submittedAt?: Date | string | null
  approvedAt?: Date | string | null
  rejectedAt?: Date | string | null
  rejectedReason?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  coverArts?: Prisma.ReleaseCoverArtCreateNestedManyWithoutReleaseInput
  user: Prisma.UserCreateNestedOneWithoutReleasesInput
  artists?: Prisma.ReleaseArtistCreateNestedManyWithoutReleaseInput
}

export type ReleaseUncheckedCreateWithoutTracksInput = {
  id?: string
  title: string
  releaseVersion?: string | null
  label: string
  format: $Enums.ReleaseFormat
  compilation: boolean
  explicit: $Enums.ExplicitContent
  upc?: string | null
  catalogNumber: string
  releaseDate: Date | string
  preorderDate?: Date | string | null
  originalReleaseDate?: Date | string | null
  recordingYear: number
  recordingCountry: string
  copyrightYear: number
  copyrightHolder: string
  publishingYear: number
  publishingHolder: string
  courtesyLine?: string | null
  genre: string
  subGenre?: string | null
  language: string
  status?: $Enums.ReleaseStatus
  submittedAt?: Date | string | null
  approvedAt?: Date | string | null
  rejectedAt?: Date | string | null
  rejectedReason?: string | null
  userId: string
  createdAt?: Date | string
  updatedAt?: Date | string
  coverArts?: Prisma.ReleaseCoverArtUncheckedCreateNestedManyWithoutReleaseInput
  artists?: Prisma.ReleaseArtistUncheckedCreateNestedManyWithoutReleaseInput
}

export type ReleaseCreateOrConnectWithoutTracksInput = {
  where: Prisma.ReleaseWhereUniqueInput
  create: Prisma.XOR<Prisma.ReleaseCreateWithoutTracksInput, Prisma.ReleaseUncheckedCreateWithoutTracksInput>
}

export type ReleaseUpsertWithoutTracksInput = {
  update: Prisma.XOR<Prisma.ReleaseUpdateWithoutTracksInput, Prisma.ReleaseUncheckedUpdateWithoutTracksInput>
  create: Prisma.XOR<Prisma.ReleaseCreateWithoutTracksInput, Prisma.ReleaseUncheckedCreateWithoutTracksInput>
  where?: Prisma.ReleaseWhereInput
}

export type ReleaseUpdateToOneWithWhereWithoutTracksInput = {
  where?: Prisma.ReleaseWhereInput
  data: Prisma.XOR<Prisma.ReleaseUpdateWithoutTracksInput, Prisma.ReleaseUncheckedUpdateWithoutTracksInput>
}

export type ReleaseUpdateWithoutTracksInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  releaseVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  label?: Prisma.StringFieldUpdateOperationsInput | string
  format?: Prisma.EnumReleaseFormatFieldUpdateOperationsInput | $Enums.ReleaseFormat
  compilation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  explicit?: Prisma.EnumExplicitContentFieldUpdateOperationsInput | $Enums.ExplicitContent
  upc?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  catalogNumber?: Prisma.StringFieldUpdateOperationsInput | string
  releaseDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  preorderDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  originalReleaseDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  recordingYear?: Prisma.IntFieldUpdateOperationsInput | number
  recordingCountry?: Prisma.StringFieldUpdateOperationsInput | string
  copyrightYear?: Prisma.IntFieldUpdateOperationsInput | number
  copyrightHolder?: Prisma.StringFieldUpdateOperationsInput | string
  publishingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingHolder?: Prisma.StringFieldUpdateOperationsInput | string
  courtesyLine?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  genre?: Prisma.StringFieldUpdateOperationsInput | string
  subGenre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  language?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumReleaseStatusFieldUpdateOperationsInput | $Enums.ReleaseStatus
  submittedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  approvedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejectedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejectedReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  coverArts?: Prisma.ReleaseCoverArtUpdateManyWithoutReleaseNestedInput
  user?: Prisma.UserUpdateOneRequiredWithoutReleasesNestedInput
  artists?: Prisma.ReleaseArtistUpdateManyWithoutReleaseNestedInput
}

export type ReleaseUncheckedUpdateWithoutTracksInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  releaseVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  label?: Prisma.StringFieldUpdateOperationsInput | string
  format?: Prisma.EnumReleaseFormatFieldUpdateOperationsInput | $Enums.ReleaseFormat
  compilation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  explicit?: Prisma.EnumExplicitContentFieldUpdateOperationsInput | $Enums.ExplicitContent
  upc?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  catalogNumber?: Prisma.StringFieldUpdateOperationsInput | string
  releaseDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  preorderDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  originalReleaseDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  recordingYear?: Prisma.IntFieldUpdateOperationsInput | number
  recordingCountry?: Prisma.StringFieldUpdateOperationsInput | string
  copyrightYear?: Prisma.IntFieldUpdateOperationsInput | number
  copyrightHolder?: Prisma.StringFieldUpdateOperationsInput | string
  publishingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingHolder?: Prisma.StringFieldUpdateOperationsInput | string
  courtesyLine?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  genre?: Prisma.StringFieldUpdateOperationsInput | string
  subGenre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  language?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumReleaseStatusFieldUpdateOperationsInput | $Enums.ReleaseStatus
  submittedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  approvedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejectedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejectedReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  coverArts?: Prisma.ReleaseCoverArtUncheckedUpdateManyWithoutReleaseNestedInput
  artists?: Prisma.ReleaseArtistUncheckedUpdateManyWithoutReleaseNestedInput
}

export type ReleaseCreateWithoutCoverArtsInput = {
  id?: string
  title: string
  releaseVersion?: string | null
  label: string
  format: $Enums.ReleaseFormat
  compilation: boolean
  explicit: $Enums.ExplicitContent
  upc?: string | null
  catalogNumber: string
  releaseDate: Date | string
  preorderDate?: Date | string | null
  originalReleaseDate?: Date | string | null
  recordingYear: number
  recordingCountry: string
  copyrightYear: number
  copyrightHolder: string
  publishingYear: number
  publishingHolder: string
  courtesyLine?: string | null
  genre: string
  subGenre?: string | null
  language: string
  status?: $Enums.ReleaseStatus
  submittedAt?: Date | string | null
  approvedAt?: Date | string | null
  rejectedAt?: Date | string | null
  rejectedReason?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutReleasesInput
  artists?: Prisma.ReleaseArtistCreateNestedManyWithoutReleaseInput
  tracks?: Prisma.ReleaseTrackCreateNestedManyWithoutReleaseInput
}

export type ReleaseUncheckedCreateWithoutCoverArtsInput = {
  id?: string
  title: string
  releaseVersion?: string | null
  label: string
  format: $Enums.ReleaseFormat
  compilation: boolean
  explicit: $Enums.ExplicitContent
  upc?: string | null
  catalogNumber: string
  releaseDate: Date | string
  preorderDate?: Date | string | null
  originalReleaseDate?: Date | string | null
  recordingYear: number
  recordingCountry: string
  copyrightYear: number
  copyrightHolder: string
  publishingYear: number
  publishingHolder: string
  courtesyLine?: string | null
  genre: string
  subGenre?: string | null
  language: string
  status?: $Enums.ReleaseStatus
  submittedAt?: Date | string | null
  approvedAt?: Date | string | null
  rejectedAt?: Date | string | null
  rejectedReason?: string | null
  userId: string
  createdAt?: Date | string
  updatedAt?: Date | string
  artists?: Prisma.ReleaseArtistUncheckedCreateNestedManyWithoutReleaseInput
  tracks?: Prisma.ReleaseTrackUncheckedCreateNestedManyWithoutReleaseInput
}

export type ReleaseCreateOrConnectWithoutCoverArtsInput = {
  where: Prisma.ReleaseWhereUniqueInput
  create: Prisma.XOR<Prisma.ReleaseCreateWithoutCoverArtsInput, Prisma.ReleaseUncheckedCreateWithoutCoverArtsInput>
}

export type ReleaseUpsertWithoutCoverArtsInput = {
  update: Prisma.XOR<Prisma.ReleaseUpdateWithoutCoverArtsInput, Prisma.ReleaseUncheckedUpdateWithoutCoverArtsInput>
  create: Prisma.XOR<Prisma.ReleaseCreateWithoutCoverArtsInput, Prisma.ReleaseUncheckedCreateWithoutCoverArtsInput>
  where?: Prisma.ReleaseWhereInput
}

export type ReleaseUpdateToOneWithWhereWithoutCoverArtsInput = {
  where?: Prisma.ReleaseWhereInput
  data: Prisma.XOR<Prisma.ReleaseUpdateWithoutCoverArtsInput, Prisma.ReleaseUncheckedUpdateWithoutCoverArtsInput>
}

export type ReleaseUpdateWithoutCoverArtsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  releaseVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  label?: Prisma.StringFieldUpdateOperationsInput | string
  format?: Prisma.EnumReleaseFormatFieldUpdateOperationsInput | $Enums.ReleaseFormat
  compilation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  explicit?: Prisma.EnumExplicitContentFieldUpdateOperationsInput | $Enums.ExplicitContent
  upc?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  catalogNumber?: Prisma.StringFieldUpdateOperationsInput | string
  releaseDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  preorderDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  originalReleaseDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  recordingYear?: Prisma.IntFieldUpdateOperationsInput | number
  recordingCountry?: Prisma.StringFieldUpdateOperationsInput | string
  copyrightYear?: Prisma.IntFieldUpdateOperationsInput | number
  copyrightHolder?: Prisma.StringFieldUpdateOperationsInput | string
  publishingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingHolder?: Prisma.StringFieldUpdateOperationsInput | string
  courtesyLine?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  genre?: Prisma.StringFieldUpdateOperationsInput | string
  subGenre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  language?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumReleaseStatusFieldUpdateOperationsInput | $Enums.ReleaseStatus
  submittedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  approvedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejectedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejectedReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutReleasesNestedInput
  artists?: Prisma.ReleaseArtistUpdateManyWithoutReleaseNestedInput
  tracks?: Prisma.ReleaseTrackUpdateManyWithoutReleaseNestedInput
}

export type ReleaseUncheckedUpdateWithoutCoverArtsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  releaseVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  label?: Prisma.StringFieldUpdateOperationsInput | string
  format?: Prisma.EnumReleaseFormatFieldUpdateOperationsInput | $Enums.ReleaseFormat
  compilation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  explicit?: Prisma.EnumExplicitContentFieldUpdateOperationsInput | $Enums.ExplicitContent
  upc?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  catalogNumber?: Prisma.StringFieldUpdateOperationsInput | string
  releaseDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  preorderDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  originalReleaseDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  recordingYear?: Prisma.IntFieldUpdateOperationsInput | number
  recordingCountry?: Prisma.StringFieldUpdateOperationsInput | string
  copyrightYear?: Prisma.IntFieldUpdateOperationsInput | number
  copyrightHolder?: Prisma.StringFieldUpdateOperationsInput | string
  publishingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingHolder?: Prisma.StringFieldUpdateOperationsInput | string
  courtesyLine?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  genre?: Prisma.StringFieldUpdateOperationsInput | string
  subGenre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  language?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumReleaseStatusFieldUpdateOperationsInput | $Enums.ReleaseStatus
  submittedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  approvedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejectedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejectedReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  artists?: Prisma.ReleaseArtistUncheckedUpdateManyWithoutReleaseNestedInput
  tracks?: Prisma.ReleaseTrackUncheckedUpdateManyWithoutReleaseNestedInput
}

export type ReleaseCreateManyUserInput = {
  id?: string
  title: string
  releaseVersion?: string | null
  label: string
  format: $Enums.ReleaseFormat
  compilation: boolean
  explicit: $Enums.ExplicitContent
  upc?: string | null
  catalogNumber: string
  releaseDate: Date | string
  preorderDate?: Date | string | null
  originalReleaseDate?: Date | string | null
  recordingYear: number
  recordingCountry: string
  copyrightYear: number
  copyrightHolder: string
  publishingYear: number
  publishingHolder: string
  courtesyLine?: string | null
  genre: string
  subGenre?: string | null
  language: string
  status?: $Enums.ReleaseStatus
  submittedAt?: Date | string | null
  approvedAt?: Date | string | null
  rejectedAt?: Date | string | null
  rejectedReason?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type ReleaseUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  releaseVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  label?: Prisma.StringFieldUpdateOperationsInput | string
  format?: Prisma.EnumReleaseFormatFieldUpdateOperationsInput | $Enums.ReleaseFormat
  compilation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  explicit?: Prisma.EnumExplicitContentFieldUpdateOperationsInput | $Enums.ExplicitContent
  upc?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  catalogNumber?: Prisma.StringFieldUpdateOperationsInput | string
  releaseDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  preorderDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  originalReleaseDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  recordingYear?: Prisma.IntFieldUpdateOperationsInput | number
  recordingCountry?: Prisma.StringFieldUpdateOperationsInput | string
  copyrightYear?: Prisma.IntFieldUpdateOperationsInput | number
  copyrightHolder?: Prisma.StringFieldUpdateOperationsInput | string
  publishingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingHolder?: Prisma.StringFieldUpdateOperationsInput | string
  courtesyLine?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  genre?: Prisma.StringFieldUpdateOperationsInput | string
  subGenre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  language?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumReleaseStatusFieldUpdateOperationsInput | $Enums.ReleaseStatus
  submittedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  approvedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejectedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejectedReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  coverArts?: Prisma.ReleaseCoverArtUpdateManyWithoutReleaseNestedInput
  artists?: Prisma.ReleaseArtistUpdateManyWithoutReleaseNestedInput
  tracks?: Prisma.ReleaseTrackUpdateManyWithoutReleaseNestedInput
}

export type ReleaseUncheckedUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  releaseVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  label?: Prisma.StringFieldUpdateOperationsInput | string
  format?: Prisma.EnumReleaseFormatFieldUpdateOperationsInput | $Enums.ReleaseFormat
  compilation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  explicit?: Prisma.EnumExplicitContentFieldUpdateOperationsInput | $Enums.ExplicitContent
  upc?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  catalogNumber?: Prisma.StringFieldUpdateOperationsInput | string
  releaseDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  preorderDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  originalReleaseDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  recordingYear?: Prisma.IntFieldUpdateOperationsInput | number
  recordingCountry?: Prisma.StringFieldUpdateOperationsInput | string
  copyrightYear?: Prisma.IntFieldUpdateOperationsInput | number
  copyrightHolder?: Prisma.StringFieldUpdateOperationsInput | string
  publishingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingHolder?: Prisma.StringFieldUpdateOperationsInput | string
  courtesyLine?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  genre?: Prisma.StringFieldUpdateOperationsInput | string
  subGenre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  language?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumReleaseStatusFieldUpdateOperationsInput | $Enums.ReleaseStatus
  submittedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  approvedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejectedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejectedReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  coverArts?: Prisma.ReleaseCoverArtUncheckedUpdateManyWithoutReleaseNestedInput
  artists?: Prisma.ReleaseArtistUncheckedUpdateManyWithoutReleaseNestedInput
  tracks?: Prisma.ReleaseTrackUncheckedUpdateManyWithoutReleaseNestedInput
}

export type ReleaseUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  releaseVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  label?: Prisma.StringFieldUpdateOperationsInput | string
  format?: Prisma.EnumReleaseFormatFieldUpdateOperationsInput | $Enums.ReleaseFormat
  compilation?: Prisma.BoolFieldUpdateOperationsInput | boolean
  explicit?: Prisma.EnumExplicitContentFieldUpdateOperationsInput | $Enums.ExplicitContent
  upc?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  catalogNumber?: Prisma.StringFieldUpdateOperationsInput | string
  releaseDate?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  preorderDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  originalReleaseDate?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  recordingYear?: Prisma.IntFieldUpdateOperationsInput | number
  recordingCountry?: Prisma.StringFieldUpdateOperationsInput | string
  copyrightYear?: Prisma.IntFieldUpdateOperationsInput | number
  copyrightHolder?: Prisma.StringFieldUpdateOperationsInput | string
  publishingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingHolder?: Prisma.StringFieldUpdateOperationsInput | string
  courtesyLine?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  genre?: Prisma.StringFieldUpdateOperationsInput | string
  subGenre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  language?: Prisma.StringFieldUpdateOperationsInput | string
  status?: Prisma.EnumReleaseStatusFieldUpdateOperationsInput | $Enums.ReleaseStatus
  submittedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  approvedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejectedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  rejectedReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}


/**
 * Count Type ReleaseCountOutputType
 */

export type ReleaseCountOutputType = {
  coverArts: number
  artists: number
  tracks: number
}

export type ReleaseCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  coverArts?: boolean | ReleaseCountOutputTypeCountCoverArtsArgs
  artists?: boolean | ReleaseCountOutputTypeCountArtistsArgs
  tracks?: boolean | ReleaseCountOutputTypeCountTracksArgs
}

/**
 * ReleaseCountOutputType without action
 */
export type ReleaseCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseCountOutputType
   */
  select?: Prisma.ReleaseCountOutputTypeSelect<ExtArgs> | null
}

/**
 * ReleaseCountOutputType without action
 */
export type ReleaseCountOutputTypeCountCoverArtsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ReleaseCoverArtWhereInput
}

/**
 * ReleaseCountOutputType without action
 */
export type ReleaseCountOutputTypeCountArtistsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ReleaseArtistWhereInput
}

/**
 * ReleaseCountOutputType without action
 */
export type ReleaseCountOutputTypeCountTracksArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ReleaseTrackWhereInput
}


export type ReleaseSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  title?: boolean
  releaseVersion?: boolean
  label?: boolean
  format?: boolean
  compilation?: boolean
  explicit?: boolean
  upc?: boolean
  catalogNumber?: boolean
  releaseDate?: boolean
  preorderDate?: boolean
  originalReleaseDate?: boolean
  recordingYear?: boolean
  recordingCountry?: boolean
  copyrightYear?: boolean
  copyrightHolder?: boolean
  publishingYear?: boolean
  publishingHolder?: boolean
  courtesyLine?: boolean
  genre?: boolean
  subGenre?: boolean
  language?: boolean
  status?: boolean
  submittedAt?: boolean
  approvedAt?: boolean
  rejectedAt?: boolean
  rejectedReason?: boolean
  userId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  coverArts?: boolean | Prisma.Release$coverArtsArgs<ExtArgs>
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  artists?: boolean | Prisma.Release$artistsArgs<ExtArgs>
  tracks?: boolean | Prisma.Release$tracksArgs<ExtArgs>
  _count?: boolean | Prisma.ReleaseCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["release"]>

export type ReleaseSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  title?: boolean
  releaseVersion?: boolean
  label?: boolean
  format?: boolean
  compilation?: boolean
  explicit?: boolean
  upc?: boolean
  catalogNumber?: boolean
  releaseDate?: boolean
  preorderDate?: boolean
  originalReleaseDate?: boolean
  recordingYear?: boolean
  recordingCountry?: boolean
  copyrightYear?: boolean
  copyrightHolder?: boolean
  publishingYear?: boolean
  publishingHolder?: boolean
  courtesyLine?: boolean
  genre?: boolean
  subGenre?: boolean
  language?: boolean
  status?: boolean
  submittedAt?: boolean
  approvedAt?: boolean
  rejectedAt?: boolean
  rejectedReason?: boolean
  userId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}, ExtArgs["result"]["release"]>

export type ReleaseSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  title?: boolean
  releaseVersion?: boolean
  label?: boolean
  format?: boolean
  compilation?: boolean
  explicit?: boolean
  upc?: boolean
  catalogNumber?: boolean
  releaseDate?: boolean
  preorderDate?: boolean
  originalReleaseDate?: boolean
  recordingYear?: boolean
  recordingCountry?: boolean
  copyrightYear?: boolean
  copyrightHolder?: boolean
  publishingYear?: boolean
  publishingHolder?: boolean
  courtesyLine?: boolean
  genre?: boolean
  subGenre?: boolean
  language?: boolean
  status?: boolean
  submittedAt?: boolean
  approvedAt?: boolean
  rejectedAt?: boolean
  rejectedReason?: boolean
  userId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}, ExtArgs["result"]["release"]>

export type ReleaseSelectScalar = {
  id?: boolean
  title?: boolean
  releaseVersion?: boolean
  label?: boolean
  format?: boolean
  compilation?: boolean
  explicit?: boolean
  upc?: boolean
  catalogNumber?: boolean
  releaseDate?: boolean
  preorderDate?: boolean
  originalReleaseDate?: boolean
  recordingYear?: boolean
  recordingCountry?: boolean
  copyrightYear?: boolean
  copyrightHolder?: boolean
  publishingYear?: boolean
  publishingHolder?: boolean
  courtesyLine?: boolean
  genre?: boolean
  subGenre?: boolean
  language?: boolean
  status?: boolean
  submittedAt?: boolean
  approvedAt?: boolean
  rejectedAt?: boolean
  rejectedReason?: boolean
  userId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type ReleaseOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "title" | "releaseVersion" | "label" | "format" | "compilation" | "explicit" | "upc" | "catalogNumber" | "releaseDate" | "preorderDate" | "originalReleaseDate" | "recordingYear" | "recordingCountry" | "copyrightYear" | "copyrightHolder" | "publishingYear" | "publishingHolder" | "courtesyLine" | "genre" | "subGenre" | "language" | "status" | "submittedAt" | "approvedAt" | "rejectedAt" | "rejectedReason" | "userId" | "createdAt" | "updatedAt", ExtArgs["result"]["release"]>
export type ReleaseInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  coverArts?: boolean | Prisma.Release$coverArtsArgs<ExtArgs>
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  artists?: boolean | Prisma.Release$artistsArgs<ExtArgs>
  tracks?: boolean | Prisma.Release$tracksArgs<ExtArgs>
  _count?: boolean | Prisma.ReleaseCountOutputTypeDefaultArgs<ExtArgs>
}
export type ReleaseIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}
export type ReleaseIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}

export type $ReleasePayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Release"
  objects: {
    coverArts: Prisma.$ReleaseCoverArtPayload<ExtArgs>[]
    user: Prisma.$UserPayload<ExtArgs>
    artists: Prisma.$ReleaseArtistPayload<ExtArgs>[]
    tracks: Prisma.$ReleaseTrackPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    title: string
    releaseVersion: string | null
    label: string
    format: $Enums.ReleaseFormat
    compilation: boolean
    explicit: $Enums.ExplicitContent
    upc: string | null
    catalogNumber: string
    releaseDate: Date
    preorderDate: Date | null
    originalReleaseDate: Date | null
    recordingYear: number
    recordingCountry: string
    copyrightYear: number
    copyrightHolder: string
    publishingYear: number
    publishingHolder: string
    courtesyLine: string | null
    genre: string
    subGenre: string | null
    language: string
    status: $Enums.ReleaseStatus
    submittedAt: Date | null
    approvedAt: Date | null
    rejectedAt: Date | null
    rejectedReason: string | null
    userId: string
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["release"]>
  composites: {}
}

export type ReleaseGetPayload<S extends boolean | null | undefined | ReleaseDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$ReleasePayload, S>

export type ReleaseCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<ReleaseFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: ReleaseCountAggregateInputType | true
  }

export interface ReleaseDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Release'], meta: { name: 'Release' } }
  /**
   * Find zero or one Release that matches the filter.
   * @param {ReleaseFindUniqueArgs} args - Arguments to find a Release
   * @example
   * // Get one Release
   * const release = await prisma.release.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends ReleaseFindUniqueArgs>(args: Prisma.SelectSubset<T, ReleaseFindUniqueArgs<ExtArgs>>): Prisma.Prisma__ReleaseClient<runtime.Types.Result.GetResult<Prisma.$ReleasePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Release that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {ReleaseFindUniqueOrThrowArgs} args - Arguments to find a Release
   * @example
   * // Get one Release
   * const release = await prisma.release.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends ReleaseFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, ReleaseFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__ReleaseClient<runtime.Types.Result.GetResult<Prisma.$ReleasePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Release that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReleaseFindFirstArgs} args - Arguments to find a Release
   * @example
   * // Get one Release
   * const release = await prisma.release.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends ReleaseFindFirstArgs>(args?: Prisma.SelectSubset<T, ReleaseFindFirstArgs<ExtArgs>>): Prisma.Prisma__ReleaseClient<runtime.Types.Result.GetResult<Prisma.$ReleasePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Release that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReleaseFindFirstOrThrowArgs} args - Arguments to find a Release
   * @example
   * // Get one Release
   * const release = await prisma.release.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends ReleaseFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, ReleaseFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__ReleaseClient<runtime.Types.Result.GetResult<Prisma.$ReleasePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Releases that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReleaseFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Releases
   * const releases = await prisma.release.findMany()
   * 
   * // Get first 10 Releases
   * const releases = await prisma.release.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const releaseWithIdOnly = await prisma.release.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends ReleaseFindManyArgs>(args?: Prisma.SelectSubset<T, ReleaseFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ReleasePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Release.
   * @param {ReleaseCreateArgs} args - Arguments to create a Release.
   * @example
   * // Create one Release
   * const Release = await prisma.release.create({
   *   data: {
   *     // ... data to create a Release
   *   }
   * })
   * 
   */
  create<T extends ReleaseCreateArgs>(args: Prisma.SelectSubset<T, ReleaseCreateArgs<ExtArgs>>): Prisma.Prisma__ReleaseClient<runtime.Types.Result.GetResult<Prisma.$ReleasePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Releases.
   * @param {ReleaseCreateManyArgs} args - Arguments to create many Releases.
   * @example
   * // Create many Releases
   * const release = await prisma.release.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends ReleaseCreateManyArgs>(args?: Prisma.SelectSubset<T, ReleaseCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Releases and returns the data saved in the database.
   * @param {ReleaseCreateManyAndReturnArgs} args - Arguments to create many Releases.
   * @example
   * // Create many Releases
   * const release = await prisma.release.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Releases and only return the `id`
   * const releaseWithIdOnly = await prisma.release.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends ReleaseCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, ReleaseCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ReleasePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a Release.
   * @param {ReleaseDeleteArgs} args - Arguments to delete one Release.
   * @example
   * // Delete one Release
   * const Release = await prisma.release.delete({
   *   where: {
   *     // ... filter to delete one Release
   *   }
   * })
   * 
   */
  delete<T extends ReleaseDeleteArgs>(args: Prisma.SelectSubset<T, ReleaseDeleteArgs<ExtArgs>>): Prisma.Prisma__ReleaseClient<runtime.Types.Result.GetResult<Prisma.$ReleasePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Release.
   * @param {ReleaseUpdateArgs} args - Arguments to update one Release.
   * @example
   * // Update one Release
   * const release = await prisma.release.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends ReleaseUpdateArgs>(args: Prisma.SelectSubset<T, ReleaseUpdateArgs<ExtArgs>>): Prisma.Prisma__ReleaseClient<runtime.Types.Result.GetResult<Prisma.$ReleasePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Releases.
   * @param {ReleaseDeleteManyArgs} args - Arguments to filter Releases to delete.
   * @example
   * // Delete a few Releases
   * const { count } = await prisma.release.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends ReleaseDeleteManyArgs>(args?: Prisma.SelectSubset<T, ReleaseDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Releases.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReleaseUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Releases
   * const release = await prisma.release.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends ReleaseUpdateManyArgs>(args: Prisma.SelectSubset<T, ReleaseUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Releases and returns the data updated in the database.
   * @param {ReleaseUpdateManyAndReturnArgs} args - Arguments to update many Releases.
   * @example
   * // Update many Releases
   * const release = await prisma.release.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Releases and only return the `id`
   * const releaseWithIdOnly = await prisma.release.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends ReleaseUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, ReleaseUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ReleasePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one Release.
   * @param {ReleaseUpsertArgs} args - Arguments to update or create a Release.
   * @example
   * // Update or create a Release
   * const release = await prisma.release.upsert({
   *   create: {
   *     // ... data to create a Release
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Release we want to update
   *   }
   * })
   */
  upsert<T extends ReleaseUpsertArgs>(args: Prisma.SelectSubset<T, ReleaseUpsertArgs<ExtArgs>>): Prisma.Prisma__ReleaseClient<runtime.Types.Result.GetResult<Prisma.$ReleasePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Releases.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReleaseCountArgs} args - Arguments to filter Releases to count.
   * @example
   * // Count the number of Releases
   * const count = await prisma.release.count({
   *   where: {
   *     // ... the filter for the Releases we want to count
   *   }
   * })
  **/
  count<T extends ReleaseCountArgs>(
    args?: Prisma.Subset<T, ReleaseCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], ReleaseCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Release.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReleaseAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends ReleaseAggregateArgs>(args: Prisma.Subset<T, ReleaseAggregateArgs>): Prisma.PrismaPromise<GetReleaseAggregateType<T>>

  /**
   * Group by Release.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReleaseGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends ReleaseGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: ReleaseGroupByArgs['orderBy'] }
      : { orderBy?: ReleaseGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, ReleaseGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetReleaseGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Release model
 */
readonly fields: ReleaseFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Release.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__ReleaseClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  coverArts<T extends Prisma.Release$coverArtsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Release$coverArtsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ReleaseCoverArtPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  user<T extends Prisma.UserDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.UserDefaultArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  artists<T extends Prisma.Release$artistsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Release$artistsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ReleaseArtistPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  tracks<T extends Prisma.Release$tracksArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Release$tracksArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ReleaseTrackPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Release model
 */
export interface ReleaseFieldRefs {
  readonly id: Prisma.FieldRef<"Release", 'String'>
  readonly title: Prisma.FieldRef<"Release", 'String'>
  readonly releaseVersion: Prisma.FieldRef<"Release", 'String'>
  readonly label: Prisma.FieldRef<"Release", 'String'>
  readonly format: Prisma.FieldRef<"Release", 'ReleaseFormat'>
  readonly compilation: Prisma.FieldRef<"Release", 'Boolean'>
  readonly explicit: Prisma.FieldRef<"Release", 'ExplicitContent'>
  readonly upc: Prisma.FieldRef<"Release", 'String'>
  readonly catalogNumber: Prisma.FieldRef<"Release", 'String'>
  readonly releaseDate: Prisma.FieldRef<"Release", 'DateTime'>
  readonly preorderDate: Prisma.FieldRef<"Release", 'DateTime'>
  readonly originalReleaseDate: Prisma.FieldRef<"Release", 'DateTime'>
  readonly recordingYear: Prisma.FieldRef<"Release", 'Int'>
  readonly recordingCountry: Prisma.FieldRef<"Release", 'String'>
  readonly copyrightYear: Prisma.FieldRef<"Release", 'Int'>
  readonly copyrightHolder: Prisma.FieldRef<"Release", 'String'>
  readonly publishingYear: Prisma.FieldRef<"Release", 'Int'>
  readonly publishingHolder: Prisma.FieldRef<"Release", 'String'>
  readonly courtesyLine: Prisma.FieldRef<"Release", 'String'>
  readonly genre: Prisma.FieldRef<"Release", 'String'>
  readonly subGenre: Prisma.FieldRef<"Release", 'String'>
  readonly language: Prisma.FieldRef<"Release", 'String'>
  readonly status: Prisma.FieldRef<"Release", 'ReleaseStatus'>
  readonly submittedAt: Prisma.FieldRef<"Release", 'DateTime'>
  readonly approvedAt: Prisma.FieldRef<"Release", 'DateTime'>
  readonly rejectedAt: Prisma.FieldRef<"Release", 'DateTime'>
  readonly rejectedReason: Prisma.FieldRef<"Release", 'String'>
  readonly userId: Prisma.FieldRef<"Release", 'String'>
  readonly createdAt: Prisma.FieldRef<"Release", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"Release", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Release findUnique
 */
export type ReleaseFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Release
   */
  select?: Prisma.ReleaseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Release
   */
  omit?: Prisma.ReleaseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseInclude<ExtArgs> | null
  /**
   * Filter, which Release to fetch.
   */
  where: Prisma.ReleaseWhereUniqueInput
}

/**
 * Release findUniqueOrThrow
 */
export type ReleaseFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Release
   */
  select?: Prisma.ReleaseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Release
   */
  omit?: Prisma.ReleaseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseInclude<ExtArgs> | null
  /**
   * Filter, which Release to fetch.
   */
  where: Prisma.ReleaseWhereUniqueInput
}

/**
 * Release findFirst
 */
export type ReleaseFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Release
   */
  select?: Prisma.ReleaseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Release
   */
  omit?: Prisma.ReleaseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseInclude<ExtArgs> | null
  /**
   * Filter, which Release to fetch.
   */
  where?: Prisma.ReleaseWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Releases to fetch.
   */
  orderBy?: Prisma.ReleaseOrderByWithRelationInput | Prisma.ReleaseOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Releases.
   */
  cursor?: Prisma.ReleaseWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Releases from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Releases.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Releases.
   */
  distinct?: Prisma.ReleaseScalarFieldEnum | Prisma.ReleaseScalarFieldEnum[]
}

/**
 * Release findFirstOrThrow
 */
export type ReleaseFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Release
   */
  select?: Prisma.ReleaseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Release
   */
  omit?: Prisma.ReleaseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseInclude<ExtArgs> | null
  /**
   * Filter, which Release to fetch.
   */
  where?: Prisma.ReleaseWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Releases to fetch.
   */
  orderBy?: Prisma.ReleaseOrderByWithRelationInput | Prisma.ReleaseOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Releases.
   */
  cursor?: Prisma.ReleaseWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Releases from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Releases.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Releases.
   */
  distinct?: Prisma.ReleaseScalarFieldEnum | Prisma.ReleaseScalarFieldEnum[]
}

/**
 * Release findMany
 */
export type ReleaseFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Release
   */
  select?: Prisma.ReleaseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Release
   */
  omit?: Prisma.ReleaseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseInclude<ExtArgs> | null
  /**
   * Filter, which Releases to fetch.
   */
  where?: Prisma.ReleaseWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Releases to fetch.
   */
  orderBy?: Prisma.ReleaseOrderByWithRelationInput | Prisma.ReleaseOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Releases.
   */
  cursor?: Prisma.ReleaseWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Releases from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Releases.
   */
  skip?: number
  distinct?: Prisma.ReleaseScalarFieldEnum | Prisma.ReleaseScalarFieldEnum[]
}

/**
 * Release create
 */
export type ReleaseCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Release
   */
  select?: Prisma.ReleaseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Release
   */
  omit?: Prisma.ReleaseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseInclude<ExtArgs> | null
  /**
   * The data needed to create a Release.
   */
  data: Prisma.XOR<Prisma.ReleaseCreateInput, Prisma.ReleaseUncheckedCreateInput>
}

/**
 * Release createMany
 */
export type ReleaseCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Releases.
   */
  data: Prisma.ReleaseCreateManyInput | Prisma.ReleaseCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Release createManyAndReturn
 */
export type ReleaseCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Release
   */
  select?: Prisma.ReleaseSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Release
   */
  omit?: Prisma.ReleaseOmit<ExtArgs> | null
  /**
   * The data used to create many Releases.
   */
  data: Prisma.ReleaseCreateManyInput | Prisma.ReleaseCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * Release update
 */
export type ReleaseUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Release
   */
  select?: Prisma.ReleaseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Release
   */
  omit?: Prisma.ReleaseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseInclude<ExtArgs> | null
  /**
   * The data needed to update a Release.
   */
  data: Prisma.XOR<Prisma.ReleaseUpdateInput, Prisma.ReleaseUncheckedUpdateInput>
  /**
   * Choose, which Release to update.
   */
  where: Prisma.ReleaseWhereUniqueInput
}

/**
 * Release updateMany
 */
export type ReleaseUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Releases.
   */
  data: Prisma.XOR<Prisma.ReleaseUpdateManyMutationInput, Prisma.ReleaseUncheckedUpdateManyInput>
  /**
   * Filter which Releases to update
   */
  where?: Prisma.ReleaseWhereInput
  /**
   * Limit how many Releases to update.
   */
  limit?: number
}

/**
 * Release updateManyAndReturn
 */
export type ReleaseUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Release
   */
  select?: Prisma.ReleaseSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Release
   */
  omit?: Prisma.ReleaseOmit<ExtArgs> | null
  /**
   * The data used to update Releases.
   */
  data: Prisma.XOR<Prisma.ReleaseUpdateManyMutationInput, Prisma.ReleaseUncheckedUpdateManyInput>
  /**
   * Filter which Releases to update
   */
  where?: Prisma.ReleaseWhereInput
  /**
   * Limit how many Releases to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * Release upsert
 */
export type ReleaseUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Release
   */
  select?: Prisma.ReleaseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Release
   */
  omit?: Prisma.ReleaseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseInclude<ExtArgs> | null
  /**
   * The filter to search for the Release to update in case it exists.
   */
  where: Prisma.ReleaseWhereUniqueInput
  /**
   * In case the Release found by the `where` argument doesn't exist, create a new Release with this data.
   */
  create: Prisma.XOR<Prisma.ReleaseCreateInput, Prisma.ReleaseUncheckedCreateInput>
  /**
   * In case the Release was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.ReleaseUpdateInput, Prisma.ReleaseUncheckedUpdateInput>
}

/**
 * Release delete
 */
export type ReleaseDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Release
   */
  select?: Prisma.ReleaseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Release
   */
  omit?: Prisma.ReleaseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseInclude<ExtArgs> | null
  /**
   * Filter which Release to delete.
   */
  where: Prisma.ReleaseWhereUniqueInput
}

/**
 * Release deleteMany
 */
export type ReleaseDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Releases to delete
   */
  where?: Prisma.ReleaseWhereInput
  /**
   * Limit how many Releases to delete.
   */
  limit?: number
}

/**
 * Release.coverArts
 */
export type Release$coverArtsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseCoverArt
   */
  select?: Prisma.ReleaseCoverArtSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseCoverArt
   */
  omit?: Prisma.ReleaseCoverArtOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseCoverArtInclude<ExtArgs> | null
  where?: Prisma.ReleaseCoverArtWhereInput
  orderBy?: Prisma.ReleaseCoverArtOrderByWithRelationInput | Prisma.ReleaseCoverArtOrderByWithRelationInput[]
  cursor?: Prisma.ReleaseCoverArtWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.ReleaseCoverArtScalarFieldEnum | Prisma.ReleaseCoverArtScalarFieldEnum[]
}

/**
 * Release.artists
 */
export type Release$artistsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseArtist
   */
  select?: Prisma.ReleaseArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseArtist
   */
  omit?: Prisma.ReleaseArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseArtistInclude<ExtArgs> | null
  where?: Prisma.ReleaseArtistWhereInput
  orderBy?: Prisma.ReleaseArtistOrderByWithRelationInput | Prisma.ReleaseArtistOrderByWithRelationInput[]
  cursor?: Prisma.ReleaseArtistWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.ReleaseArtistScalarFieldEnum | Prisma.ReleaseArtistScalarFieldEnum[]
}

/**
 * Release.tracks
 */
export type Release$tracksArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseTrack
   */
  select?: Prisma.ReleaseTrackSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseTrack
   */
  omit?: Prisma.ReleaseTrackOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseTrackInclude<ExtArgs> | null
  where?: Prisma.ReleaseTrackWhereInput
  orderBy?: Prisma.ReleaseTrackOrderByWithRelationInput | Prisma.ReleaseTrackOrderByWithRelationInput[]
  cursor?: Prisma.ReleaseTrackWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.ReleaseTrackScalarFieldEnum | Prisma.ReleaseTrackScalarFieldEnum[]
}

/**
 * Release without action
 */
export type ReleaseDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Release
   */
  select?: Prisma.ReleaseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Release
   */
  omit?: Prisma.ReleaseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseInclude<ExtArgs> | null
}
