
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Session` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Session
 * 
 */
export type SessionModel = runtime.Types.Result.DefaultSelection<Prisma.$SessionPayload>

export type AggregateSession = {
  _count: SessionCountAggregateOutputType | null
  _min: SessionMinAggregateOutputType | null
  _max: SessionMaxAggregateOutputType | null
}

export type SessionMinAggregateOutputType = {
  id: string | null
  expiresAt: Date | null
  token: string | null
  createdAt: Date | null
  updatedAt: Date | null
  ipAddress: string | null
  userAgent: string | null
  impersonatedBy: string | null
  userId: string | null
}

export type SessionMaxAggregateOutputType = {
  id: string | null
  expiresAt: Date | null
  token: string | null
  createdAt: Date | null
  updatedAt: Date | null
  ipAddress: string | null
  userAgent: string | null
  impersonatedBy: string | null
  userId: string | null
}

export type SessionCountAggregateOutputType = {
  id: number
  expiresAt: number
  token: number
  createdAt: number
  updatedAt: number
  ipAddress: number
  userAgent: number
  impersonatedBy: number
  userId: number
  _all: number
}


export type SessionMinAggregateInputType = {
  id?: true
  expiresAt?: true
  token?: true
  createdAt?: true
  updatedAt?: true
  ipAddress?: true
  userAgent?: true
  impersonatedBy?: true
  userId?: true
}

export type SessionMaxAggregateInputType = {
  id?: true
  expiresAt?: true
  token?: true
  createdAt?: true
  updatedAt?: true
  ipAddress?: true
  userAgent?: true
  impersonatedBy?: true
  userId?: true
}

export type SessionCountAggregateInputType = {
  id?: true
  expiresAt?: true
  token?: true
  createdAt?: true
  updatedAt?: true
  ipAddress?: true
  userAgent?: true
  impersonatedBy?: true
  userId?: true
  _all?: true
}

export type SessionAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Session to aggregate.
   */
  where?: Prisma.SessionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Sessions to fetch.
   */
  orderBy?: Prisma.SessionOrderByWithRelationInput | Prisma.SessionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.SessionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Sessions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Sessions.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Sessions
  **/
  _count?: true | SessionCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: SessionMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: SessionMaxAggregateInputType
}

export type GetSessionAggregateType<T extends SessionAggregateArgs> = {
      [P in keyof T & keyof AggregateSession]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateSession[P]>
    : Prisma.GetScalarType<T[P], AggregateSession[P]>
}




export type SessionGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.SessionWhereInput
  orderBy?: Prisma.SessionOrderByWithAggregationInput | Prisma.SessionOrderByWithAggregationInput[]
  by: Prisma.SessionScalarFieldEnum[] | Prisma.SessionScalarFieldEnum
  having?: Prisma.SessionScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: SessionCountAggregateInputType | true
  _min?: SessionMinAggregateInputType
  _max?: SessionMaxAggregateInputType
}

export type SessionGroupByOutputType = {
  id: string
  expiresAt: Date
  token: string
  createdAt: Date
  updatedAt: Date
  ipAddress: string | null
  userAgent: string | null
  impersonatedBy: string | null
  userId: string
  _count: SessionCountAggregateOutputType | null
  _min: SessionMinAggregateOutputType | null
  _max: SessionMaxAggregateOutputType | null
}

type GetSessionGroupByPayload<T extends SessionGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<SessionGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof SessionGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], SessionGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], SessionGroupByOutputType[P]>
      }
    >
  > 



export type SessionWhereInput = {
  AND?: Prisma.SessionWhereInput | Prisma.SessionWhereInput[]
  OR?: Prisma.SessionWhereInput[]
  NOT?: Prisma.SessionWhereInput | Prisma.SessionWhereInput[]
  id?: Prisma.StringFilter<"Session"> | string
  expiresAt?: Prisma.DateTimeFilter<"Session"> | Date | string
  token?: Prisma.StringFilter<"Session"> | string
  createdAt?: Prisma.DateTimeFilter<"Session"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Session"> | Date | string
  ipAddress?: Prisma.StringNullableFilter<"Session"> | string | null
  userAgent?: Prisma.StringNullableFilter<"Session"> | string | null
  impersonatedBy?: Prisma.StringNullableFilter<"Session"> | string | null
  userId?: Prisma.StringFilter<"Session"> | string
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
}

export type SessionOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  token?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  ipAddress?: Prisma.SortOrderInput | Prisma.SortOrder
  userAgent?: Prisma.SortOrderInput | Prisma.SortOrder
  impersonatedBy?: Prisma.SortOrderInput | Prisma.SortOrder
  userId?: Prisma.SortOrder
  user?: Prisma.UserOrderByWithRelationInput
}

export type SessionWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  token?: string
  AND?: Prisma.SessionWhereInput | Prisma.SessionWhereInput[]
  OR?: Prisma.SessionWhereInput[]
  NOT?: Prisma.SessionWhereInput | Prisma.SessionWhereInput[]
  expiresAt?: Prisma.DateTimeFilter<"Session"> | Date | string
  createdAt?: Prisma.DateTimeFilter<"Session"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Session"> | Date | string
  ipAddress?: Prisma.StringNullableFilter<"Session"> | string | null
  userAgent?: Prisma.StringNullableFilter<"Session"> | string | null
  impersonatedBy?: Prisma.StringNullableFilter<"Session"> | string | null
  userId?: Prisma.StringFilter<"Session"> | string
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
}, "id" | "token">

export type SessionOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  token?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  ipAddress?: Prisma.SortOrderInput | Prisma.SortOrder
  userAgent?: Prisma.SortOrderInput | Prisma.SortOrder
  impersonatedBy?: Prisma.SortOrderInput | Prisma.SortOrder
  userId?: Prisma.SortOrder
  _count?: Prisma.SessionCountOrderByAggregateInput
  _max?: Prisma.SessionMaxOrderByAggregateInput
  _min?: Prisma.SessionMinOrderByAggregateInput
}

export type SessionScalarWhereWithAggregatesInput = {
  AND?: Prisma.SessionScalarWhereWithAggregatesInput | Prisma.SessionScalarWhereWithAggregatesInput[]
  OR?: Prisma.SessionScalarWhereWithAggregatesInput[]
  NOT?: Prisma.SessionScalarWhereWithAggregatesInput | Prisma.SessionScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Session"> | string
  expiresAt?: Prisma.DateTimeWithAggregatesFilter<"Session"> | Date | string
  token?: Prisma.StringWithAggregatesFilter<"Session"> | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Session"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"Session"> | Date | string
  ipAddress?: Prisma.StringNullableWithAggregatesFilter<"Session"> | string | null
  userAgent?: Prisma.StringNullableWithAggregatesFilter<"Session"> | string | null
  impersonatedBy?: Prisma.StringNullableWithAggregatesFilter<"Session"> | string | null
  userId?: Prisma.StringWithAggregatesFilter<"Session"> | string
}

export type SessionCreateInput = {
  id: string
  expiresAt: Date | string
  token: string
  createdAt: Date | string
  updatedAt: Date | string
  ipAddress?: string | null
  userAgent?: string | null
  impersonatedBy?: string | null
  user: Prisma.UserCreateNestedOneWithoutSessionsInput
}

export type SessionUncheckedCreateInput = {
  id: string
  expiresAt: Date | string
  token: string
  createdAt: Date | string
  updatedAt: Date | string
  ipAddress?: string | null
  userAgent?: string | null
  impersonatedBy?: string | null
  userId: string
}

export type SessionUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  token?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  ipAddress?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  impersonatedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  user?: Prisma.UserUpdateOneRequiredWithoutSessionsNestedInput
}

export type SessionUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  token?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  ipAddress?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  impersonatedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type SessionCreateManyInput = {
  id: string
  expiresAt: Date | string
  token: string
  createdAt: Date | string
  updatedAt: Date | string
  ipAddress?: string | null
  userAgent?: string | null
  impersonatedBy?: string | null
  userId: string
}

export type SessionUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  token?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  ipAddress?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  impersonatedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type SessionUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  token?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  ipAddress?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  impersonatedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userId?: Prisma.StringFieldUpdateOperationsInput | string
}

export type SessionListRelationFilter = {
  every?: Prisma.SessionWhereInput
  some?: Prisma.SessionWhereInput
  none?: Prisma.SessionWhereInput
}

export type SessionOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type SessionCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  token?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  ipAddress?: Prisma.SortOrder
  userAgent?: Prisma.SortOrder
  impersonatedBy?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type SessionMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  token?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  ipAddress?: Prisma.SortOrder
  userAgent?: Prisma.SortOrder
  impersonatedBy?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type SessionMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  expiresAt?: Prisma.SortOrder
  token?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  ipAddress?: Prisma.SortOrder
  userAgent?: Prisma.SortOrder
  impersonatedBy?: Prisma.SortOrder
  userId?: Prisma.SortOrder
}

export type SessionCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.SessionCreateWithoutUserInput, Prisma.SessionUncheckedCreateWithoutUserInput> | Prisma.SessionCreateWithoutUserInput[] | Prisma.SessionUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.SessionCreateOrConnectWithoutUserInput | Prisma.SessionCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.SessionCreateManyUserInputEnvelope
  connect?: Prisma.SessionWhereUniqueInput | Prisma.SessionWhereUniqueInput[]
}

export type SessionUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.SessionCreateWithoutUserInput, Prisma.SessionUncheckedCreateWithoutUserInput> | Prisma.SessionCreateWithoutUserInput[] | Prisma.SessionUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.SessionCreateOrConnectWithoutUserInput | Prisma.SessionCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.SessionCreateManyUserInputEnvelope
  connect?: Prisma.SessionWhereUniqueInput | Prisma.SessionWhereUniqueInput[]
}

export type SessionUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.SessionCreateWithoutUserInput, Prisma.SessionUncheckedCreateWithoutUserInput> | Prisma.SessionCreateWithoutUserInput[] | Prisma.SessionUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.SessionCreateOrConnectWithoutUserInput | Prisma.SessionCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.SessionUpsertWithWhereUniqueWithoutUserInput | Prisma.SessionUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.SessionCreateManyUserInputEnvelope
  set?: Prisma.SessionWhereUniqueInput | Prisma.SessionWhereUniqueInput[]
  disconnect?: Prisma.SessionWhereUniqueInput | Prisma.SessionWhereUniqueInput[]
  delete?: Prisma.SessionWhereUniqueInput | Prisma.SessionWhereUniqueInput[]
  connect?: Prisma.SessionWhereUniqueInput | Prisma.SessionWhereUniqueInput[]
  update?: Prisma.SessionUpdateWithWhereUniqueWithoutUserInput | Prisma.SessionUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.SessionUpdateManyWithWhereWithoutUserInput | Prisma.SessionUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.SessionScalarWhereInput | Prisma.SessionScalarWhereInput[]
}

export type SessionUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.SessionCreateWithoutUserInput, Prisma.SessionUncheckedCreateWithoutUserInput> | Prisma.SessionCreateWithoutUserInput[] | Prisma.SessionUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.SessionCreateOrConnectWithoutUserInput | Prisma.SessionCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.SessionUpsertWithWhereUniqueWithoutUserInput | Prisma.SessionUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.SessionCreateManyUserInputEnvelope
  set?: Prisma.SessionWhereUniqueInput | Prisma.SessionWhereUniqueInput[]
  disconnect?: Prisma.SessionWhereUniqueInput | Prisma.SessionWhereUniqueInput[]
  delete?: Prisma.SessionWhereUniqueInput | Prisma.SessionWhereUniqueInput[]
  connect?: Prisma.SessionWhereUniqueInput | Prisma.SessionWhereUniqueInput[]
  update?: Prisma.SessionUpdateWithWhereUniqueWithoutUserInput | Prisma.SessionUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.SessionUpdateManyWithWhereWithoutUserInput | Prisma.SessionUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.SessionScalarWhereInput | Prisma.SessionScalarWhereInput[]
}

export type SessionCreateWithoutUserInput = {
  id: string
  expiresAt: Date | string
  token: string
  createdAt: Date | string
  updatedAt: Date | string
  ipAddress?: string | null
  userAgent?: string | null
  impersonatedBy?: string | null
}

export type SessionUncheckedCreateWithoutUserInput = {
  id: string
  expiresAt: Date | string
  token: string
  createdAt: Date | string
  updatedAt: Date | string
  ipAddress?: string | null
  userAgent?: string | null
  impersonatedBy?: string | null
}

export type SessionCreateOrConnectWithoutUserInput = {
  where: Prisma.SessionWhereUniqueInput
  create: Prisma.XOR<Prisma.SessionCreateWithoutUserInput, Prisma.SessionUncheckedCreateWithoutUserInput>
}

export type SessionCreateManyUserInputEnvelope = {
  data: Prisma.SessionCreateManyUserInput | Prisma.SessionCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type SessionUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.SessionWhereUniqueInput
  update: Prisma.XOR<Prisma.SessionUpdateWithoutUserInput, Prisma.SessionUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.SessionCreateWithoutUserInput, Prisma.SessionUncheckedCreateWithoutUserInput>
}

export type SessionUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.SessionWhereUniqueInput
  data: Prisma.XOR<Prisma.SessionUpdateWithoutUserInput, Prisma.SessionUncheckedUpdateWithoutUserInput>
}

export type SessionUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.SessionScalarWhereInput
  data: Prisma.XOR<Prisma.SessionUpdateManyMutationInput, Prisma.SessionUncheckedUpdateManyWithoutUserInput>
}

export type SessionScalarWhereInput = {
  AND?: Prisma.SessionScalarWhereInput | Prisma.SessionScalarWhereInput[]
  OR?: Prisma.SessionScalarWhereInput[]
  NOT?: Prisma.SessionScalarWhereInput | Prisma.SessionScalarWhereInput[]
  id?: Prisma.StringFilter<"Session"> | string
  expiresAt?: Prisma.DateTimeFilter<"Session"> | Date | string
  token?: Prisma.StringFilter<"Session"> | string
  createdAt?: Prisma.DateTimeFilter<"Session"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Session"> | Date | string
  ipAddress?: Prisma.StringNullableFilter<"Session"> | string | null
  userAgent?: Prisma.StringNullableFilter<"Session"> | string | null
  impersonatedBy?: Prisma.StringNullableFilter<"Session"> | string | null
  userId?: Prisma.StringFilter<"Session"> | string
}

export type SessionCreateManyUserInput = {
  id: string
  expiresAt: Date | string
  token: string
  createdAt: Date | string
  updatedAt: Date | string
  ipAddress?: string | null
  userAgent?: string | null
  impersonatedBy?: string | null
}

export type SessionUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  token?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  ipAddress?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  impersonatedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type SessionUncheckedUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  token?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  ipAddress?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  impersonatedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}

export type SessionUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  expiresAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  token?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  ipAddress?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userAgent?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  impersonatedBy?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
}



export type SessionSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  expiresAt?: boolean
  token?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  ipAddress?: boolean
  userAgent?: boolean
  impersonatedBy?: boolean
  userId?: boolean
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}, ExtArgs["result"]["session"]>

export type SessionSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  expiresAt?: boolean
  token?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  ipAddress?: boolean
  userAgent?: boolean
  impersonatedBy?: boolean
  userId?: boolean
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}, ExtArgs["result"]["session"]>

export type SessionSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  expiresAt?: boolean
  token?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  ipAddress?: boolean
  userAgent?: boolean
  impersonatedBy?: boolean
  userId?: boolean
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}, ExtArgs["result"]["session"]>

export type SessionSelectScalar = {
  id?: boolean
  expiresAt?: boolean
  token?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  ipAddress?: boolean
  userAgent?: boolean
  impersonatedBy?: boolean
  userId?: boolean
}

export type SessionOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "expiresAt" | "token" | "createdAt" | "updatedAt" | "ipAddress" | "userAgent" | "impersonatedBy" | "userId", ExtArgs["result"]["session"]>
export type SessionInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}
export type SessionIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}
export type SessionIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}

export type $SessionPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Session"
  objects: {
    user: Prisma.$UserPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    expiresAt: Date
    token: string
    createdAt: Date
    updatedAt: Date
    ipAddress: string | null
    userAgent: string | null
    impersonatedBy: string | null
    userId: string
  }, ExtArgs["result"]["session"]>
  composites: {}
}

export type SessionGetPayload<S extends boolean | null | undefined | SessionDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$SessionPayload, S>

export type SessionCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<SessionFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: SessionCountAggregateInputType | true
  }

export interface SessionDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Session'], meta: { name: 'Session' } }
  /**
   * Find zero or one Session that matches the filter.
   * @param {SessionFindUniqueArgs} args - Arguments to find a Session
   * @example
   * // Get one Session
   * const session = await prisma.session.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends SessionFindUniqueArgs>(args: Prisma.SelectSubset<T, SessionFindUniqueArgs<ExtArgs>>): Prisma.Prisma__SessionClient<runtime.Types.Result.GetResult<Prisma.$SessionPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Session that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {SessionFindUniqueOrThrowArgs} args - Arguments to find a Session
   * @example
   * // Get one Session
   * const session = await prisma.session.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends SessionFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, SessionFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__SessionClient<runtime.Types.Result.GetResult<Prisma.$SessionPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Session that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SessionFindFirstArgs} args - Arguments to find a Session
   * @example
   * // Get one Session
   * const session = await prisma.session.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends SessionFindFirstArgs>(args?: Prisma.SelectSubset<T, SessionFindFirstArgs<ExtArgs>>): Prisma.Prisma__SessionClient<runtime.Types.Result.GetResult<Prisma.$SessionPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Session that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SessionFindFirstOrThrowArgs} args - Arguments to find a Session
   * @example
   * // Get one Session
   * const session = await prisma.session.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends SessionFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, SessionFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__SessionClient<runtime.Types.Result.GetResult<Prisma.$SessionPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Sessions that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SessionFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Sessions
   * const sessions = await prisma.session.findMany()
   * 
   * // Get first 10 Sessions
   * const sessions = await prisma.session.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const sessionWithIdOnly = await prisma.session.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends SessionFindManyArgs>(args?: Prisma.SelectSubset<T, SessionFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$SessionPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Session.
   * @param {SessionCreateArgs} args - Arguments to create a Session.
   * @example
   * // Create one Session
   * const Session = await prisma.session.create({
   *   data: {
   *     // ... data to create a Session
   *   }
   * })
   * 
   */
  create<T extends SessionCreateArgs>(args: Prisma.SelectSubset<T, SessionCreateArgs<ExtArgs>>): Prisma.Prisma__SessionClient<runtime.Types.Result.GetResult<Prisma.$SessionPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Sessions.
   * @param {SessionCreateManyArgs} args - Arguments to create many Sessions.
   * @example
   * // Create many Sessions
   * const session = await prisma.session.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends SessionCreateManyArgs>(args?: Prisma.SelectSubset<T, SessionCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Sessions and returns the data saved in the database.
   * @param {SessionCreateManyAndReturnArgs} args - Arguments to create many Sessions.
   * @example
   * // Create many Sessions
   * const session = await prisma.session.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Sessions and only return the `id`
   * const sessionWithIdOnly = await prisma.session.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends SessionCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, SessionCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$SessionPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a Session.
   * @param {SessionDeleteArgs} args - Arguments to delete one Session.
   * @example
   * // Delete one Session
   * const Session = await prisma.session.delete({
   *   where: {
   *     // ... filter to delete one Session
   *   }
   * })
   * 
   */
  delete<T extends SessionDeleteArgs>(args: Prisma.SelectSubset<T, SessionDeleteArgs<ExtArgs>>): Prisma.Prisma__SessionClient<runtime.Types.Result.GetResult<Prisma.$SessionPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Session.
   * @param {SessionUpdateArgs} args - Arguments to update one Session.
   * @example
   * // Update one Session
   * const session = await prisma.session.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends SessionUpdateArgs>(args: Prisma.SelectSubset<T, SessionUpdateArgs<ExtArgs>>): Prisma.Prisma__SessionClient<runtime.Types.Result.GetResult<Prisma.$SessionPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Sessions.
   * @param {SessionDeleteManyArgs} args - Arguments to filter Sessions to delete.
   * @example
   * // Delete a few Sessions
   * const { count } = await prisma.session.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends SessionDeleteManyArgs>(args?: Prisma.SelectSubset<T, SessionDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Sessions.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SessionUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Sessions
   * const session = await prisma.session.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends SessionUpdateManyArgs>(args: Prisma.SelectSubset<T, SessionUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Sessions and returns the data updated in the database.
   * @param {SessionUpdateManyAndReturnArgs} args - Arguments to update many Sessions.
   * @example
   * // Update many Sessions
   * const session = await prisma.session.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Sessions and only return the `id`
   * const sessionWithIdOnly = await prisma.session.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends SessionUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, SessionUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$SessionPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one Session.
   * @param {SessionUpsertArgs} args - Arguments to update or create a Session.
   * @example
   * // Update or create a Session
   * const session = await prisma.session.upsert({
   *   create: {
   *     // ... data to create a Session
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Session we want to update
   *   }
   * })
   */
  upsert<T extends SessionUpsertArgs>(args: Prisma.SelectSubset<T, SessionUpsertArgs<ExtArgs>>): Prisma.Prisma__SessionClient<runtime.Types.Result.GetResult<Prisma.$SessionPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Sessions.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SessionCountArgs} args - Arguments to filter Sessions to count.
   * @example
   * // Count the number of Sessions
   * const count = await prisma.session.count({
   *   where: {
   *     // ... the filter for the Sessions we want to count
   *   }
   * })
  **/
  count<T extends SessionCountArgs>(
    args?: Prisma.Subset<T, SessionCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], SessionCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Session.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SessionAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends SessionAggregateArgs>(args: Prisma.Subset<T, SessionAggregateArgs>): Prisma.PrismaPromise<GetSessionAggregateType<T>>

  /**
   * Group by Session.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {SessionGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends SessionGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: SessionGroupByArgs['orderBy'] }
      : { orderBy?: SessionGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, SessionGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetSessionGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Session model
 */
readonly fields: SessionFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Session.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__SessionClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user<T extends Prisma.UserDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.UserDefaultArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Session model
 */
export interface SessionFieldRefs {
  readonly id: Prisma.FieldRef<"Session", 'String'>
  readonly expiresAt: Prisma.FieldRef<"Session", 'DateTime'>
  readonly token: Prisma.FieldRef<"Session", 'String'>
  readonly createdAt: Prisma.FieldRef<"Session", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"Session", 'DateTime'>
  readonly ipAddress: Prisma.FieldRef<"Session", 'String'>
  readonly userAgent: Prisma.FieldRef<"Session", 'String'>
  readonly impersonatedBy: Prisma.FieldRef<"Session", 'String'>
  readonly userId: Prisma.FieldRef<"Session", 'String'>
}
    

// Custom InputTypes
/**
 * Session findUnique
 */
export type SessionFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Session
   */
  select?: Prisma.SessionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Session
   */
  omit?: Prisma.SessionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SessionInclude<ExtArgs> | null
  /**
   * Filter, which Session to fetch.
   */
  where: Prisma.SessionWhereUniqueInput
}

/**
 * Session findUniqueOrThrow
 */
export type SessionFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Session
   */
  select?: Prisma.SessionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Session
   */
  omit?: Prisma.SessionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SessionInclude<ExtArgs> | null
  /**
   * Filter, which Session to fetch.
   */
  where: Prisma.SessionWhereUniqueInput
}

/**
 * Session findFirst
 */
export type SessionFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Session
   */
  select?: Prisma.SessionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Session
   */
  omit?: Prisma.SessionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SessionInclude<ExtArgs> | null
  /**
   * Filter, which Session to fetch.
   */
  where?: Prisma.SessionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Sessions to fetch.
   */
  orderBy?: Prisma.SessionOrderByWithRelationInput | Prisma.SessionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Sessions.
   */
  cursor?: Prisma.SessionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Sessions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Sessions.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Sessions.
   */
  distinct?: Prisma.SessionScalarFieldEnum | Prisma.SessionScalarFieldEnum[]
}

/**
 * Session findFirstOrThrow
 */
export type SessionFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Session
   */
  select?: Prisma.SessionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Session
   */
  omit?: Prisma.SessionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SessionInclude<ExtArgs> | null
  /**
   * Filter, which Session to fetch.
   */
  where?: Prisma.SessionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Sessions to fetch.
   */
  orderBy?: Prisma.SessionOrderByWithRelationInput | Prisma.SessionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Sessions.
   */
  cursor?: Prisma.SessionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Sessions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Sessions.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Sessions.
   */
  distinct?: Prisma.SessionScalarFieldEnum | Prisma.SessionScalarFieldEnum[]
}

/**
 * Session findMany
 */
export type SessionFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Session
   */
  select?: Prisma.SessionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Session
   */
  omit?: Prisma.SessionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SessionInclude<ExtArgs> | null
  /**
   * Filter, which Sessions to fetch.
   */
  where?: Prisma.SessionWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Sessions to fetch.
   */
  orderBy?: Prisma.SessionOrderByWithRelationInput | Prisma.SessionOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Sessions.
   */
  cursor?: Prisma.SessionWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Sessions from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Sessions.
   */
  skip?: number
  distinct?: Prisma.SessionScalarFieldEnum | Prisma.SessionScalarFieldEnum[]
}

/**
 * Session create
 */
export type SessionCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Session
   */
  select?: Prisma.SessionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Session
   */
  omit?: Prisma.SessionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SessionInclude<ExtArgs> | null
  /**
   * The data needed to create a Session.
   */
  data: Prisma.XOR<Prisma.SessionCreateInput, Prisma.SessionUncheckedCreateInput>
}

/**
 * Session createMany
 */
export type SessionCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Sessions.
   */
  data: Prisma.SessionCreateManyInput | Prisma.SessionCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Session createManyAndReturn
 */
export type SessionCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Session
   */
  select?: Prisma.SessionSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Session
   */
  omit?: Prisma.SessionOmit<ExtArgs> | null
  /**
   * The data used to create many Sessions.
   */
  data: Prisma.SessionCreateManyInput | Prisma.SessionCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SessionIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * Session update
 */
export type SessionUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Session
   */
  select?: Prisma.SessionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Session
   */
  omit?: Prisma.SessionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SessionInclude<ExtArgs> | null
  /**
   * The data needed to update a Session.
   */
  data: Prisma.XOR<Prisma.SessionUpdateInput, Prisma.SessionUncheckedUpdateInput>
  /**
   * Choose, which Session to update.
   */
  where: Prisma.SessionWhereUniqueInput
}

/**
 * Session updateMany
 */
export type SessionUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Sessions.
   */
  data: Prisma.XOR<Prisma.SessionUpdateManyMutationInput, Prisma.SessionUncheckedUpdateManyInput>
  /**
   * Filter which Sessions to update
   */
  where?: Prisma.SessionWhereInput
  /**
   * Limit how many Sessions to update.
   */
  limit?: number
}

/**
 * Session updateManyAndReturn
 */
export type SessionUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Session
   */
  select?: Prisma.SessionSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Session
   */
  omit?: Prisma.SessionOmit<ExtArgs> | null
  /**
   * The data used to update Sessions.
   */
  data: Prisma.XOR<Prisma.SessionUpdateManyMutationInput, Prisma.SessionUncheckedUpdateManyInput>
  /**
   * Filter which Sessions to update
   */
  where?: Prisma.SessionWhereInput
  /**
   * Limit how many Sessions to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SessionIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * Session upsert
 */
export type SessionUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Session
   */
  select?: Prisma.SessionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Session
   */
  omit?: Prisma.SessionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SessionInclude<ExtArgs> | null
  /**
   * The filter to search for the Session to update in case it exists.
   */
  where: Prisma.SessionWhereUniqueInput
  /**
   * In case the Session found by the `where` argument doesn't exist, create a new Session with this data.
   */
  create: Prisma.XOR<Prisma.SessionCreateInput, Prisma.SessionUncheckedCreateInput>
  /**
   * In case the Session was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.SessionUpdateInput, Prisma.SessionUncheckedUpdateInput>
}

/**
 * Session delete
 */
export type SessionDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Session
   */
  select?: Prisma.SessionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Session
   */
  omit?: Prisma.SessionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SessionInclude<ExtArgs> | null
  /**
   * Filter which Session to delete.
   */
  where: Prisma.SessionWhereUniqueInput
}

/**
 * Session deleteMany
 */
export type SessionDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Sessions to delete
   */
  where?: Prisma.SessionWhereInput
  /**
   * Limit how many Sessions to delete.
   */
  limit?: number
}

/**
 * Session without action
 */
export type SessionDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Session
   */
  select?: Prisma.SessionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Session
   */
  omit?: Prisma.SessionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SessionInclude<ExtArgs> | null
}
