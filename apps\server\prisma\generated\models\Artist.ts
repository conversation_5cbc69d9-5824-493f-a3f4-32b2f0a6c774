
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Artist` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Artist
 * 
 */
export type ArtistModel = runtime.Types.Result.DefaultSelection<Prisma.$ArtistPayload>

export type AggregateArtist = {
  _count: ArtistCountAggregateOutputType | null
  _min: ArtistMinAggregateOutputType | null
  _max: ArtistMaxAggregateOutputType | null
}

export type ArtistMinAggregateOutputType = {
  id: string | null
  name: string | null
  instagram: string | null
  biography: string | null
  country: string | null
  labelId: string | null
  genre: string | null
  userId: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type ArtistMaxAggregateOutputType = {
  id: string | null
  name: string | null
  instagram: string | null
  biography: string | null
  country: string | null
  labelId: string | null
  genre: string | null
  userId: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type ArtistCountAggregateOutputType = {
  id: number
  name: number
  instagram: number
  biography: number
  country: number
  labelId: number
  genre: number
  userId: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type ArtistMinAggregateInputType = {
  id?: true
  name?: true
  instagram?: true
  biography?: true
  country?: true
  labelId?: true
  genre?: true
  userId?: true
  createdAt?: true
  updatedAt?: true
}

export type ArtistMaxAggregateInputType = {
  id?: true
  name?: true
  instagram?: true
  biography?: true
  country?: true
  labelId?: true
  genre?: true
  userId?: true
  createdAt?: true
  updatedAt?: true
}

export type ArtistCountAggregateInputType = {
  id?: true
  name?: true
  instagram?: true
  biography?: true
  country?: true
  labelId?: true
  genre?: true
  userId?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type ArtistAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Artist to aggregate.
   */
  where?: Prisma.ArtistWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Artists to fetch.
   */
  orderBy?: Prisma.ArtistOrderByWithRelationInput | Prisma.ArtistOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.ArtistWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Artists from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Artists.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Artists
  **/
  _count?: true | ArtistCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: ArtistMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: ArtistMaxAggregateInputType
}

export type GetArtistAggregateType<T extends ArtistAggregateArgs> = {
      [P in keyof T & keyof AggregateArtist]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateArtist[P]>
    : Prisma.GetScalarType<T[P], AggregateArtist[P]>
}




export type ArtistGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ArtistWhereInput
  orderBy?: Prisma.ArtistOrderByWithAggregationInput | Prisma.ArtistOrderByWithAggregationInput[]
  by: Prisma.ArtistScalarFieldEnum[] | Prisma.ArtistScalarFieldEnum
  having?: Prisma.ArtistScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: ArtistCountAggregateInputType | true
  _min?: ArtistMinAggregateInputType
  _max?: ArtistMaxAggregateInputType
}

export type ArtistGroupByOutputType = {
  id: string
  name: string
  instagram: string | null
  biography: string | null
  country: string | null
  labelId: string | null
  genre: string | null
  userId: string
  createdAt: Date
  updatedAt: Date
  _count: ArtistCountAggregateOutputType | null
  _min: ArtistMinAggregateOutputType | null
  _max: ArtistMaxAggregateOutputType | null
}

type GetArtistGroupByPayload<T extends ArtistGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<ArtistGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof ArtistGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], ArtistGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], ArtistGroupByOutputType[P]>
      }
    >
  > 



export type ArtistWhereInput = {
  AND?: Prisma.ArtistWhereInput | Prisma.ArtistWhereInput[]
  OR?: Prisma.ArtistWhereInput[]
  NOT?: Prisma.ArtistWhereInput | Prisma.ArtistWhereInput[]
  id?: Prisma.StringFilter<"Artist"> | string
  name?: Prisma.StringFilter<"Artist"> | string
  instagram?: Prisma.StringNullableFilter<"Artist"> | string | null
  biography?: Prisma.StringNullableFilter<"Artist"> | string | null
  country?: Prisma.StringNullableFilter<"Artist"> | string | null
  labelId?: Prisma.StringNullableFilter<"Artist"> | string | null
  genre?: Prisma.StringNullableFilter<"Artist"> | string | null
  userId?: Prisma.StringFilter<"Artist"> | string
  createdAt?: Prisma.DateTimeFilter<"Artist"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Artist"> | Date | string
  label?: Prisma.XOR<Prisma.LabelNullableScalarRelationFilter, Prisma.LabelWhereInput> | null
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
  identifiers?: Prisma.ArtistIdentifierListRelationFilter
  releases?: Prisma.ReleaseArtistListRelationFilter
  tracks?: Prisma.TrackArtistListRelationFilter
}

export type ArtistOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  instagram?: Prisma.SortOrderInput | Prisma.SortOrder
  biography?: Prisma.SortOrderInput | Prisma.SortOrder
  country?: Prisma.SortOrderInput | Prisma.SortOrder
  labelId?: Prisma.SortOrderInput | Prisma.SortOrder
  genre?: Prisma.SortOrderInput | Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  label?: Prisma.LabelOrderByWithRelationInput
  user?: Prisma.UserOrderByWithRelationInput
  identifiers?: Prisma.ArtistIdentifierOrderByRelationAggregateInput
  releases?: Prisma.ReleaseArtistOrderByRelationAggregateInput
  tracks?: Prisma.TrackArtistOrderByRelationAggregateInput
}

export type ArtistWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  userId_name?: Prisma.ArtistUserIdNameCompoundUniqueInput
  AND?: Prisma.ArtistWhereInput | Prisma.ArtistWhereInput[]
  OR?: Prisma.ArtistWhereInput[]
  NOT?: Prisma.ArtistWhereInput | Prisma.ArtistWhereInput[]
  name?: Prisma.StringFilter<"Artist"> | string
  instagram?: Prisma.StringNullableFilter<"Artist"> | string | null
  biography?: Prisma.StringNullableFilter<"Artist"> | string | null
  country?: Prisma.StringNullableFilter<"Artist"> | string | null
  labelId?: Prisma.StringNullableFilter<"Artist"> | string | null
  genre?: Prisma.StringNullableFilter<"Artist"> | string | null
  userId?: Prisma.StringFilter<"Artist"> | string
  createdAt?: Prisma.DateTimeFilter<"Artist"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Artist"> | Date | string
  label?: Prisma.XOR<Prisma.LabelNullableScalarRelationFilter, Prisma.LabelWhereInput> | null
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
  identifiers?: Prisma.ArtistIdentifierListRelationFilter
  releases?: Prisma.ReleaseArtistListRelationFilter
  tracks?: Prisma.TrackArtistListRelationFilter
}, "id" | "userId_name">

export type ArtistOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  instagram?: Prisma.SortOrderInput | Prisma.SortOrder
  biography?: Prisma.SortOrderInput | Prisma.SortOrder
  country?: Prisma.SortOrderInput | Prisma.SortOrder
  labelId?: Prisma.SortOrderInput | Prisma.SortOrder
  genre?: Prisma.SortOrderInput | Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.ArtistCountOrderByAggregateInput
  _max?: Prisma.ArtistMaxOrderByAggregateInput
  _min?: Prisma.ArtistMinOrderByAggregateInput
}

export type ArtistScalarWhereWithAggregatesInput = {
  AND?: Prisma.ArtistScalarWhereWithAggregatesInput | Prisma.ArtistScalarWhereWithAggregatesInput[]
  OR?: Prisma.ArtistScalarWhereWithAggregatesInput[]
  NOT?: Prisma.ArtistScalarWhereWithAggregatesInput | Prisma.ArtistScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Artist"> | string
  name?: Prisma.StringWithAggregatesFilter<"Artist"> | string
  instagram?: Prisma.StringNullableWithAggregatesFilter<"Artist"> | string | null
  biography?: Prisma.StringNullableWithAggregatesFilter<"Artist"> | string | null
  country?: Prisma.StringNullableWithAggregatesFilter<"Artist"> | string | null
  labelId?: Prisma.StringNullableWithAggregatesFilter<"Artist"> | string | null
  genre?: Prisma.StringNullableWithAggregatesFilter<"Artist"> | string | null
  userId?: Prisma.StringWithAggregatesFilter<"Artist"> | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Artist"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"Artist"> | Date | string
}

export type ArtistCreateInput = {
  id?: string
  name: string
  instagram?: string | null
  biography?: string | null
  country?: string | null
  genre?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  label?: Prisma.LabelCreateNestedOneWithoutArtistsInput
  user: Prisma.UserCreateNestedOneWithoutArtistsInput
  identifiers?: Prisma.ArtistIdentifierCreateNestedManyWithoutArtistInput
  releases?: Prisma.ReleaseArtistCreateNestedManyWithoutArtistInput
  tracks?: Prisma.TrackArtistCreateNestedManyWithoutArtistInput
}

export type ArtistUncheckedCreateInput = {
  id?: string
  name: string
  instagram?: string | null
  biography?: string | null
  country?: string | null
  labelId?: string | null
  genre?: string | null
  userId: string
  createdAt?: Date | string
  updatedAt?: Date | string
  identifiers?: Prisma.ArtistIdentifierUncheckedCreateNestedManyWithoutArtistInput
  releases?: Prisma.ReleaseArtistUncheckedCreateNestedManyWithoutArtistInput
  tracks?: Prisma.TrackArtistUncheckedCreateNestedManyWithoutArtistInput
}

export type ArtistUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  instagram?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  biography?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  country?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  genre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  label?: Prisma.LabelUpdateOneWithoutArtistsNestedInput
  user?: Prisma.UserUpdateOneRequiredWithoutArtistsNestedInput
  identifiers?: Prisma.ArtistIdentifierUpdateManyWithoutArtistNestedInput
  releases?: Prisma.ReleaseArtistUpdateManyWithoutArtistNestedInput
  tracks?: Prisma.TrackArtistUpdateManyWithoutArtistNestedInput
}

export type ArtistUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  instagram?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  biography?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  country?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  labelId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  genre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  identifiers?: Prisma.ArtistIdentifierUncheckedUpdateManyWithoutArtistNestedInput
  releases?: Prisma.ReleaseArtistUncheckedUpdateManyWithoutArtistNestedInput
  tracks?: Prisma.TrackArtistUncheckedUpdateManyWithoutArtistNestedInput
}

export type ArtistCreateManyInput = {
  id?: string
  name: string
  instagram?: string | null
  biography?: string | null
  country?: string | null
  labelId?: string | null
  genre?: string | null
  userId: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type ArtistUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  instagram?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  biography?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  country?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  genre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ArtistUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  instagram?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  biography?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  country?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  labelId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  genre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ArtistUserIdNameCompoundUniqueInput = {
  userId: string
  name: string
}

export type ArtistCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  instagram?: Prisma.SortOrder
  biography?: Prisma.SortOrder
  country?: Prisma.SortOrder
  labelId?: Prisma.SortOrder
  genre?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type ArtistMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  instagram?: Prisma.SortOrder
  biography?: Prisma.SortOrder
  country?: Prisma.SortOrder
  labelId?: Prisma.SortOrder
  genre?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type ArtistMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  instagram?: Prisma.SortOrder
  biography?: Prisma.SortOrder
  country?: Prisma.SortOrder
  labelId?: Prisma.SortOrder
  genre?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type ArtistScalarRelationFilter = {
  is?: Prisma.ArtistWhereInput
  isNot?: Prisma.ArtistWhereInput
}

export type ArtistListRelationFilter = {
  every?: Prisma.ArtistWhereInput
  some?: Prisma.ArtistWhereInput
  none?: Prisma.ArtistWhereInput
}

export type ArtistOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type StringFieldUpdateOperationsInput = {
  set?: string
}

export type NullableStringFieldUpdateOperationsInput = {
  set?: string | null
}

export type DateTimeFieldUpdateOperationsInput = {
  set?: Date | string
}

export type ArtistCreateNestedOneWithoutIdentifiersInput = {
  create?: Prisma.XOR<Prisma.ArtistCreateWithoutIdentifiersInput, Prisma.ArtistUncheckedCreateWithoutIdentifiersInput>
  connectOrCreate?: Prisma.ArtistCreateOrConnectWithoutIdentifiersInput
  connect?: Prisma.ArtistWhereUniqueInput
}

export type ArtistUpdateOneRequiredWithoutIdentifiersNestedInput = {
  create?: Prisma.XOR<Prisma.ArtistCreateWithoutIdentifiersInput, Prisma.ArtistUncheckedCreateWithoutIdentifiersInput>
  connectOrCreate?: Prisma.ArtistCreateOrConnectWithoutIdentifiersInput
  upsert?: Prisma.ArtistUpsertWithoutIdentifiersInput
  connect?: Prisma.ArtistWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.ArtistUpdateToOneWithWhereWithoutIdentifiersInput, Prisma.ArtistUpdateWithoutIdentifiersInput>, Prisma.ArtistUncheckedUpdateWithoutIdentifiersInput>
}

export type ArtistCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.ArtistCreateWithoutUserInput, Prisma.ArtistUncheckedCreateWithoutUserInput> | Prisma.ArtistCreateWithoutUserInput[] | Prisma.ArtistUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.ArtistCreateOrConnectWithoutUserInput | Prisma.ArtistCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.ArtistCreateManyUserInputEnvelope
  connect?: Prisma.ArtistWhereUniqueInput | Prisma.ArtistWhereUniqueInput[]
}

export type ArtistUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.ArtistCreateWithoutUserInput, Prisma.ArtistUncheckedCreateWithoutUserInput> | Prisma.ArtistCreateWithoutUserInput[] | Prisma.ArtistUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.ArtistCreateOrConnectWithoutUserInput | Prisma.ArtistCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.ArtistCreateManyUserInputEnvelope
  connect?: Prisma.ArtistWhereUniqueInput | Prisma.ArtistWhereUniqueInput[]
}

export type ArtistUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.ArtistCreateWithoutUserInput, Prisma.ArtistUncheckedCreateWithoutUserInput> | Prisma.ArtistCreateWithoutUserInput[] | Prisma.ArtistUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.ArtistCreateOrConnectWithoutUserInput | Prisma.ArtistCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.ArtistUpsertWithWhereUniqueWithoutUserInput | Prisma.ArtistUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.ArtistCreateManyUserInputEnvelope
  set?: Prisma.ArtistWhereUniqueInput | Prisma.ArtistWhereUniqueInput[]
  disconnect?: Prisma.ArtistWhereUniqueInput | Prisma.ArtistWhereUniqueInput[]
  delete?: Prisma.ArtistWhereUniqueInput | Prisma.ArtistWhereUniqueInput[]
  connect?: Prisma.ArtistWhereUniqueInput | Prisma.ArtistWhereUniqueInput[]
  update?: Prisma.ArtistUpdateWithWhereUniqueWithoutUserInput | Prisma.ArtistUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.ArtistUpdateManyWithWhereWithoutUserInput | Prisma.ArtistUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.ArtistScalarWhereInput | Prisma.ArtistScalarWhereInput[]
}

export type ArtistUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.ArtistCreateWithoutUserInput, Prisma.ArtistUncheckedCreateWithoutUserInput> | Prisma.ArtistCreateWithoutUserInput[] | Prisma.ArtistUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.ArtistCreateOrConnectWithoutUserInput | Prisma.ArtistCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.ArtistUpsertWithWhereUniqueWithoutUserInput | Prisma.ArtistUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.ArtistCreateManyUserInputEnvelope
  set?: Prisma.ArtistWhereUniqueInput | Prisma.ArtistWhereUniqueInput[]
  disconnect?: Prisma.ArtistWhereUniqueInput | Prisma.ArtistWhereUniqueInput[]
  delete?: Prisma.ArtistWhereUniqueInput | Prisma.ArtistWhereUniqueInput[]
  connect?: Prisma.ArtistWhereUniqueInput | Prisma.ArtistWhereUniqueInput[]
  update?: Prisma.ArtistUpdateWithWhereUniqueWithoutUserInput | Prisma.ArtistUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.ArtistUpdateManyWithWhereWithoutUserInput | Prisma.ArtistUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.ArtistScalarWhereInput | Prisma.ArtistScalarWhereInput[]
}

export type ArtistCreateNestedManyWithoutLabelInput = {
  create?: Prisma.XOR<Prisma.ArtistCreateWithoutLabelInput, Prisma.ArtistUncheckedCreateWithoutLabelInput> | Prisma.ArtistCreateWithoutLabelInput[] | Prisma.ArtistUncheckedCreateWithoutLabelInput[]
  connectOrCreate?: Prisma.ArtistCreateOrConnectWithoutLabelInput | Prisma.ArtistCreateOrConnectWithoutLabelInput[]
  createMany?: Prisma.ArtistCreateManyLabelInputEnvelope
  connect?: Prisma.ArtistWhereUniqueInput | Prisma.ArtistWhereUniqueInput[]
}

export type ArtistUncheckedCreateNestedManyWithoutLabelInput = {
  create?: Prisma.XOR<Prisma.ArtistCreateWithoutLabelInput, Prisma.ArtistUncheckedCreateWithoutLabelInput> | Prisma.ArtistCreateWithoutLabelInput[] | Prisma.ArtistUncheckedCreateWithoutLabelInput[]
  connectOrCreate?: Prisma.ArtistCreateOrConnectWithoutLabelInput | Prisma.ArtistCreateOrConnectWithoutLabelInput[]
  createMany?: Prisma.ArtistCreateManyLabelInputEnvelope
  connect?: Prisma.ArtistWhereUniqueInput | Prisma.ArtistWhereUniqueInput[]
}

export type ArtistUpdateManyWithoutLabelNestedInput = {
  create?: Prisma.XOR<Prisma.ArtistCreateWithoutLabelInput, Prisma.ArtistUncheckedCreateWithoutLabelInput> | Prisma.ArtistCreateWithoutLabelInput[] | Prisma.ArtistUncheckedCreateWithoutLabelInput[]
  connectOrCreate?: Prisma.ArtistCreateOrConnectWithoutLabelInput | Prisma.ArtistCreateOrConnectWithoutLabelInput[]
  upsert?: Prisma.ArtistUpsertWithWhereUniqueWithoutLabelInput | Prisma.ArtistUpsertWithWhereUniqueWithoutLabelInput[]
  createMany?: Prisma.ArtistCreateManyLabelInputEnvelope
  set?: Prisma.ArtistWhereUniqueInput | Prisma.ArtistWhereUniqueInput[]
  disconnect?: Prisma.ArtistWhereUniqueInput | Prisma.ArtistWhereUniqueInput[]
  delete?: Prisma.ArtistWhereUniqueInput | Prisma.ArtistWhereUniqueInput[]
  connect?: Prisma.ArtistWhereUniqueInput | Prisma.ArtistWhereUniqueInput[]
  update?: Prisma.ArtistUpdateWithWhereUniqueWithoutLabelInput | Prisma.ArtistUpdateWithWhereUniqueWithoutLabelInput[]
  updateMany?: Prisma.ArtistUpdateManyWithWhereWithoutLabelInput | Prisma.ArtistUpdateManyWithWhereWithoutLabelInput[]
  deleteMany?: Prisma.ArtistScalarWhereInput | Prisma.ArtistScalarWhereInput[]
}

export type ArtistUncheckedUpdateManyWithoutLabelNestedInput = {
  create?: Prisma.XOR<Prisma.ArtistCreateWithoutLabelInput, Prisma.ArtistUncheckedCreateWithoutLabelInput> | Prisma.ArtistCreateWithoutLabelInput[] | Prisma.ArtistUncheckedCreateWithoutLabelInput[]
  connectOrCreate?: Prisma.ArtistCreateOrConnectWithoutLabelInput | Prisma.ArtistCreateOrConnectWithoutLabelInput[]
  upsert?: Prisma.ArtistUpsertWithWhereUniqueWithoutLabelInput | Prisma.ArtistUpsertWithWhereUniqueWithoutLabelInput[]
  createMany?: Prisma.ArtistCreateManyLabelInputEnvelope
  set?: Prisma.ArtistWhereUniqueInput | Prisma.ArtistWhereUniqueInput[]
  disconnect?: Prisma.ArtistWhereUniqueInput | Prisma.ArtistWhereUniqueInput[]
  delete?: Prisma.ArtistWhereUniqueInput | Prisma.ArtistWhereUniqueInput[]
  connect?: Prisma.ArtistWhereUniqueInput | Prisma.ArtistWhereUniqueInput[]
  update?: Prisma.ArtistUpdateWithWhereUniqueWithoutLabelInput | Prisma.ArtistUpdateWithWhereUniqueWithoutLabelInput[]
  updateMany?: Prisma.ArtistUpdateManyWithWhereWithoutLabelInput | Prisma.ArtistUpdateManyWithWhereWithoutLabelInput[]
  deleteMany?: Prisma.ArtistScalarWhereInput | Prisma.ArtistScalarWhereInput[]
}

export type ArtistCreateNestedOneWithoutReleasesInput = {
  create?: Prisma.XOR<Prisma.ArtistCreateWithoutReleasesInput, Prisma.ArtistUncheckedCreateWithoutReleasesInput>
  connectOrCreate?: Prisma.ArtistCreateOrConnectWithoutReleasesInput
  connect?: Prisma.ArtistWhereUniqueInput
}

export type ArtistUpdateOneRequiredWithoutReleasesNestedInput = {
  create?: Prisma.XOR<Prisma.ArtistCreateWithoutReleasesInput, Prisma.ArtistUncheckedCreateWithoutReleasesInput>
  connectOrCreate?: Prisma.ArtistCreateOrConnectWithoutReleasesInput
  upsert?: Prisma.ArtistUpsertWithoutReleasesInput
  connect?: Prisma.ArtistWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.ArtistUpdateToOneWithWhereWithoutReleasesInput, Prisma.ArtistUpdateWithoutReleasesInput>, Prisma.ArtistUncheckedUpdateWithoutReleasesInput>
}

export type ArtistCreateNestedOneWithoutTracksInput = {
  create?: Prisma.XOR<Prisma.ArtistCreateWithoutTracksInput, Prisma.ArtistUncheckedCreateWithoutTracksInput>
  connectOrCreate?: Prisma.ArtistCreateOrConnectWithoutTracksInput
  connect?: Prisma.ArtistWhereUniqueInput
}

export type ArtistUpdateOneRequiredWithoutTracksNestedInput = {
  create?: Prisma.XOR<Prisma.ArtistCreateWithoutTracksInput, Prisma.ArtistUncheckedCreateWithoutTracksInput>
  connectOrCreate?: Prisma.ArtistCreateOrConnectWithoutTracksInput
  upsert?: Prisma.ArtistUpsertWithoutTracksInput
  connect?: Prisma.ArtistWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.ArtistUpdateToOneWithWhereWithoutTracksInput, Prisma.ArtistUpdateWithoutTracksInput>, Prisma.ArtistUncheckedUpdateWithoutTracksInput>
}

export type ArtistCreateWithoutIdentifiersInput = {
  id?: string
  name: string
  instagram?: string | null
  biography?: string | null
  country?: string | null
  genre?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  label?: Prisma.LabelCreateNestedOneWithoutArtistsInput
  user: Prisma.UserCreateNestedOneWithoutArtistsInput
  releases?: Prisma.ReleaseArtistCreateNestedManyWithoutArtistInput
  tracks?: Prisma.TrackArtistCreateNestedManyWithoutArtistInput
}

export type ArtistUncheckedCreateWithoutIdentifiersInput = {
  id?: string
  name: string
  instagram?: string | null
  biography?: string | null
  country?: string | null
  labelId?: string | null
  genre?: string | null
  userId: string
  createdAt?: Date | string
  updatedAt?: Date | string
  releases?: Prisma.ReleaseArtistUncheckedCreateNestedManyWithoutArtistInput
  tracks?: Prisma.TrackArtistUncheckedCreateNestedManyWithoutArtistInput
}

export type ArtistCreateOrConnectWithoutIdentifiersInput = {
  where: Prisma.ArtistWhereUniqueInput
  create: Prisma.XOR<Prisma.ArtistCreateWithoutIdentifiersInput, Prisma.ArtistUncheckedCreateWithoutIdentifiersInput>
}

export type ArtistUpsertWithoutIdentifiersInput = {
  update: Prisma.XOR<Prisma.ArtistUpdateWithoutIdentifiersInput, Prisma.ArtistUncheckedUpdateWithoutIdentifiersInput>
  create: Prisma.XOR<Prisma.ArtistCreateWithoutIdentifiersInput, Prisma.ArtistUncheckedCreateWithoutIdentifiersInput>
  where?: Prisma.ArtistWhereInput
}

export type ArtistUpdateToOneWithWhereWithoutIdentifiersInput = {
  where?: Prisma.ArtistWhereInput
  data: Prisma.XOR<Prisma.ArtistUpdateWithoutIdentifiersInput, Prisma.ArtistUncheckedUpdateWithoutIdentifiersInput>
}

export type ArtistUpdateWithoutIdentifiersInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  instagram?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  biography?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  country?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  genre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  label?: Prisma.LabelUpdateOneWithoutArtistsNestedInput
  user?: Prisma.UserUpdateOneRequiredWithoutArtistsNestedInput
  releases?: Prisma.ReleaseArtistUpdateManyWithoutArtistNestedInput
  tracks?: Prisma.TrackArtistUpdateManyWithoutArtistNestedInput
}

export type ArtistUncheckedUpdateWithoutIdentifiersInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  instagram?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  biography?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  country?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  labelId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  genre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  releases?: Prisma.ReleaseArtistUncheckedUpdateManyWithoutArtistNestedInput
  tracks?: Prisma.TrackArtistUncheckedUpdateManyWithoutArtistNestedInput
}

export type ArtistCreateWithoutUserInput = {
  id?: string
  name: string
  instagram?: string | null
  biography?: string | null
  country?: string | null
  genre?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  label?: Prisma.LabelCreateNestedOneWithoutArtistsInput
  identifiers?: Prisma.ArtistIdentifierCreateNestedManyWithoutArtistInput
  releases?: Prisma.ReleaseArtistCreateNestedManyWithoutArtistInput
  tracks?: Prisma.TrackArtistCreateNestedManyWithoutArtistInput
}

export type ArtistUncheckedCreateWithoutUserInput = {
  id?: string
  name: string
  instagram?: string | null
  biography?: string | null
  country?: string | null
  labelId?: string | null
  genre?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  identifiers?: Prisma.ArtistIdentifierUncheckedCreateNestedManyWithoutArtistInput
  releases?: Prisma.ReleaseArtistUncheckedCreateNestedManyWithoutArtistInput
  tracks?: Prisma.TrackArtistUncheckedCreateNestedManyWithoutArtistInput
}

export type ArtistCreateOrConnectWithoutUserInput = {
  where: Prisma.ArtistWhereUniqueInput
  create: Prisma.XOR<Prisma.ArtistCreateWithoutUserInput, Prisma.ArtistUncheckedCreateWithoutUserInput>
}

export type ArtistCreateManyUserInputEnvelope = {
  data: Prisma.ArtistCreateManyUserInput | Prisma.ArtistCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type ArtistUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.ArtistWhereUniqueInput
  update: Prisma.XOR<Prisma.ArtistUpdateWithoutUserInput, Prisma.ArtistUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.ArtistCreateWithoutUserInput, Prisma.ArtistUncheckedCreateWithoutUserInput>
}

export type ArtistUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.ArtistWhereUniqueInput
  data: Prisma.XOR<Prisma.ArtistUpdateWithoutUserInput, Prisma.ArtistUncheckedUpdateWithoutUserInput>
}

export type ArtistUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.ArtistScalarWhereInput
  data: Prisma.XOR<Prisma.ArtistUpdateManyMutationInput, Prisma.ArtistUncheckedUpdateManyWithoutUserInput>
}

export type ArtistScalarWhereInput = {
  AND?: Prisma.ArtistScalarWhereInput | Prisma.ArtistScalarWhereInput[]
  OR?: Prisma.ArtistScalarWhereInput[]
  NOT?: Prisma.ArtistScalarWhereInput | Prisma.ArtistScalarWhereInput[]
  id?: Prisma.StringFilter<"Artist"> | string
  name?: Prisma.StringFilter<"Artist"> | string
  instagram?: Prisma.StringNullableFilter<"Artist"> | string | null
  biography?: Prisma.StringNullableFilter<"Artist"> | string | null
  country?: Prisma.StringNullableFilter<"Artist"> | string | null
  labelId?: Prisma.StringNullableFilter<"Artist"> | string | null
  genre?: Prisma.StringNullableFilter<"Artist"> | string | null
  userId?: Prisma.StringFilter<"Artist"> | string
  createdAt?: Prisma.DateTimeFilter<"Artist"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Artist"> | Date | string
}

export type ArtistCreateWithoutLabelInput = {
  id?: string
  name: string
  instagram?: string | null
  biography?: string | null
  country?: string | null
  genre?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutArtistsInput
  identifiers?: Prisma.ArtistIdentifierCreateNestedManyWithoutArtistInput
  releases?: Prisma.ReleaseArtistCreateNestedManyWithoutArtistInput
  tracks?: Prisma.TrackArtistCreateNestedManyWithoutArtistInput
}

export type ArtistUncheckedCreateWithoutLabelInput = {
  id?: string
  name: string
  instagram?: string | null
  biography?: string | null
  country?: string | null
  genre?: string | null
  userId: string
  createdAt?: Date | string
  updatedAt?: Date | string
  identifiers?: Prisma.ArtistIdentifierUncheckedCreateNestedManyWithoutArtistInput
  releases?: Prisma.ReleaseArtistUncheckedCreateNestedManyWithoutArtistInput
  tracks?: Prisma.TrackArtistUncheckedCreateNestedManyWithoutArtistInput
}

export type ArtistCreateOrConnectWithoutLabelInput = {
  where: Prisma.ArtistWhereUniqueInput
  create: Prisma.XOR<Prisma.ArtistCreateWithoutLabelInput, Prisma.ArtistUncheckedCreateWithoutLabelInput>
}

export type ArtistCreateManyLabelInputEnvelope = {
  data: Prisma.ArtistCreateManyLabelInput | Prisma.ArtistCreateManyLabelInput[]
  skipDuplicates?: boolean
}

export type ArtistUpsertWithWhereUniqueWithoutLabelInput = {
  where: Prisma.ArtistWhereUniqueInput
  update: Prisma.XOR<Prisma.ArtistUpdateWithoutLabelInput, Prisma.ArtistUncheckedUpdateWithoutLabelInput>
  create: Prisma.XOR<Prisma.ArtistCreateWithoutLabelInput, Prisma.ArtistUncheckedCreateWithoutLabelInput>
}

export type ArtistUpdateWithWhereUniqueWithoutLabelInput = {
  where: Prisma.ArtistWhereUniqueInput
  data: Prisma.XOR<Prisma.ArtistUpdateWithoutLabelInput, Prisma.ArtistUncheckedUpdateWithoutLabelInput>
}

export type ArtistUpdateManyWithWhereWithoutLabelInput = {
  where: Prisma.ArtistScalarWhereInput
  data: Prisma.XOR<Prisma.ArtistUpdateManyMutationInput, Prisma.ArtistUncheckedUpdateManyWithoutLabelInput>
}

export type ArtistCreateWithoutReleasesInput = {
  id?: string
  name: string
  instagram?: string | null
  biography?: string | null
  country?: string | null
  genre?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  label?: Prisma.LabelCreateNestedOneWithoutArtistsInput
  user: Prisma.UserCreateNestedOneWithoutArtistsInput
  identifiers?: Prisma.ArtistIdentifierCreateNestedManyWithoutArtistInput
  tracks?: Prisma.TrackArtistCreateNestedManyWithoutArtistInput
}

export type ArtistUncheckedCreateWithoutReleasesInput = {
  id?: string
  name: string
  instagram?: string | null
  biography?: string | null
  country?: string | null
  labelId?: string | null
  genre?: string | null
  userId: string
  createdAt?: Date | string
  updatedAt?: Date | string
  identifiers?: Prisma.ArtistIdentifierUncheckedCreateNestedManyWithoutArtistInput
  tracks?: Prisma.TrackArtistUncheckedCreateNestedManyWithoutArtistInput
}

export type ArtistCreateOrConnectWithoutReleasesInput = {
  where: Prisma.ArtistWhereUniqueInput
  create: Prisma.XOR<Prisma.ArtistCreateWithoutReleasesInput, Prisma.ArtistUncheckedCreateWithoutReleasesInput>
}

export type ArtistUpsertWithoutReleasesInput = {
  update: Prisma.XOR<Prisma.ArtistUpdateWithoutReleasesInput, Prisma.ArtistUncheckedUpdateWithoutReleasesInput>
  create: Prisma.XOR<Prisma.ArtistCreateWithoutReleasesInput, Prisma.ArtistUncheckedCreateWithoutReleasesInput>
  where?: Prisma.ArtistWhereInput
}

export type ArtistUpdateToOneWithWhereWithoutReleasesInput = {
  where?: Prisma.ArtistWhereInput
  data: Prisma.XOR<Prisma.ArtistUpdateWithoutReleasesInput, Prisma.ArtistUncheckedUpdateWithoutReleasesInput>
}

export type ArtistUpdateWithoutReleasesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  instagram?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  biography?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  country?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  genre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  label?: Prisma.LabelUpdateOneWithoutArtistsNestedInput
  user?: Prisma.UserUpdateOneRequiredWithoutArtistsNestedInput
  identifiers?: Prisma.ArtistIdentifierUpdateManyWithoutArtistNestedInput
  tracks?: Prisma.TrackArtistUpdateManyWithoutArtistNestedInput
}

export type ArtistUncheckedUpdateWithoutReleasesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  instagram?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  biography?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  country?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  labelId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  genre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  identifiers?: Prisma.ArtistIdentifierUncheckedUpdateManyWithoutArtistNestedInput
  tracks?: Prisma.TrackArtistUncheckedUpdateManyWithoutArtistNestedInput
}

export type ArtistCreateWithoutTracksInput = {
  id?: string
  name: string
  instagram?: string | null
  biography?: string | null
  country?: string | null
  genre?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  label?: Prisma.LabelCreateNestedOneWithoutArtistsInput
  user: Prisma.UserCreateNestedOneWithoutArtistsInput
  identifiers?: Prisma.ArtistIdentifierCreateNestedManyWithoutArtistInput
  releases?: Prisma.ReleaseArtistCreateNestedManyWithoutArtistInput
}

export type ArtistUncheckedCreateWithoutTracksInput = {
  id?: string
  name: string
  instagram?: string | null
  biography?: string | null
  country?: string | null
  labelId?: string | null
  genre?: string | null
  userId: string
  createdAt?: Date | string
  updatedAt?: Date | string
  identifiers?: Prisma.ArtistIdentifierUncheckedCreateNestedManyWithoutArtistInput
  releases?: Prisma.ReleaseArtistUncheckedCreateNestedManyWithoutArtistInput
}

export type ArtistCreateOrConnectWithoutTracksInput = {
  where: Prisma.ArtistWhereUniqueInput
  create: Prisma.XOR<Prisma.ArtistCreateWithoutTracksInput, Prisma.ArtistUncheckedCreateWithoutTracksInput>
}

export type ArtistUpsertWithoutTracksInput = {
  update: Prisma.XOR<Prisma.ArtistUpdateWithoutTracksInput, Prisma.ArtistUncheckedUpdateWithoutTracksInput>
  create: Prisma.XOR<Prisma.ArtistCreateWithoutTracksInput, Prisma.ArtistUncheckedCreateWithoutTracksInput>
  where?: Prisma.ArtistWhereInput
}

export type ArtistUpdateToOneWithWhereWithoutTracksInput = {
  where?: Prisma.ArtistWhereInput
  data: Prisma.XOR<Prisma.ArtistUpdateWithoutTracksInput, Prisma.ArtistUncheckedUpdateWithoutTracksInput>
}

export type ArtistUpdateWithoutTracksInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  instagram?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  biography?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  country?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  genre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  label?: Prisma.LabelUpdateOneWithoutArtistsNestedInput
  user?: Prisma.UserUpdateOneRequiredWithoutArtistsNestedInput
  identifiers?: Prisma.ArtistIdentifierUpdateManyWithoutArtistNestedInput
  releases?: Prisma.ReleaseArtistUpdateManyWithoutArtistNestedInput
}

export type ArtistUncheckedUpdateWithoutTracksInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  instagram?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  biography?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  country?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  labelId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  genre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  identifiers?: Prisma.ArtistIdentifierUncheckedUpdateManyWithoutArtistNestedInput
  releases?: Prisma.ReleaseArtistUncheckedUpdateManyWithoutArtistNestedInput
}

export type ArtistCreateManyUserInput = {
  id?: string
  name: string
  instagram?: string | null
  biography?: string | null
  country?: string | null
  labelId?: string | null
  genre?: string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type ArtistUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  instagram?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  biography?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  country?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  genre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  label?: Prisma.LabelUpdateOneWithoutArtistsNestedInput
  identifiers?: Prisma.ArtistIdentifierUpdateManyWithoutArtistNestedInput
  releases?: Prisma.ReleaseArtistUpdateManyWithoutArtistNestedInput
  tracks?: Prisma.TrackArtistUpdateManyWithoutArtistNestedInput
}

export type ArtistUncheckedUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  instagram?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  biography?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  country?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  labelId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  genre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  identifiers?: Prisma.ArtistIdentifierUncheckedUpdateManyWithoutArtistNestedInput
  releases?: Prisma.ReleaseArtistUncheckedUpdateManyWithoutArtistNestedInput
  tracks?: Prisma.TrackArtistUncheckedUpdateManyWithoutArtistNestedInput
}

export type ArtistUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  instagram?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  biography?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  country?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  labelId?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  genre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ArtistCreateManyLabelInput = {
  id?: string
  name: string
  instagram?: string | null
  biography?: string | null
  country?: string | null
  genre?: string | null
  userId: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type ArtistUpdateWithoutLabelInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  instagram?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  biography?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  country?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  genre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutArtistsNestedInput
  identifiers?: Prisma.ArtistIdentifierUpdateManyWithoutArtistNestedInput
  releases?: Prisma.ReleaseArtistUpdateManyWithoutArtistNestedInput
  tracks?: Prisma.TrackArtistUpdateManyWithoutArtistNestedInput
}

export type ArtistUncheckedUpdateWithoutLabelInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  instagram?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  biography?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  country?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  genre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  identifiers?: Prisma.ArtistIdentifierUncheckedUpdateManyWithoutArtistNestedInput
  releases?: Prisma.ReleaseArtistUncheckedUpdateManyWithoutArtistNestedInput
  tracks?: Prisma.TrackArtistUncheckedUpdateManyWithoutArtistNestedInput
}

export type ArtistUncheckedUpdateManyWithoutLabelInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  instagram?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  biography?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  country?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  genre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}


/**
 * Count Type ArtistCountOutputType
 */

export type ArtistCountOutputType = {
  identifiers: number
  releases: number
  tracks: number
}

export type ArtistCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  identifiers?: boolean | ArtistCountOutputTypeCountIdentifiersArgs
  releases?: boolean | ArtistCountOutputTypeCountReleasesArgs
  tracks?: boolean | ArtistCountOutputTypeCountTracksArgs
}

/**
 * ArtistCountOutputType without action
 */
export type ArtistCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ArtistCountOutputType
   */
  select?: Prisma.ArtistCountOutputTypeSelect<ExtArgs> | null
}

/**
 * ArtistCountOutputType without action
 */
export type ArtistCountOutputTypeCountIdentifiersArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ArtistIdentifierWhereInput
}

/**
 * ArtistCountOutputType without action
 */
export type ArtistCountOutputTypeCountReleasesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ReleaseArtistWhereInput
}

/**
 * ArtistCountOutputType without action
 */
export type ArtistCountOutputTypeCountTracksArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.TrackArtistWhereInput
}


export type ArtistSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  instagram?: boolean
  biography?: boolean
  country?: boolean
  labelId?: boolean
  genre?: boolean
  userId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  label?: boolean | Prisma.Artist$labelArgs<ExtArgs>
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  identifiers?: boolean | Prisma.Artist$identifiersArgs<ExtArgs>
  releases?: boolean | Prisma.Artist$releasesArgs<ExtArgs>
  tracks?: boolean | Prisma.Artist$tracksArgs<ExtArgs>
  _count?: boolean | Prisma.ArtistCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["artist"]>

export type ArtistSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  instagram?: boolean
  biography?: boolean
  country?: boolean
  labelId?: boolean
  genre?: boolean
  userId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  label?: boolean | Prisma.Artist$labelArgs<ExtArgs>
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}, ExtArgs["result"]["artist"]>

export type ArtistSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  instagram?: boolean
  biography?: boolean
  country?: boolean
  labelId?: boolean
  genre?: boolean
  userId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  label?: boolean | Prisma.Artist$labelArgs<ExtArgs>
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}, ExtArgs["result"]["artist"]>

export type ArtistSelectScalar = {
  id?: boolean
  name?: boolean
  instagram?: boolean
  biography?: boolean
  country?: boolean
  labelId?: boolean
  genre?: boolean
  userId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type ArtistOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "name" | "instagram" | "biography" | "country" | "labelId" | "genre" | "userId" | "createdAt" | "updatedAt", ExtArgs["result"]["artist"]>
export type ArtistInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  label?: boolean | Prisma.Artist$labelArgs<ExtArgs>
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  identifiers?: boolean | Prisma.Artist$identifiersArgs<ExtArgs>
  releases?: boolean | Prisma.Artist$releasesArgs<ExtArgs>
  tracks?: boolean | Prisma.Artist$tracksArgs<ExtArgs>
  _count?: boolean | Prisma.ArtistCountOutputTypeDefaultArgs<ExtArgs>
}
export type ArtistIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  label?: boolean | Prisma.Artist$labelArgs<ExtArgs>
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}
export type ArtistIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  label?: boolean | Prisma.Artist$labelArgs<ExtArgs>
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}

export type $ArtistPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Artist"
  objects: {
    label: Prisma.$LabelPayload<ExtArgs> | null
    user: Prisma.$UserPayload<ExtArgs>
    identifiers: Prisma.$ArtistIdentifierPayload<ExtArgs>[]
    releases: Prisma.$ReleaseArtistPayload<ExtArgs>[]
    tracks: Prisma.$TrackArtistPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    name: string
    instagram: string | null
    biography: string | null
    country: string | null
    labelId: string | null
    genre: string | null
    userId: string
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["artist"]>
  composites: {}
}

export type ArtistGetPayload<S extends boolean | null | undefined | ArtistDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$ArtistPayload, S>

export type ArtistCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<ArtistFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: ArtistCountAggregateInputType | true
  }

export interface ArtistDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Artist'], meta: { name: 'Artist' } }
  /**
   * Find zero or one Artist that matches the filter.
   * @param {ArtistFindUniqueArgs} args - Arguments to find a Artist
   * @example
   * // Get one Artist
   * const artist = await prisma.artist.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends ArtistFindUniqueArgs>(args: Prisma.SelectSubset<T, ArtistFindUniqueArgs<ExtArgs>>): Prisma.Prisma__ArtistClient<runtime.Types.Result.GetResult<Prisma.$ArtistPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Artist that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {ArtistFindUniqueOrThrowArgs} args - Arguments to find a Artist
   * @example
   * // Get one Artist
   * const artist = await prisma.artist.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends ArtistFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, ArtistFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__ArtistClient<runtime.Types.Result.GetResult<Prisma.$ArtistPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Artist that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ArtistFindFirstArgs} args - Arguments to find a Artist
   * @example
   * // Get one Artist
   * const artist = await prisma.artist.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends ArtistFindFirstArgs>(args?: Prisma.SelectSubset<T, ArtistFindFirstArgs<ExtArgs>>): Prisma.Prisma__ArtistClient<runtime.Types.Result.GetResult<Prisma.$ArtistPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Artist that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ArtistFindFirstOrThrowArgs} args - Arguments to find a Artist
   * @example
   * // Get one Artist
   * const artist = await prisma.artist.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends ArtistFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, ArtistFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__ArtistClient<runtime.Types.Result.GetResult<Prisma.$ArtistPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Artists that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ArtistFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Artists
   * const artists = await prisma.artist.findMany()
   * 
   * // Get first 10 Artists
   * const artists = await prisma.artist.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const artistWithIdOnly = await prisma.artist.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends ArtistFindManyArgs>(args?: Prisma.SelectSubset<T, ArtistFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ArtistPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Artist.
   * @param {ArtistCreateArgs} args - Arguments to create a Artist.
   * @example
   * // Create one Artist
   * const Artist = await prisma.artist.create({
   *   data: {
   *     // ... data to create a Artist
   *   }
   * })
   * 
   */
  create<T extends ArtistCreateArgs>(args: Prisma.SelectSubset<T, ArtistCreateArgs<ExtArgs>>): Prisma.Prisma__ArtistClient<runtime.Types.Result.GetResult<Prisma.$ArtistPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Artists.
   * @param {ArtistCreateManyArgs} args - Arguments to create many Artists.
   * @example
   * // Create many Artists
   * const artist = await prisma.artist.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends ArtistCreateManyArgs>(args?: Prisma.SelectSubset<T, ArtistCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Artists and returns the data saved in the database.
   * @param {ArtistCreateManyAndReturnArgs} args - Arguments to create many Artists.
   * @example
   * // Create many Artists
   * const artist = await prisma.artist.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Artists and only return the `id`
   * const artistWithIdOnly = await prisma.artist.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends ArtistCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, ArtistCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ArtistPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a Artist.
   * @param {ArtistDeleteArgs} args - Arguments to delete one Artist.
   * @example
   * // Delete one Artist
   * const Artist = await prisma.artist.delete({
   *   where: {
   *     // ... filter to delete one Artist
   *   }
   * })
   * 
   */
  delete<T extends ArtistDeleteArgs>(args: Prisma.SelectSubset<T, ArtistDeleteArgs<ExtArgs>>): Prisma.Prisma__ArtistClient<runtime.Types.Result.GetResult<Prisma.$ArtistPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Artist.
   * @param {ArtistUpdateArgs} args - Arguments to update one Artist.
   * @example
   * // Update one Artist
   * const artist = await prisma.artist.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends ArtistUpdateArgs>(args: Prisma.SelectSubset<T, ArtistUpdateArgs<ExtArgs>>): Prisma.Prisma__ArtistClient<runtime.Types.Result.GetResult<Prisma.$ArtistPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Artists.
   * @param {ArtistDeleteManyArgs} args - Arguments to filter Artists to delete.
   * @example
   * // Delete a few Artists
   * const { count } = await prisma.artist.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends ArtistDeleteManyArgs>(args?: Prisma.SelectSubset<T, ArtistDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Artists.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ArtistUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Artists
   * const artist = await prisma.artist.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends ArtistUpdateManyArgs>(args: Prisma.SelectSubset<T, ArtistUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Artists and returns the data updated in the database.
   * @param {ArtistUpdateManyAndReturnArgs} args - Arguments to update many Artists.
   * @example
   * // Update many Artists
   * const artist = await prisma.artist.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Artists and only return the `id`
   * const artistWithIdOnly = await prisma.artist.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends ArtistUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, ArtistUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ArtistPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one Artist.
   * @param {ArtistUpsertArgs} args - Arguments to update or create a Artist.
   * @example
   * // Update or create a Artist
   * const artist = await prisma.artist.upsert({
   *   create: {
   *     // ... data to create a Artist
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Artist we want to update
   *   }
   * })
   */
  upsert<T extends ArtistUpsertArgs>(args: Prisma.SelectSubset<T, ArtistUpsertArgs<ExtArgs>>): Prisma.Prisma__ArtistClient<runtime.Types.Result.GetResult<Prisma.$ArtistPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Artists.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ArtistCountArgs} args - Arguments to filter Artists to count.
   * @example
   * // Count the number of Artists
   * const count = await prisma.artist.count({
   *   where: {
   *     // ... the filter for the Artists we want to count
   *   }
   * })
  **/
  count<T extends ArtistCountArgs>(
    args?: Prisma.Subset<T, ArtistCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], ArtistCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Artist.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ArtistAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends ArtistAggregateArgs>(args: Prisma.Subset<T, ArtistAggregateArgs>): Prisma.PrismaPromise<GetArtistAggregateType<T>>

  /**
   * Group by Artist.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ArtistGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends ArtistGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: ArtistGroupByArgs['orderBy'] }
      : { orderBy?: ArtistGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, ArtistGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetArtistGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Artist model
 */
readonly fields: ArtistFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Artist.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__ArtistClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  label<T extends Prisma.Artist$labelArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Artist$labelArgs<ExtArgs>>): Prisma.Prisma__LabelClient<runtime.Types.Result.GetResult<Prisma.$LabelPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
  user<T extends Prisma.UserDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.UserDefaultArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  identifiers<T extends Prisma.Artist$identifiersArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Artist$identifiersArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ArtistIdentifierPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  releases<T extends Prisma.Artist$releasesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Artist$releasesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ReleaseArtistPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  tracks<T extends Prisma.Artist$tracksArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Artist$tracksArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TrackArtistPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Artist model
 */
export interface ArtistFieldRefs {
  readonly id: Prisma.FieldRef<"Artist", 'String'>
  readonly name: Prisma.FieldRef<"Artist", 'String'>
  readonly instagram: Prisma.FieldRef<"Artist", 'String'>
  readonly biography: Prisma.FieldRef<"Artist", 'String'>
  readonly country: Prisma.FieldRef<"Artist", 'String'>
  readonly labelId: Prisma.FieldRef<"Artist", 'String'>
  readonly genre: Prisma.FieldRef<"Artist", 'String'>
  readonly userId: Prisma.FieldRef<"Artist", 'String'>
  readonly createdAt: Prisma.FieldRef<"Artist", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"Artist", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Artist findUnique
 */
export type ArtistFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Artist
   */
  select?: Prisma.ArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Artist
   */
  omit?: Prisma.ArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ArtistInclude<ExtArgs> | null
  /**
   * Filter, which Artist to fetch.
   */
  where: Prisma.ArtistWhereUniqueInput
}

/**
 * Artist findUniqueOrThrow
 */
export type ArtistFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Artist
   */
  select?: Prisma.ArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Artist
   */
  omit?: Prisma.ArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ArtistInclude<ExtArgs> | null
  /**
   * Filter, which Artist to fetch.
   */
  where: Prisma.ArtistWhereUniqueInput
}

/**
 * Artist findFirst
 */
export type ArtistFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Artist
   */
  select?: Prisma.ArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Artist
   */
  omit?: Prisma.ArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ArtistInclude<ExtArgs> | null
  /**
   * Filter, which Artist to fetch.
   */
  where?: Prisma.ArtistWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Artists to fetch.
   */
  orderBy?: Prisma.ArtistOrderByWithRelationInput | Prisma.ArtistOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Artists.
   */
  cursor?: Prisma.ArtistWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Artists from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Artists.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Artists.
   */
  distinct?: Prisma.ArtistScalarFieldEnum | Prisma.ArtistScalarFieldEnum[]
}

/**
 * Artist findFirstOrThrow
 */
export type ArtistFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Artist
   */
  select?: Prisma.ArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Artist
   */
  omit?: Prisma.ArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ArtistInclude<ExtArgs> | null
  /**
   * Filter, which Artist to fetch.
   */
  where?: Prisma.ArtistWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Artists to fetch.
   */
  orderBy?: Prisma.ArtistOrderByWithRelationInput | Prisma.ArtistOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Artists.
   */
  cursor?: Prisma.ArtistWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Artists from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Artists.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Artists.
   */
  distinct?: Prisma.ArtistScalarFieldEnum | Prisma.ArtistScalarFieldEnum[]
}

/**
 * Artist findMany
 */
export type ArtistFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Artist
   */
  select?: Prisma.ArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Artist
   */
  omit?: Prisma.ArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ArtistInclude<ExtArgs> | null
  /**
   * Filter, which Artists to fetch.
   */
  where?: Prisma.ArtistWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Artists to fetch.
   */
  orderBy?: Prisma.ArtistOrderByWithRelationInput | Prisma.ArtistOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Artists.
   */
  cursor?: Prisma.ArtistWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Artists from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Artists.
   */
  skip?: number
  distinct?: Prisma.ArtistScalarFieldEnum | Prisma.ArtistScalarFieldEnum[]
}

/**
 * Artist create
 */
export type ArtistCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Artist
   */
  select?: Prisma.ArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Artist
   */
  omit?: Prisma.ArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ArtistInclude<ExtArgs> | null
  /**
   * The data needed to create a Artist.
   */
  data: Prisma.XOR<Prisma.ArtistCreateInput, Prisma.ArtistUncheckedCreateInput>
}

/**
 * Artist createMany
 */
export type ArtistCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Artists.
   */
  data: Prisma.ArtistCreateManyInput | Prisma.ArtistCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Artist createManyAndReturn
 */
export type ArtistCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Artist
   */
  select?: Prisma.ArtistSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Artist
   */
  omit?: Prisma.ArtistOmit<ExtArgs> | null
  /**
   * The data used to create many Artists.
   */
  data: Prisma.ArtistCreateManyInput | Prisma.ArtistCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ArtistIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * Artist update
 */
export type ArtistUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Artist
   */
  select?: Prisma.ArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Artist
   */
  omit?: Prisma.ArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ArtistInclude<ExtArgs> | null
  /**
   * The data needed to update a Artist.
   */
  data: Prisma.XOR<Prisma.ArtistUpdateInput, Prisma.ArtistUncheckedUpdateInput>
  /**
   * Choose, which Artist to update.
   */
  where: Prisma.ArtistWhereUniqueInput
}

/**
 * Artist updateMany
 */
export type ArtistUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Artists.
   */
  data: Prisma.XOR<Prisma.ArtistUpdateManyMutationInput, Prisma.ArtistUncheckedUpdateManyInput>
  /**
   * Filter which Artists to update
   */
  where?: Prisma.ArtistWhereInput
  /**
   * Limit how many Artists to update.
   */
  limit?: number
}

/**
 * Artist updateManyAndReturn
 */
export type ArtistUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Artist
   */
  select?: Prisma.ArtistSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Artist
   */
  omit?: Prisma.ArtistOmit<ExtArgs> | null
  /**
   * The data used to update Artists.
   */
  data: Prisma.XOR<Prisma.ArtistUpdateManyMutationInput, Prisma.ArtistUncheckedUpdateManyInput>
  /**
   * Filter which Artists to update
   */
  where?: Prisma.ArtistWhereInput
  /**
   * Limit how many Artists to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ArtistIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * Artist upsert
 */
export type ArtistUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Artist
   */
  select?: Prisma.ArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Artist
   */
  omit?: Prisma.ArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ArtistInclude<ExtArgs> | null
  /**
   * The filter to search for the Artist to update in case it exists.
   */
  where: Prisma.ArtistWhereUniqueInput
  /**
   * In case the Artist found by the `where` argument doesn't exist, create a new Artist with this data.
   */
  create: Prisma.XOR<Prisma.ArtistCreateInput, Prisma.ArtistUncheckedCreateInput>
  /**
   * In case the Artist was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.ArtistUpdateInput, Prisma.ArtistUncheckedUpdateInput>
}

/**
 * Artist delete
 */
export type ArtistDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Artist
   */
  select?: Prisma.ArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Artist
   */
  omit?: Prisma.ArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ArtistInclude<ExtArgs> | null
  /**
   * Filter which Artist to delete.
   */
  where: Prisma.ArtistWhereUniqueInput
}

/**
 * Artist deleteMany
 */
export type ArtistDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Artists to delete
   */
  where?: Prisma.ArtistWhereInput
  /**
   * Limit how many Artists to delete.
   */
  limit?: number
}

/**
 * Artist.label
 */
export type Artist$labelArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Label
   */
  select?: Prisma.LabelSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Label
   */
  omit?: Prisma.LabelOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LabelInclude<ExtArgs> | null
  where?: Prisma.LabelWhereInput
}

/**
 * Artist.identifiers
 */
export type Artist$identifiersArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ArtistIdentifier
   */
  select?: Prisma.ArtistIdentifierSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ArtistIdentifier
   */
  omit?: Prisma.ArtistIdentifierOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ArtistIdentifierInclude<ExtArgs> | null
  where?: Prisma.ArtistIdentifierWhereInput
  orderBy?: Prisma.ArtistIdentifierOrderByWithRelationInput | Prisma.ArtistIdentifierOrderByWithRelationInput[]
  cursor?: Prisma.ArtistIdentifierWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.ArtistIdentifierScalarFieldEnum | Prisma.ArtistIdentifierScalarFieldEnum[]
}

/**
 * Artist.releases
 */
export type Artist$releasesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseArtist
   */
  select?: Prisma.ReleaseArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseArtist
   */
  omit?: Prisma.ReleaseArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseArtistInclude<ExtArgs> | null
  where?: Prisma.ReleaseArtistWhereInput
  orderBy?: Prisma.ReleaseArtistOrderByWithRelationInput | Prisma.ReleaseArtistOrderByWithRelationInput[]
  cursor?: Prisma.ReleaseArtistWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.ReleaseArtistScalarFieldEnum | Prisma.ReleaseArtistScalarFieldEnum[]
}

/**
 * Artist.tracks
 */
export type Artist$tracksArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackArtist
   */
  select?: Prisma.TrackArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackArtist
   */
  omit?: Prisma.TrackArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackArtistInclude<ExtArgs> | null
  where?: Prisma.TrackArtistWhereInput
  orderBy?: Prisma.TrackArtistOrderByWithRelationInput | Prisma.TrackArtistOrderByWithRelationInput[]
  cursor?: Prisma.TrackArtistWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.TrackArtistScalarFieldEnum | Prisma.TrackArtistScalarFieldEnum[]
}

/**
 * Artist without action
 */
export type ArtistDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Artist
   */
  select?: Prisma.ArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Artist
   */
  omit?: Prisma.ArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ArtistInclude<ExtArgs> | null
}
