import { Badge } from "@/components/ui/badge";

const platformDetails: Record<
  string,
  {
    name: string;
    color: string;
    iconSlug: string;
    textColor: string;
    logoColor: string;
  }
> = {
  spotify: {
    name: "Spotify",
    color: "1ED760",
    iconSlug: "spotify",
    textColor: "#000000",
    logoColor: "000000",
  },
  applemusic: {
    name: "Apple Music",
    color: "FA243C",
    iconSlug: "applemusic",
    textColor: "#ffffff",
    logoColor: "ffffff",
  },
  youtubemusic: {
    name: "YouTube Music",
    color: "FF0000",
    iconSlug: "youtubemusic",
    textColor: "#ffffff",
    logoColor: "ffffff",
  },
  audiomack: {
    name: "Audiomack",
    color: "FFA200",
    iconSlug: "audiomack",
    textColor: "#000000",
    logoColor: "000000",
  },
  soundcloud: {
    name: "SoundCloud",
    color: "FF5500",
    iconSlug: "soundcloud",
    textColor: "#ffffff",
    logoColor: "ffffff",
  },
  deezer: {
    name: "<PERSON><PERSON>",
    color: "191919",
    iconSlug: "deezer",
    textColor: "#ffffff",
    logoColor: "ffffff",
  },
  amazonmusic: {
    name: "Amazon Music",
    color: "25D1DA",
    iconSlug: "amazonmusic",
    textColor: "#000000",
    logoColor: "000000",
  },
  tidal: {
    name: "Tidal",
    color: "000000",
    iconSlug: "tidal",
    textColor: "#ffffff",
    logoColor: "ffffff",
  },
};

interface PlatformBadgeProps {
  service: string;
  identifier?: string;
  width?: number;
  height?: number;
  className?: string;
}

export function PlatformBadge({
  service,
  identifier,
  width = 20,
  height = 20,
  className,
}: PlatformBadgeProps) {
  // Handle different possible service name formats
  let platformKey = service.toLowerCase().replace(/_/g, "");

  // Map alternative service names to our standard keys
  const serviceMapping: Record<string, string> = {
    youtube: "youtubemusic",
    youtube_music: "youtubemusic",
    youtubemusic: "youtubemusic",
    apple_music: "applemusic",
    applemusic: "applemusic",
    sound_cloud: "soundcloud",
    soundcloud: "soundcloud",
    amazon_music: "amazonmusic",
    amazonmusic: "amazonmusic",
  };

  platformKey = serviceMapping[platformKey] || platformKey;
  const details = platformDetails[platformKey];
  const serviceName = service
    .replace(/_/g, " ")
    .replace(/\b\w/g, (l) => l.toUpperCase());

  if (!details) {
    return (
      <Badge variant="outline" className={className}>
        <div
          style={{ width, height }}
          className="bg-muted rounded-sm mr-1"
          title={serviceName}
        />
        {serviceName}
      </Badge>
    );
  }

  const { name, color, iconSlug, textColor, logoColor } = details;

  // Handle "Create New" case with different styling
  if (!identifier || identifier === "Create New") {
    return (
      <Badge
        variant="outline"
        className={`border-2 border-dashed border-muted-foreground/50 bg-muted/30 text-muted-foreground h-10 ${className}`}
        title={`We will create a ${name} profile for you`}
      >
        <div className="flex items-center gap-1">
          <img
            height={height}
            width={width}
            src={`https://cdn.simpleicons.org/${iconSlug}/9CA3AF`}
            alt={`${name} logo`}
            className="rounded-sm opacity-60"
          />
          <span className="text-sm">No Profile</span>
        </div>
      </Badge>
    );
  }

  // Regular platform badge with brand colors
  const badgeStyle = {
    backgroundColor: `#${color}`,
    color: textColor,
    borderColor: `#${color}`,
  };

  // Use the identifier as the full URL directly
  return (
    <Badge
      asChild
      variant="outline"
      className={`border-2 text-sm cursor-pointer hover:opacity-80 transition-opacity ${className}`}
      style={badgeStyle}
    >
      <a
        href={identifier}
        target="_blank"
        rel="noopener noreferrer"
        title={`View on ${name}`}
      >
        <img
          height={height}
          width={width}
          src={`https://cdn.simpleicons.org/${iconSlug}/${logoColor}`}
          alt={`${name} logo`}
          className="rounded-sm mr-1"
        />
        {name}
      </a>
    </Badge>
  );
}
