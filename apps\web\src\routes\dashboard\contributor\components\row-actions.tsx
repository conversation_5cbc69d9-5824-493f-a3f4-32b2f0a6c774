import type { Row } from "@tanstack/react-table";
import {
  EllipsisIcon,
  TrashIcon,
  CircleAlertIcon,
  EditIcon,
} from "lucide-react";
import { toast } from "sonner";
import { useMutation } from "@tanstack/react-query";

import { useTRPCClient } from "@/utils/trpc";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { UpdateContributorDialog } from "@/routes/dashboard/contributor/components/update-contributor-dialog";
import type { ContributorTableItem } from "@/routes/dashboard/contributor/components/contributor-table";

interface RowActionsProps {
  row: Row<ContributorTableItem>;
  onContributorDeleted: () => void;
}

export function RowActions({ row, onContributorDeleted }: RowActionsProps) {
  const trpcClient = useTRPCClient();

  const deleteContributorMutation = useMutation({
    mutationFn: async () => {
      return trpcClient.contributor.delete.mutate({
        id: row.original.id,
      });
    },
    onSuccess: () => {
      toast.success(`Successfully deleted contributor ${row.original.name}`);
      onContributorDeleted();
    },
    onError: (error: any) => {
      console.error("Failed to delete contributor:", error);
      toast.error(
        "Failed to delete contributor: " + (error.message || "Unknown error")
      );
    },
  });

  const handleDeleteContributor = async () => {
    try {
      await deleteContributorMutation.mutateAsync();
    } catch (error) {
      // Error handling is done in the mutation
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="flex justify-end">
          <Button
            size="icon"
            variant="ghost"
            className="shadow-none cursor-pointer"
            aria-label="Edit item"
          >
            <EllipsisIcon size={16} aria-hidden="true" />
          </Button>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuGroup>
          <UpdateContributorDialog
            contributor={row.original}
            onContributorUpdated={onContributorDeleted}
          >
            <DropdownMenuItem
              onSelect={(e) => e.preventDefault()}
              className="cursor-pointer"
            >
              <EditIcon className="h-4 w-4" />
              <span>Edit Contributor</span>
            </DropdownMenuItem>
          </UpdateContributorDialog>
        </DropdownMenuGroup>
        <DropdownMenuSeparator className="h-[2px]" />
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <DropdownMenuItem
              className="text-destructive focus:text-destructive cursor-pointer"
              onSelect={(e) => e.preventDefault()}
            >
              <div className="flex items-center gap-2">
                <TrashIcon
                  size={16}
                  className="text-destructive focus:text-destructive"
                />
                <span>Delete</span>
              </div>
            </DropdownMenuItem>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <div className="flex flex-col gap-2 max-sm:items-center sm:flex-row sm:gap-4">
              <div
                className="flex size-9 shrink-0 items-center justify-center rounded-full border"
                aria-hidden="true"
              >
                <CircleAlertIcon className="opacity-80" size={16} />
              </div>
              <AlertDialogHeader>
                <AlertDialogTitle>Delete Contributor</AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to delete{" "}
                  <strong>{row.original.name}</strong>? This action cannot be
                  undone and will permanently remove the contributor.
                </AlertDialogDescription>
              </AlertDialogHeader>
            </div>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDeleteContributor}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                disabled={deleteContributorMutation.isPending}
              >
                {deleteContributorMutation.isPending
                  ? "Deleting..."
                  : "Delete Contributor"}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
