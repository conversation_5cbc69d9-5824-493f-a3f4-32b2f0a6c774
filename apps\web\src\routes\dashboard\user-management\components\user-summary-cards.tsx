import React from "react";
import { useQuery } from "@tanstack/react-query";
import { Users, UserX, Clock, User, AlertTriangle } from "lucide-react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { authClient } from "@/lib/auth-client";
import { toast } from "sonner";

// Better Auth UserWithRole type (from the API response)
type UserWithRole = {
  id: string;
  name: string;
  email: string;
  emailVerified: boolean;
  image?: string | null;
  phone?: string | null;
  role?: string | null;
  banned?: boolean | null;
  banReason?: string | null;
  banExpires?: Date | null;
  createdAt: Date;
  updatedAt: Date;
};

interface UserSummaryStats {
  total: number;
  banned: number;
  pending: number;
  artists: number;
}

export default function UserSummaryCards() {
  // Fetch all users to calculate statistics
  const {
    data: usersResponse,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["user-summary-stats"],
    queryFn: async () => {
      try {
        // Fetch all users for summary statistics
        const result = await authClient.admin.listUsers({
          query: {
            limit: 1000, // Get all users for accurate stats
          },
        });

        return result;
      } catch (error: any) {
        console.error("Failed to fetch user summary stats:", error);
        toast.error(
          "Failed to fetch user statistics: " +
            (error.message || "Unknown error")
        );
        throw error;
      }
    },
    staleTime: 30000, // 30 seconds
    retry: 2,
  });

  // Calculate statistics from the user data
  const stats: UserSummaryStats = React.useMemo(() => {
    if (!usersResponse?.data?.users) {
      return {
        total: 0,
        banned: 0,
        pending: 0,
        artists: 0,
      };
    }

    const users = usersResponse.data.users;

    return users.reduce(
      (acc: UserSummaryStats, user: UserWithRole) => {
        acc.total++;

        // Count by status
        if (user.banned) {
          acc.banned++;
        } else if (!user.emailVerified) {
          acc.pending++;
        }

        // Count artists (non-admin users)
        if (user.role !== "admin") {
          acc.artists++;
        }

        return acc;
      },
      {
        total: 0,
        banned: 0,
        pending: 0,
        artists: 0,
      }
    );
  }, [usersResponse?.data?.users]);

  if (error) {
    return (
      <div className="grid gap-4 grid-cols-2 lg:grid-cols-4">
        <Card className="border-red-200">
          <CardContent className="flex items-center justify-center p-6">
            <div className="flex flex-col items-center gap-2">
              <AlertTriangle className="h-8 w-8 text-red-500" />
              <p className="text-sm text-muted-foreground">
                Failed to load statistics
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="grid gap-4 grid-cols-2 lg:grid-cols-4">
      {/* Total Users */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Users</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {isLoading ? <Skeleton className="h-8 w-16" /> : stats.total}
          </div>
          <p className="text-xs text-muted-foreground">All registered users</p>
        </CardContent>
      </Card>

      {/* Artists */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Artists</CardTitle>
          <User className="h-4 w-4 text-purple-600" />
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2">
            <div className="text-2xl font-bold text-purple-600">
              {isLoading ? <Skeleton className="h-8 w-16" /> : stats.artists}
            </div>
            {!isLoading && stats.total > 0 && (
              <Badge variant="secondary" className="text-xs">
                {Math.round((stats.artists / stats.total) * 100)}%
              </Badge>
            )}
          </div>
          <p className="text-xs text-muted-foreground">Artist role users</p>
        </CardContent>
      </Card>

      {/* Pending Users */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Pending Users</CardTitle>
          <Clock className="h-4 w-4 text-yellow-600" />
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2">
            <div className="text-2xl font-bold text-yellow-600">
              {isLoading ? <Skeleton className="h-8 w-16" /> : stats.pending}
            </div>
            {!isLoading && stats.total > 0 && (
              <Badge variant="secondary" className="text-xs">
                {Math.round((stats.pending / stats.total) * 100)}%
              </Badge>
            )}
          </div>
          <p className="text-xs text-muted-foreground">
            Awaiting email verification
          </p>
        </CardContent>
      </Card>

      {/* Banned Users */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Banned Users</CardTitle>
          <UserX className="h-4 w-4 text-red-600" />
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2">
            <div className="text-2xl font-bold text-red-600">
              {isLoading ? <Skeleton className="h-8 w-16" /> : stats.banned}
            </div>
            {!isLoading && stats.total > 0 && (
              <Badge variant="secondary" className="text-xs">
                {Math.round((stats.banned / stats.total) * 100)}%
              </Badge>
            )}
          </div>
          <p className="text-xs text-muted-foreground">Suspended accounts</p>
        </CardContent>
      </Card>
    </div>
  );
}
