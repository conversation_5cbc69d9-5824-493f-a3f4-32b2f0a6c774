import { useState, useMemo } from "react";
import { Check, ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { genres, capitalizeGenre } from "@/lib/genre";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

// Popular genres that will be shown first
const POPULAR_GENRES = [
  "pop",
  "rock",
  "hip hop",
  "r&b",
  "electronic",
  "indie",
  "alternative",
  "country",
  "folk",
  "jazz",
  "blues",
  "classical",
  "reggae",
  "metal",
  "punk",
  "funk",
  "soul",
  "disco",
  "house",
  "techno",
  "dubstep",
  "trap",
  "lo-fi",
  "ambient",
  "gospel",
] as const;

interface GenreSelectProps {
  value?: string;
  onValueChange?: (value: string) => void;
  disabled?: boolean;
  placeholder?: string;
}

export function GenreSelect({
  value,
  onValueChange,
  disabled = false,
  placeholder = "Select genre...",
}: GenreSelectProps) {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  // Filter genres based on search query
  const filteredGenres = useMemo(() => {
    if (!searchQuery) {
      // Show popular genres first, then alphabetical order for others
      const popularGenresSet = new Set(POPULAR_GENRES);
      const otherGenres = genres
        .filter((genre) => !popularGenresSet.has(genre as any))
        .sort();

      return [...POPULAR_GENRES, ...otherGenres];
    }

    // Search through all genres
    const query = searchQuery.toLowerCase();
    return genres
      .filter((genre) => genre.toLowerCase().includes(query))
      .sort()
      .slice(0, 50); // Limit to 50 results for performance
  }, [searchQuery]);

  // Show popular and other groups when not searching
  const shouldShowGroups = !searchQuery;
  const popularFiltered = shouldShowGroups
    ? POPULAR_GENRES
    : filteredGenres.filter((genre) => POPULAR_GENRES.includes(genre as any));
  const otherFiltered = shouldShowGroups
    ? filteredGenres.filter((genre) => !POPULAR_GENRES.includes(genre as any))
    : filteredGenres.filter((genre) => !POPULAR_GENRES.includes(genre as any));

  const triggerClasses = cn(
    "flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1 cursor-pointer"
  );

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger
        className={triggerClasses}
        disabled={disabled}
        role="combobox"
        aria-expanded={open}
      >
        <span className="overflow-hidden text-ellipsis whitespace-nowrap">
          {value ? capitalizeGenre(value as any) : placeholder}
        </span>
        <ChevronDown size={16} />
      </PopoverTrigger>
      <PopoverContent
        className="w-full p-0 h-50"
        align="start"
        side="bottom"
        sideOffset={10}
      >
        <Command>
          <CommandInput
            placeholder="Search genres..."
            value={searchQuery}
            onValueChange={setSearchQuery}
          />
          <CommandList>
            <CommandEmpty>No genre found.</CommandEmpty>

            {shouldShowGroups && popularFiltered.length > 0 && (
              <CommandGroup heading="Popular Genres">
                {popularFiltered.map((genre) => (
                  <CommandItem
                    key={genre}
                    value={genre}
                    onSelect={(currentValue) => {
                      onValueChange?.(currentValue);
                      setOpen(false);
                      setSearchQuery("");
                    }}
                    className="cursor-pointer"
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        value === genre ? "opacity-100" : "opacity-0"
                      )}
                    />
                    {capitalizeGenre(genre)}
                  </CommandItem>
                ))}
              </CommandGroup>
            )}

            {shouldShowGroups && otherFiltered.length > 0 && (
              <CommandGroup heading="All Genres">
                {otherFiltered.slice(0, 20).map((genre) => (
                  <CommandItem
                    key={genre}
                    value={genre}
                    onSelect={(currentValue) => {
                      onValueChange?.(currentValue);
                      setOpen(false);
                      setSearchQuery("");
                    }}
                    className="cursor-pointer"
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        value === genre ? "opacity-100" : "opacity-0"
                      )}
                    />
                    {capitalizeGenre(genre)}
                  </CommandItem>
                ))}
                {otherFiltered.length > 20 && (
                  <div className="px-2 py-1.5 text-sm text-muted-foreground">
                    Type to search through {otherFiltered.length - 20} more...
                  </div>
                )}
              </CommandGroup>
            )}

            {!shouldShowGroups && filteredGenres.length > 0 && (
              <CommandGroup>
                {filteredGenres.map((genre) => (
                  <CommandItem
                    key={genre}
                    value={genre}
                    onSelect={(currentValue) => {
                      onValueChange?.(currentValue);
                      setOpen(false);
                      setSearchQuery("");
                    }}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        value === genre ? "opacity-100" : "opacity-0"
                      )}
                    />
                    {capitalizeGenre(genre)}
                  </CommandItem>
                ))}
              </CommandGroup>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
