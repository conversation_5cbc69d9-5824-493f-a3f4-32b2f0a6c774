import { useEffect, useId, useMemo, useRef, useState } from "react";
import {
  type ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  type PaginationState,
  type SortingState,
  useReactTable,
  type VisibilityState,
} from "@tanstack/react-table";
import {
  ChevronDownIcon,
  ChevronFirstIcon,
  ChevronLastIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ChevronUpIcon,
  CircleAlertIcon,
  CircleXIcon,
  Columns3Icon,
  FilterIcon,
  ListFilterIcon,
  PlusIcon,
  TrashIcon,
  UserIcon,
} from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";

import { cn } from "@/lib/utils";
import { authClient } from "@/lib/auth-client";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
} from "@/components/ui/pagination";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

// Import separated components
import { CreateUserDialog } from "./create-user-dialog";
import { createColumns, transformUserToTableItem } from "./user-table-columns";
import type { UserTableItem } from "./types";

export default function UserTable() {
  const id = useId();
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const inputRef = useRef<HTMLInputElement>(null);

  const [sorting, setSorting] = useState<SortingState>([
    {
      id: "name",
      desc: false,
    },
  ]);

  // Search and filter states
  const [searchQuery, setSearchQuery] = useState("");
  const [roleFilter, setRoleFilter] = useState<string[]>([]);
  const [statusFilter, setStatusFilter] = useState<string[]>([]);

  // Fetch users using Better Auth admin API
  const {
    data: usersResponse,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: [
      "admin-users",
      pagination.pageIndex,
      pagination.pageSize,
      searchQuery,
      roleFilter,
      statusFilter,
      sorting,
    ],
    queryFn: async () => {
      try {
        // Build query parameters for Better Auth admin.listUsers
        const queryParams: any = {
          limit: pagination.pageSize,
          offset: pagination.pageIndex * pagination.pageSize,
        };

        // Add search parameters if search query exists
        if (searchQuery) {
          if (searchQuery.includes("@")) {
            // Search by email if query contains @
            queryParams.searchField = "email";
            queryParams.searchOperator = "contains";
            queryParams.searchValue = searchQuery;
          } else {
            // Search by name if no @ symbol
            queryParams.searchField = "name";
            queryParams.searchOperator = "contains";
            queryParams.searchValue = searchQuery;
          }
        }

        // Add sorting if specified
        if (sorting.length > 0) {
          queryParams.sortBy = sorting[0].id;
          queryParams.sortDirection = sorting[0].desc ? "desc" : "asc";
        }

        // Add role filter if specified
        if (roleFilter.length > 0) {
          // Map display role back to API role
          const apiRole =
            roleFilter[0] === "Artist"
              ? "user"
              : roleFilter[0] === "Admin"
              ? "admin"
              : roleFilter[0];
          queryParams.filterField = "role";
          queryParams.filterOperator = "eq";
          queryParams.filterValue = apiRole; // Better Auth supports single filter value
        }

        const result = await authClient.admin.listUsers({
          query: queryParams,
        });

        return result;
      } catch (error: any) {
        console.error("Failed to fetch users:", error);
        toast.error(
          "Failed to fetch users: " + (error.message || "Unknown error")
        );
        throw error;
      }
    },
    staleTime: 30000, // 30 seconds
    retry: 2,
  });

  // Transform users data for the table
  const data = useMemo(() => {
    if (!usersResponse?.data?.users) return [];

    let transformedUsers = usersResponse.data.users.map(
      transformUserToTableItem
    );

    // Apply client-side filtering for status since Better Auth doesn't support status filtering directly
    // Role filtering is handled server-side in the query
    if (statusFilter.length > 0) {
      transformedUsers = transformedUsers.filter((user: UserTableItem) =>
        statusFilter.includes(user.status)
      );
    }

    // Apply client-side filtering for additional role filters if multiple roles are selected
    if (roleFilter.length > 1) {
      transformedUsers = transformedUsers.filter((user: UserTableItem) =>
        roleFilter.includes(user.displayRole)
      );
    }

    return transformedUsers;
  }, [usersResponse?.data?.users, roleFilter, statusFilter]);

  const handleDeleteRows = async () => {
    const selectedRows = table.getSelectedRowModel().rows;
    const userIds = selectedRows.map((row) => row.original.id);

    try {
      // Delete users one by one using Better Auth admin API
      const deletePromises = userIds.map((userId) =>
        authClient.admin.removeUser({ userId })
      );

      await Promise.all(deletePromises);

      toast.success(
        `Successfully deleted ${userIds.length} user${
          userIds.length > 1 ? "s" : ""
        }`
      );

      // Reset selection and refetch data
      table.resetRowSelection();
      refetch();
    } catch (error: any) {
      console.error("Failed to delete users:", error);
      toast.error(
        "Failed to delete users: " + (error.message || "Unknown error")
      );
    }
  };

  // Create columns with refetch callback
  const columns = useMemo(() => createColumns(refetch), [refetch]);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    enableSortingRemoval: false,
    getPaginationRowModel: getPaginationRowModel(),
    onPaginationChange: setPagination,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getFilteredRowModel: getFilteredRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    meta: {
      refetch,
    },
    state: {
      sorting,
      pagination,
      columnFilters,
      columnVisibility,
    },
  });

  // Get unique status and role values from data
  const uniqueStatusValues = useMemo(() => {
    const statuses = ["Active", "Banned", "Pending"];
    return statuses;
  }, []);

  const uniqueRoleValues = useMemo(() => {
    const roles = ["Admin", "Artist"]; // Display roles
    return roles;
  }, []);

  // Get counts for each status and role
  const statusCounts = useMemo(() => {
    const counts = new Map<string, number>();
    data.forEach((user) => {
      const current = counts.get(user.status) || 0;
      counts.set(user.status, current + 1);
    });
    return counts;
  }, [data]);

  const roleCounts = useMemo(() => {
    const counts = new Map<string, number>();
    data.forEach((user) => {
      const current = counts.get(user.displayRole) || 0;
      counts.set(user.displayRole, current + 1);
    });
    return counts;
  }, [data]);

  const handleStatusChange = (checked: boolean, value: string) => {
    if (checked) {
      setStatusFilter((prev) => [...prev, value]);
    } else {
      setStatusFilter((prev) => prev.filter((v) => v !== value));
    }
  };

  const handleRoleChange = (checked: boolean, value: string) => {
    if (checked) {
      setRoleFilter((prev) => [...prev, value]);
    } else {
      setRoleFilter((prev) => prev.filter((v) => v !== value));
    }
  };

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex flex-wrap items-center justify-between gap-3">
        <div className="flex items-center gap-3">
          {/* Filter by name or email */}
          <div className="relative">
            <Input
              id={`${id}-input`}
              ref={inputRef}
              className={cn(
                "peer min-w-60 max-sm:min-w-48 ps-9 h-9",
                Boolean(searchQuery) && "pe-9"
              )}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search by name or email..."
              type="text"
              aria-label="Search by name or email"
            />
            <div className="text-muted-foreground/80 pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 peer-disabled:opacity-50">
              <ListFilterIcon size={16} aria-hidden="true" />
            </div>
            {Boolean(searchQuery) && (
              <button
                className="text-muted-foreground/80 hover:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 absolute inset-y-0 end-0 flex h-full w-9 items-center justify-center rounded-e-md transition-[color,box-shadow] outline-none focus:z-10 focus-visible:ring-[3px] disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50"
                aria-label="Clear search"
                onClick={() => {
                  setSearchQuery("");
                  if (inputRef.current) {
                    inputRef.current.focus();
                  }
                }}
                disabled={isLoading}
              >
                <CircleXIcon size={16} aria-hidden="true" />
              </button>
            )}
          </div>

          {/* Combined Filter for Mobile */}
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="sm:hidden">
                <FilterIcon
                  className="opacity-60"
                  size={16}
                  aria-hidden="true"
                />

                {(statusFilter.length > 0 || roleFilter.length > 0) && (
                  <span className="bg-background text-muted-foreground/70 -me-1 inline-flex h-5 max-h-full items-center rounded border px-1 font-[inherit] text-[0.625rem] font-medium">
                    {statusFilter.length + roleFilter.length}
                  </span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 p-4" align="start">
              <div className="space-y-4">
                {/* Status Filters */}
                <div className="space-y-3">
                  <div className="text-muted-foreground text-sm font-medium">
                    Status
                  </div>
                  <div className="space-y-2">
                    {uniqueStatusValues.map((value, i) => (
                      <div key={value} className="flex items-center gap-2">
                        <Checkbox
                          id={`${id}-mobile-status-${i}`}
                          checked={statusFilter.includes(value)}
                          onCheckedChange={(checked: boolean) =>
                            handleStatusChange(checked, value)
                          }
                        />
                        <Label
                          htmlFor={`${id}-mobile-status-${i}`}
                          className="flex grow justify-between gap-2 font-normal"
                        >
                          {value}{" "}
                          <span className="text-muted-foreground ms-2 text-xs">
                            {statusCounts.get(value) || 0}
                          </span>
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Role Filters */}
                <div className="space-y-3">
                  <div className="text-muted-foreground text-sm font-medium">
                    Role
                  </div>
                  <div className="space-y-2">
                    {uniqueRoleValues.map((value, i) => (
                      <div key={value} className="flex items-center gap-2">
                        <Checkbox
                          id={`${id}-mobile-role-${i}`}
                          checked={roleFilter.includes(value)}
                          onCheckedChange={(checked: boolean) =>
                            handleRoleChange(checked, value)
                          }
                        />
                        <Label
                          htmlFor={`${id}-mobile-role-${i}`}
                          className="flex grow justify-between gap-2 font-normal"
                        >
                          <span className="capitalize">{value}</span>
                          <span className="text-muted-foreground ms-2 text-xs">
                            {roleCounts.get(value) || 0}
                          </span>
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Column Visibility */}
                <div className="space-y-3">
                  <div className="text-muted-foreground text-sm font-medium">
                    Columns
                  </div>
                  <div className="space-y-2">
                    {table
                      .getAllColumns()
                      .filter((column) => column.getCanHide())
                      .map((column) => (
                        <div
                          key={column.id}
                          className="flex items-center gap-2"
                        >
                          <Checkbox
                            id={`${id}-mobile-column-${column.id}`}
                            checked={column.getIsVisible()}
                            onCheckedChange={(checked: boolean) =>
                              column.toggleVisibility(!!checked)
                            }
                          />
                          <Label
                            htmlFor={`${id}-mobile-column-${column.id}`}
                            className="flex grow justify-between gap-2 font-normal capitalize"
                          >
                            {column.id}
                          </Label>
                        </div>
                      ))}
                  </div>
                </div>
              </div>
            </PopoverContent>
          </Popover>

          {/* Desktop Filters */}
          <div className="hidden sm:flex items-center gap-3">
            {/* Filter by status */}
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline">
                  <FilterIcon
                    className="-ms-1 opacity-60"
                    size={16}
                    aria-hidden="true"
                  />
                  Status
                  {statusFilter.length > 0 && (
                    <span className="bg-background text-muted-foreground/70 -me-1 inline-flex h-5 max-h-full items-center rounded border px-1 font-[inherit] text-[0.625rem] font-medium">
                      {statusFilter.length}
                    </span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto min-w-36 p-3" align="start">
                <div className="space-y-3">
                  <div className="text-muted-foreground text-xs font-medium">
                    Filters
                  </div>
                  <div className="space-y-3">
                    {uniqueStatusValues.map((value, i) => (
                      <div key={value} className="flex items-center gap-2">
                        <Checkbox
                          id={`${id}-status-${i}`}
                          checked={statusFilter.includes(value)}
                          onCheckedChange={(checked: boolean) =>
                            handleStatusChange(checked, value)
                          }
                        />
                        <Label
                          htmlFor={`${id}-status-${i}`}
                          className="flex grow justify-between gap-2 font-normal"
                        >
                          {value}{" "}
                          <span className="text-muted-foreground ms-2 text-xs">
                            {statusCounts.get(value) || 0}
                          </span>
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              </PopoverContent>
            </Popover>
            {/* Filter by role */}
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline">
                  <FilterIcon
                    className="-ms-1 opacity-60"
                    size={16}
                    aria-hidden="true"
                  />
                  Role
                  {roleFilter.length > 0 && (
                    <span className="bg-background text-muted-foreground/70 -me-1 inline-flex h-5 max-h-full items-center rounded border px-1 font-[inherit] text-[0.625rem] font-medium">
                      {roleFilter.length}
                    </span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto min-w-36 p-3" align="start">
                <div className="space-y-3">
                  <div className="text-muted-foreground text-xs font-medium">
                    Role Filters
                  </div>
                  <div className="space-y-3">
                    {uniqueRoleValues.map((value, i) => (
                      <div key={value} className="flex items-center gap-2">
                        <Checkbox
                          id={`${id}-role-${i}`}
                          checked={roleFilter.includes(value)}
                          onCheckedChange={(checked: boolean) =>
                            handleRoleChange(checked, value)
                          }
                        />
                        <Label
                          htmlFor={`${id}-role-${i}`}
                          className="flex grow justify-between gap-2 font-normal"
                        >
                          <span className="capitalize">{value}</span>
                          <span className="text-muted-foreground ms-2 text-xs">
                            {roleCounts.get(value) || 0}
                          </span>
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>
          {/* Toggle columns visibility - Desktop only */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="hidden sm:flex">
                <Columns3Icon
                  className="-ms-1 opacity-60"
                  size={16}
                  aria-hidden="true"
                />
                View
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Toggle columns</DropdownMenuLabel>
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                      onSelect={(event) => event.preventDefault()}
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <div className="flex items-center gap-3">
          {/* Delete button */}
          {table.getSelectedRowModel().rows.length > 0 && (
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="outline">
                  <TrashIcon
                    className="-ms-1 opacity-60"
                    size={16}
                    aria-hidden="true"
                  />
                  Delete
                  <span className="bg-background text-muted-foreground/70 -me-1 inline-flex h-5 max-h-full items-center rounded border px-1 font-[inherit] text-[0.625rem] font-medium">
                    {table.getSelectedRowModel().rows.length}
                  </span>
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <div className="flex flex-col gap-2 max-sm:items-center sm:flex-row sm:gap-4">
                  <div
                    className="flex size-9 shrink-0 items-center justify-center rounded-full border"
                    aria-hidden="true"
                  >
                    <CircleAlertIcon className="opacity-80" size={16} />
                  </div>
                  <AlertDialogHeader>
                    <AlertDialogTitle>
                      Are you absolutely sure?
                    </AlertDialogTitle>
                    <AlertDialogDescription>
                      This action cannot be undone. This will permanently delete{" "}
                      {table.getSelectedRowModel().rows.length} selected{" "}
                      {table.getSelectedRowModel().rows.length === 1
                        ? "row"
                        : "rows"}
                      .
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                </div>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={handleDeleteRows}>
                    Delete
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          )}
          {/* Add user button */}
          <CreateUserDialog onUserCreated={refetch}>
            <Button variant="outline">
              <PlusIcon
                className="-ms-1 opacity-60"
                size={16}
                aria-hidden="true"
              />
              Add User
            </Button>
          </CreateUserDialog>
        </div>
      </div>

      {/* Table */}
      <div className="bg-background rounded-md border">
        <Table className="table-fixed">
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} className="hover:bg-transparent">
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead
                      key={header.id}
                      style={{ width: `${header.getSize()}px` }}
                      className="h-11"
                    >
                      {header.isPlaceholder ? null : header.column.getCanSort() ? (
                        <div
                          className={cn(
                            header.column.getCanSort() &&
                              "flex h-full cursor-pointer items-center justify-between gap-2 select-none"
                          )}
                          onClick={header.column.getToggleSortingHandler()}
                          onKeyDown={(e) => {
                            // Enhanced keyboard handling for sorting
                            if (
                              header.column.getCanSort() &&
                              (e.key === "Enter" || e.key === " ")
                            ) {
                              e.preventDefault();
                              header.column.getToggleSortingHandler()?.(e);
                            }
                          }}
                          tabIndex={header.column.getCanSort() ? 0 : undefined}
                        >
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                          {{
                            asc: (
                              <ChevronUpIcon
                                className="shrink-0 opacity-60"
                                size={16}
                                aria-hidden="true"
                              />
                            ),
                            desc: (
                              <ChevronDownIcon
                                className="shrink-0 opacity-60"
                                size={16}
                                aria-hidden="true"
                              />
                            ),
                          }[header.column.getIsSorted() as string] ?? null}
                        </div>
                      ) : (
                        flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )
                      )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  <div className="flex items-center justify-center gap-2">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                    Loading users...
                  </div>
                </TableCell>
              </TableRow>
            ) : error ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  <div className="flex flex-col items-center gap-2">
                    <CircleAlertIcon className="h-8 w-8 text-red-500" />
                    <div>
                      <p className="font-medium">Failed to load users</p>
                      <p className="text-sm text-muted-foreground">
                        {error.message || "An unknown error occurred"}
                      </p>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => refetch()}
                        className="mt-2"
                      >
                        Try again
                      </Button>
                    </div>
                  </div>
                </TableCell>
              </TableRow>
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className="last:py-0">
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  <div className="flex flex-col items-center gap-2 p-6">
                    <UserIcon className="h-8 w-8 text-muted-foreground" />
                    <div>
                      <p className="font-medium">No users found</p>
                      <p className="text-sm text-muted-foreground">
                        {searchQuery ||
                        roleFilter.length > 0 ||
                        statusFilter.length > 0
                          ? "Try adjusting your search or filters"
                          : "No users have been created yet"}
                      </p>
                    </div>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between gap-4">
        {/* Results per page */}
        <div className="flex items-center gap-2 sm:gap-3">
          <Label htmlFor={id} className="text-sm whitespace-nowrap">
            Rows per page
          </Label>
          <Select
            value={pagination.pageSize.toString()}
            onValueChange={(value) => {
              setPagination((prev) => ({
                ...prev,
                pageSize: Number(value),
                pageIndex: 0, // Reset to first page when changing page size
              }));
            }}
            disabled={isLoading}
          >
            <SelectTrigger
              id={id}
              className="w-fit whitespace-nowrap min-w-[4rem]"
            >
              <SelectValue placeholder="Select number of results" />
            </SelectTrigger>
            <SelectContent className="[&_*[role=option]]:ps-2 [&_*[role=option]]:pe-8 [&_*[role=option]>span]:start-auto [&_*[role=option]>span]:end-2">
              {[5, 10, 25, 50].map((pageSize) => (
                <SelectItem key={pageSize} value={pageSize.toString()}>
                  {pageSize}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Pagination controls */}
        <div className="flex items-center gap-4 sm:gap-8">
          {/* Page number information */}
          <div className="text-muted-foreground text-sm whitespace-nowrap">
            <p
              className="text-muted-foreground text-sm whitespace-nowrap"
              aria-live="polite"
            >
              {isLoading ? (
                "Loading..."
              ) : (
                <>
                  <span className="text-foreground">
                    {pagination.pageIndex * pagination.pageSize + 1}-
                    {Math.min(
                      (pagination.pageIndex + 1) * pagination.pageSize,
                      usersResponse?.data?.total || 0
                    )}
                  </span>{" "}
                  of{" "}
                  <span className="text-foreground">
                    {usersResponse?.data?.total || 0}
                  </span>
                </>
              )}
            </p>
          </div>

          {/* Pagination buttons */}
          <div>
            <Pagination>
              <PaginationContent className="gap-1">
                {/* First page button */}
                <PaginationItem>
                  <Button
                    size="icon"
                    variant="outline"
                    className="disabled:pointer-events-none disabled:opacity-50 h-8 w-8 sm:h-9 sm:w-9"
                    onClick={() =>
                      setPagination((prev) => ({ ...prev, pageIndex: 0 }))
                    }
                    disabled={pagination.pageIndex === 0 || isLoading}
                    aria-label="Go to first page"
                  >
                    <ChevronFirstIcon
                      size={14}
                      className="sm:size-4"
                      aria-hidden="true"
                    />
                  </Button>
                </PaginationItem>
                {/* Previous page button */}
                <PaginationItem>
                  <Button
                    size="icon"
                    variant="outline"
                    className="disabled:pointer-events-none disabled:opacity-50 h-8 w-8 sm:h-9 sm:w-9"
                    onClick={() =>
                      setPagination((prev) => ({
                        ...prev,
                        pageIndex: prev.pageIndex - 1,
                      }))
                    }
                    disabled={pagination.pageIndex === 0 || isLoading}
                    aria-label="Go to previous page"
                  >
                    <ChevronLeftIcon
                      size={14}
                      className="sm:size-4"
                      aria-hidden="true"
                    />
                  </Button>
                </PaginationItem>
                {/* Next page button */}
                <PaginationItem>
                  <Button
                    size="icon"
                    variant="outline"
                    className="disabled:pointer-events-none disabled:opacity-50 h-8 w-8 sm:h-9 sm:w-9"
                    onClick={() =>
                      setPagination((prev) => ({
                        ...prev,
                        pageIndex: prev.pageIndex + 1,
                      }))
                    }
                    disabled={
                      isLoading ||
                      !usersResponse?.data?.total ||
                      (pagination.pageIndex + 1) * pagination.pageSize >=
                        usersResponse.data.total
                    }
                    aria-label="Go to next page"
                  >
                    <ChevronRightIcon
                      size={14}
                      className="sm:size-4"
                      aria-hidden="true"
                    />
                  </Button>
                </PaginationItem>
                {/* Last page button */}
                <PaginationItem>
                  <Button
                    size="icon"
                    variant="outline"
                    className="disabled:pointer-events-none disabled:opacity-50 h-8 w-8 sm:h-9 sm:w-9"
                    onClick={() => {
                      const totalPages = Math.ceil(
                        (usersResponse?.data?.total || 0) / pagination.pageSize
                      );
                      setPagination((prev) => ({
                        ...prev,
                        pageIndex: totalPages - 1,
                      }));
                    }}
                    disabled={
                      isLoading ||
                      !usersResponse?.data?.total ||
                      (pagination.pageIndex + 1) * pagination.pageSize >=
                        usersResponse.data.total
                    }
                    aria-label="Go to last page"
                  >
                    <ChevronLastIcon
                      size={14}
                      className="sm:size-4"
                      aria-hidden="true"
                    />
                  </Button>
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        </div>
      </div>
    </div>
  );
}
