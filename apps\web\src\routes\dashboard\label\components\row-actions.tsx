import type { Row } from "@tanstack/react-table";
import {
  EllipsisIcon,
  TrashIcon,
  CircleAlertIcon,
  EditIcon,
} from "lucide-react";
import { toast } from "sonner";
import { useMutation } from "@tanstack/react-query";

import { useTRPCClient } from "@/utils/trpc";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { UpdateLabelDialog } from "@/routes/dashboard/label/components/update-label-dialog";
import type { LabelTableItem } from "@/routes/dashboard/label/components/label-table";

interface RowActionsProps {
  row: Row<LabelTableItem>;
  onLabelDeleted: () => void;
}

export function RowActions({ row, onLabelDeleted }: RowActionsProps) {
  const trpcClient = useTRPCClient();

  const deleteLabelMutation = useMutation({
    mutationFn: async () => {
      return trpcClient.label.delete.mutate({
        id: row.original.id,
      });
    },
    onSuccess: () => {
      toast.success(`Successfully deleted label ${row.original.name}`);
      onLabelDeleted();
    },
    onError: (error: any) => {
      console.error("Failed to delete label:", error);
      toast.error(
        "Failed to delete label: " + (error.message || "Unknown error")
      );
    },
  });

  const handleDeleteLabel = async () => {
    try {
      await deleteLabelMutation.mutateAsync();
    } catch (error) {
      // Error handling is done in the mutation
    }
  };

  const artistCount = row.original.artists?.length || 0;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="flex justify-end">
          <Button
            size="icon"
            variant="ghost"
            className="shadow-none cursor-pointer"
            aria-label="Edit item"
          >
            <EllipsisIcon size={16} aria-hidden="true" />
          </Button>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuGroup>
          <UpdateLabelDialog
            label={row.original}
            onLabelUpdated={onLabelDeleted}
          >
            <DropdownMenuItem
              onSelect={(e) => e.preventDefault()}
              className="cursor-pointer"
            >
              <EditIcon className="h-4 w-4" />
              <span>Edit Label</span>
            </DropdownMenuItem>
          </UpdateLabelDialog>
        </DropdownMenuGroup>
        <DropdownMenuSeparator className="h-[2px]" />
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <DropdownMenuItem
              className="text-destructive focus:text-destructive cursor-pointer"
              onSelect={(e) => e.preventDefault()}
            >
              <div className="flex items-center gap-2">
                <TrashIcon
                  size={16}
                  className="text-destructive focus:text-destructive"
                />
                <span>Delete</span>
              </div>
            </DropdownMenuItem>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <div className="flex flex-col gap-2 max-sm:items-center sm:flex-row sm:gap-4">
              <div
                className="flex size-9 shrink-0 items-center justify-center rounded-full border"
                aria-hidden="true"
              >
                <CircleAlertIcon className="opacity-80" size={16} />
              </div>
              <AlertDialogHeader>
                <AlertDialogTitle>Delete Label</AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to delete{" "}
                  <strong>{row.original.name}</strong>?{" "}
                  {artistCount > 0 && (
                    <>
                      This label has{" "}
                      <strong>
                        {artistCount} artist{artistCount !== 1 ? "s" : ""}
                      </strong>{" "}
                      associated with it. These artists will be unassigned from
                      the label but will remain in the system.
                    </>
                  )}
                  This action cannot be undone and will permanently remove the
                  label.
                </AlertDialogDescription>
              </AlertDialogHeader>
            </div>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDeleteLabel}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                disabled={deleteLabelMutation.isPending}
              >
                {deleteLabelMutation.isPending ? "Deleting..." : "Delete Label"}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
