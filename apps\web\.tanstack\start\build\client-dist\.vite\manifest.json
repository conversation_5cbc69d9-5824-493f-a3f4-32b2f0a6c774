{"../../../../../~start/default-client-entry.tsx": {"file": "assets/main-B9Fv5CdX.js", "name": "main", "src": "../../../../../~start/default-client-entry.tsx", "isEntry": true, "dynamicImports": ["src/routes/reset-password.tsx?tsr-split=component", "src/routes/change-email.tsx?tsr-split=component", "src/routes/dashboard/route.tsx?tsr-split=component", "src/routes/auth/route.tsx?tsr-split=component", "src/routes/index.tsx?tsr-split=component", "src/routes/dashboard/index.tsx?tsr-split=component", "src/routes/auth/index.tsx?tsr-split=component", "src/routes/dashboard/user-management/index.tsx?tsr-split=component", "src/routes/dashboard/track/index.tsx?tsr-split=component", "src/routes/dashboard/profile/index.tsx?tsr-split=component", "src/routes/dashboard/label/index.tsx?tsr-split=component", "src/routes/dashboard/contributor/index.tsx?tsr-split=component", "src/routes/dashboard/artist/index.tsx?tsr-split=component", "src/routes/dashboard/track/$id.tsx?tsr-split=component", "src/routes/dashboard/artist/$id.tsx?tsr-split=component"], "css": ["assets/index-Co2ygbN2.css"], "assets": ["assets/index-Co2ygbN2.css"]}, "C:/Users/<USER>/soundmera-tan/apps/web/src/index.css": {"file": "assets/index-Co2ygbN2.css", "src": "C:/Users/<USER>/soundmera-tan/apps/web/src/index.css"}, "_auth-client-C8WifPV2.js": {"file": "assets/auth-client-C8WifPV2.js", "name": "auth-client", "imports": ["../../../../../~start/default-client-entry.tsx"]}, "_badge-B7y-QlNI.js": {"file": "assets/badge-B7y-QlNI.js", "name": "badge", "imports": ["../../../../../~start/default-client-entry.tsx", "_button-Ispz1G12.js"]}, "_button-Ispz1G12.js": {"file": "assets/button-Ispz1G12.js", "name": "button", "imports": ["../../../../../~start/default-client-entry.tsx"]}, "_card-PyhbSuya.js": {"file": "assets/card-PyhbSuya.js", "name": "card", "imports": ["../../../../../~start/default-client-entry.tsx", "_button-Ispz1G12.js"]}, "_checkbox-Dk7Edl6C.js": {"file": "assets/checkbox-Dk7Edl6C.js", "name": "checkbox", "imports": ["../../../../../~start/default-client-entry.tsx", "_button-Ispz1G12.js", "_select-Cv6EF9My.js", "_dialog-iGlJJq5Q.js", "_label-CNQvdrLZ.js"]}, "_circle-alert-DwqW4ucM.js": {"file": "assets/circle-alert-DwqW4ucM.js", "name": "circle-alert", "imports": ["../../../../../~start/default-client-entry.tsx"]}, "_circle-check-big-BA7hvIno.js": {"file": "assets/circle-check-big-BA7hvIno.js", "name": "circle-check-big", "imports": ["../../../../../~start/default-client-entry.tsx"]}, "_circle-x-Dhf4rEsb.js": {"file": "assets/circle-x-Dhf4rEsb.js", "name": "circle-x", "imports": ["../../../../../~start/default-client-entry.tsx"]}, "_clock-BWWmp-9w.js": {"file": "assets/clock-BWWmp-9w.js", "name": "clock", "imports": ["../../../../../~start/default-client-entry.tsx"]}, "_country-dropdown-DE40VcIC.js": {"file": "assets/country-dropdown-DE40VcIC.js", "name": "country-dropdown", "imports": ["../../../../../~start/default-client-entry.tsx", "_genre-select-BveQTmBa.js", "_popover-DzeimUGg.js", "_button-Ispz1G12.js", "_select-Cv6EF9My.js"]}, "_create-artist-dialog-UMieFrvC.js": {"file": "assets/create-artist-dialog-UMieFrvC.js", "name": "create-artist-dialog", "imports": ["../../../../../~start/default-client-entry.tsx", "_useQuery-CFAncLHa.js", "_useMutation-DGkS69KN.js", "_auth-client-C8WifPV2.js", "_dialog-iGlJJq5Q.js", "_button-Ispz1G12.js", "_label-CNQvdrLZ.js", "_genre-select-BveQTmBa.js", "_select-Cv6EF9My.js", "_checkbox-Dk7Edl6C.js", "_country-dropdown-DE40VcIC.js"]}, "_create-contributor-dialog-bHaIXYGw.js": {"file": "assets/create-contributor-dialog-bHaIXYGw.js", "name": "create-contributor-dialog", "imports": ["../../../../../~start/default-client-entry.tsx", "_useMutation-DGkS69KN.js", "_auth-client-C8WifPV2.js", "_dialog-iGlJJq5Q.js", "_button-Ispz1G12.js", "_label-CNQvdrLZ.js"]}, "_create-label-dialog-CPutPDCb.js": {"file": "assets/create-label-dialog-CPutPDCb.js", "name": "create-label-dialog", "imports": ["../../../../../~start/default-client-entry.tsx", "_useMutation-DGkS69KN.js", "_auth-client-C8WifPV2.js", "_dialog-iGlJJq5Q.js", "_button-Ispz1G12.js", "_label-CNQvdrLZ.js"]}, "_dialog-iGlJJq5Q.js": {"file": "assets/dialog-iGlJJq5Q.js", "name": "dialog", "imports": ["../../../../../~start/default-client-entry.tsx", "_button-Ispz1G12.js", "_label-CNQvdrLZ.js"]}, "_dropdown-menu-DKdrXVD1.js": {"file": "assets/dropdown-menu-DKdrXVD1.js", "name": "dropdown-menu", "imports": ["../../../../../~start/default-client-entry.tsx", "_dialog-iGlJJq5Q.js", "_button-Ispz1G12.js", "_label-CNQvdrLZ.js", "_select-Cv6EF9My.js", "_scroll-area-BkIMWkxZ.js"]}, "_eye-C2utTrD7.js": {"file": "assets/eye-C2utTrD7.js", "name": "eye", "imports": ["../../../../../~start/default-client-entry.tsx"]}, "_file-text-BuKzmpXw.js": {"file": "assets/file-text-BuKzmpXw.js", "name": "file-text", "imports": ["../../../../../~start/default-client-entry.tsx"]}, "_filter-DkLeLFHN.js": {"file": "assets/filter-DkLeLFHN.js", "name": "filter", "imports": ["../../../../../~start/default-client-entry.tsx"]}, "_genre-select-BveQTmBa.js": {"file": "assets/genre-select-BveQTmBa.js", "name": "genre-select", "imports": ["../../../../../~start/default-client-entry.tsx", "_button-Ispz1G12.js", "_dialog-iGlJJq5Q.js", "_label-CNQvdrLZ.js", "_popover-DzeimUGg.js", "_select-Cv6EF9My.js"]}, "_label-CNQvdrLZ.js": {"file": "assets/label-CNQvdrLZ.js", "name": "label", "imports": ["../../../../../~start/default-client-entry.tsx", "_button-Ispz1G12.js"]}, "_list-filter-BY1QXxSs.js": {"file": "assets/list-filter-BY1QXxSs.js", "name": "list-filter", "imports": ["../../../../../~start/default-client-entry.tsx"]}, "_mail-9LfZMTcO.js": {"file": "assets/mail-9LfZMTcO.js", "name": "mail", "imports": ["../../../../../~start/default-client-entry.tsx"]}, "_platform-badge-Dh7vRBtY.js": {"file": "assets/platform-badge-Dh7vRBtY.js", "name": "platform-badge", "imports": ["../../../../../~start/default-client-entry.tsx", "_useQuery-CFAncLHa.js", "_useMutation-DGkS69KN.js", "_auth-client-C8WifPV2.js", "_dialog-iGlJJq5Q.js", "_button-Ispz1G12.js", "_label-CNQvdrLZ.js", "_genre-select-BveQTmBa.js", "_select-Cv6EF9My.js", "_checkbox-Dk7Edl6C.js", "_country-dropdown-DE40VcIC.js", "_badge-B7y-QlNI.js"]}, "_popover-DzeimUGg.js": {"file": "assets/popover-DzeimUGg.js", "name": "popover", "imports": ["../../../../../~start/default-client-entry.tsx", "_button-Ispz1G12.js", "_dialog-iGlJJq5Q.js", "_select-Cv6EF9My.js", "_label-CNQvdrLZ.js"]}, "_scroll-area-BkIMWkxZ.js": {"file": "assets/scroll-area-BkIMWkxZ.js", "name": "scroll-area", "imports": ["../../../../../~start/default-client-entry.tsx", "_dialog-iGlJJq5Q.js", "_button-Ispz1G12.js", "_label-CNQvdrLZ.js"]}, "_select-Cv6EF9My.js": {"file": "assets/select-Cv6EF9My.js", "name": "select", "imports": ["../../../../../~start/default-client-entry.tsx", "_button-Ispz1G12.js", "_dialog-iGlJJq5Q.js", "_label-CNQvdrLZ.js"]}, "_send-DtqpyuMn.js": {"file": "assets/send-DtqpyuMn.js", "name": "send", "imports": ["../../../../../~start/default-client-entry.tsx"]}, "_separator-CMLPp7Z2.js": {"file": "assets/separator-CMLPp7Z2.js", "name": "separator", "imports": ["../../../../../~start/default-client-entry.tsx", "_label-CNQvdrLZ.js", "_button-Ispz1G12.js"]}, "_skeleton-BimQrrV-.js": {"file": "assets/skeleton-BimQrrV-.js", "name": "skeleton", "imports": ["../../../../../~start/default-client-entry.tsx", "_button-Ispz1G12.js"]}, "_square-pen-DzJw3_Uf.js": {"file": "assets/square-pen-DzJw3_Uf.js", "name": "square-pen", "imports": ["../../../../../~start/default-client-entry.tsx"]}, "_table-gaCg9Yy-.js": {"file": "assets/table-gaCg9Yy-.js", "name": "table", "imports": ["../../../../../~start/default-client-entry.tsx", "_button-Ispz1G12.js", "_dialog-iGlJJq5Q.js", "_scroll-area-BkIMWkxZ.js"]}, "_track-status-dialog-BIHBsjot.js": {"file": "assets/track-status-dialog-BIHBsjot.js", "name": "track-status-dialog", "imports": ["../../../../../~start/default-client-entry.tsx", "_useMutation-DGkS69KN.js", "_auth-client-C8WifPV2.js", "_dialog-iGlJJq5Q.js", "_button-Ispz1G12.js", "_label-CNQvdrLZ.js", "_genre-select-BveQTmBa.js", "_select-Cv6EF9My.js", "_card-PyhbSuya.js", "_circle-alert-DwqW4ucM.js", "_useQuery-CFAncLHa.js", "_popover-DzeimUGg.js", "_badge-B7y-QlNI.js", "_user-DCDNZ7An.js", "_users-uEpTONEC.js", "_circle-check-big-BA7hvIno.js", "_clock-BWWmp-9w.js"]}, "_triangle-alert-BretsIXJ.js": {"file": "assets/triangle-alert-BretsIXJ.js", "name": "triangle-alert", "imports": ["../../../../../~start/default-client-entry.tsx"]}, "_types-BF4s_UBG.js": {"file": "assets/types-BF4s_UBG.js", "name": "types"}, "_useForm-BIMU_iAr.js": {"file": "assets/useForm-BIMU_iAr.js", "name": "useForm", "imports": ["../../../../../~start/default-client-entry.tsx"]}, "_useMutation-DGkS69KN.js": {"file": "assets/useMutation-DGkS69KN.js", "name": "useMutation", "imports": ["../../../../../~start/default-client-entry.tsx"]}, "_useQuery-CFAncLHa.js": {"file": "assets/useQuery-CFAncLHa.js", "name": "useQuery", "imports": ["../../../../../~start/default-client-entry.tsx"]}, "_user-DCDNZ7An.js": {"file": "assets/user-DCDNZ7An.js", "name": "user", "imports": ["../../../../../~start/default-client-entry.tsx"]}, "_users-uEpTONEC.js": {"file": "assets/users-uEpTONEC.js", "name": "users", "imports": ["../../../../../~start/default-client-entry.tsx"]}, "src/routes/auth/index.tsx?tsr-split=component": {"file": "assets/index-BOXNRTjJ.js", "name": "index", "src": "src/routes/auth/index.tsx?tsr-split=component", "isDynamicEntry": true, "imports": ["../../../../../~start/default-client-entry.tsx", "_auth-client-C8WifPV2.js", "_useForm-BIMU_iAr.js", "_button-Ispz1G12.js", "_label-CNQvdrLZ.js", "_card-PyhbSuya.js", "_useMutation-DGkS69KN.js"]}, "src/routes/auth/route.tsx?tsr-split=component": {"file": "assets/route-D_bS8w9S.js", "name": "route", "src": "src/routes/auth/route.tsx?tsr-split=component", "isDynamicEntry": true, "imports": ["../../../../../~start/default-client-entry.tsx"]}, "src/routes/change-email.tsx?tsr-split=component": {"file": "assets/change-email-C5EoiFUu.js", "name": "change-email", "src": "src/routes/change-email.tsx?tsr-split=component", "isDynamicEntry": true, "imports": ["../../../../../~start/default-client-entry.tsx", "_useMutation-DGkS69KN.js", "_card-PyhbSuya.js", "_button-Ispz1G12.js", "_circle-check-big-BA7hvIno.js", "_mail-9LfZMTcO.js", "_circle-x-Dhf4rEsb.js"]}, "src/routes/dashboard/artist/$id.tsx?tsr-split=component": {"file": "assets/_id-C4mzxEFc.js", "name": "_id", "src": "src/routes/dashboard/artist/$id.tsx?tsr-split=component", "isDynamicEntry": true, "imports": ["../../../../../~start/default-client-entry.tsx", "_useQuery-CFAncLHa.js", "_auth-client-C8WifPV2.js", "_button-Ispz1G12.js", "_card-PyhbSuya.js", "_platform-badge-Dh7vRBtY.js", "_genre-select-BveQTmBa.js", "_file-text-BuKzmpXw.js", "_square-pen-DzJw3_Uf.js", "_user-DCDNZ7An.js", "_mail-9LfZMTcO.js", "_useMutation-DGkS69KN.js", "_dialog-iGlJJq5Q.js", "_label-CNQvdrLZ.js", "_select-Cv6EF9My.js", "_checkbox-Dk7Edl6C.js", "_country-dropdown-DE40VcIC.js", "_popover-DzeimUGg.js", "_badge-B7y-QlNI.js"]}, "src/routes/dashboard/artist/index.tsx?tsr-split=component": {"file": "assets/index-DvyVyBMD.js", "name": "index", "src": "src/routes/dashboard/artist/index.tsx?tsr-split=component", "isDynamicEntry": true, "imports": ["../../../../../~start/default-client-entry.tsx", "_auth-client-C8WifPV2.js", "_table-gaCg9Yy-.js", "_useQuery-CFAncLHa.js", "_useMutation-DGkS69KN.js", "_button-Ispz1G12.js", "_checkbox-Dk7Edl6C.js", "_dropdown-menu-DKdrXVD1.js", "_label-CNQvdrLZ.js", "_popover-DzeimUGg.js", "_select-Cv6EF9My.js", "_create-artist-dialog-UMieFrvC.js", "_badge-B7y-QlNI.js", "_platform-badge-Dh7vRBtY.js", "_eye-C2utTrD7.js", "_square-pen-DzJw3_Uf.js", "_circle-alert-DwqW4ucM.js", "_genre-select-BveQTmBa.js", "_list-filter-BY1QXxSs.js", "_circle-x-Dhf4rEsb.js", "_filter-DkLeLFHN.js", "_dialog-iGlJJq5Q.js", "_scroll-area-BkIMWkxZ.js", "_country-dropdown-DE40VcIC.js"]}, "src/routes/dashboard/contributor/index.tsx?tsr-split=component": {"file": "assets/index-B9BrU5vR.js", "name": "index", "src": "src/routes/dashboard/contributor/index.tsx?tsr-split=component", "isDynamicEntry": true, "imports": ["../../../../../~start/default-client-entry.tsx", "_auth-client-C8WifPV2.js", "_table-gaCg9Yy-.js", "_useQuery-CFAncLHa.js", "_useMutation-DGkS69KN.js", "_button-Ispz1G12.js", "_dropdown-menu-DKdrXVD1.js", "_label-CNQvdrLZ.js", "_select-Cv6EF9My.js", "_create-contributor-dialog-bHaIXYGw.js", "_checkbox-Dk7Edl6C.js", "_dialog-iGlJJq5Q.js", "_square-pen-DzJw3_Uf.js", "_circle-alert-DwqW4ucM.js", "_list-filter-BY1QXxSs.js", "_circle-x-Dhf4rEsb.js", "_users-uEpTONEC.js", "_scroll-area-BkIMWkxZ.js"]}, "src/routes/dashboard/index.tsx?tsr-split=component": {"file": "assets/index-DJz4KF2e.js", "name": "index", "src": "src/routes/dashboard/index.tsx?tsr-split=component", "isDynamicEntry": true, "imports": ["../../../../../~start/default-client-entry.tsx", "_auth-client-C8WifPV2.js", "_useQuery-CFAncLHa.js"]}, "src/routes/dashboard/label/index.tsx?tsr-split=component": {"file": "assets/index-Bljhms3H.js", "name": "index", "src": "src/routes/dashboard/label/index.tsx?tsr-split=component", "isDynamicEntry": true, "imports": ["../../../../../~start/default-client-entry.tsx", "_auth-client-C8WifPV2.js", "_table-gaCg9Yy-.js", "_useQuery-CFAncLHa.js", "_useMutation-DGkS69KN.js", "_button-Ispz1G12.js", "_dropdown-menu-DKdrXVD1.js", "_label-CNQvdrLZ.js", "_select-Cv6EF9My.js", "_create-label-dialog-CPutPDCb.js", "_checkbox-Dk7Edl6C.js", "_dialog-iGlJJq5Q.js", "_square-pen-DzJw3_Uf.js", "_circle-alert-DwqW4ucM.js", "_list-filter-BY1QXxSs.js", "_circle-x-Dhf4rEsb.js", "_scroll-area-BkIMWkxZ.js"]}, "src/routes/dashboard/profile/index.tsx?tsr-split=component": {"file": "assets/index-BCSoR6Mn.js", "name": "index", "src": "src/routes/dashboard/profile/index.tsx?tsr-split=component", "isDynamicEntry": true, "imports": ["../../../../../~start/default-client-entry.tsx", "_scroll-area-BkIMWkxZ.js", "_button-Ispz1G12.js", "_dialog-iGlJJq5Q.js", "_label-CNQvdrLZ.js", "_separator-CMLPp7Z2.js", "_useForm-BIMU_iAr.js", "_auth-client-C8WifPV2.js", "_useMutation-DGkS69KN.js", "_send-DtqpyuMn.js", "_triangle-alert-BretsIXJ.js", "_types-BF4s_UBG.js", "_mail-9LfZMTcO.js"]}, "src/routes/dashboard/route.tsx?tsr-split=component": {"file": "assets/route-jY0Wtmtu.js", "name": "route", "src": "src/routes/dashboard/route.tsx?tsr-split=component", "isDynamicEntry": true, "imports": ["../../../../../~start/default-client-entry.tsx", "_button-Ispz1G12.js", "_dialog-iGlJJq5Q.js", "_select-Cv6EF9My.js", "_label-CNQvdrLZ.js", "_scroll-area-BkIMWkxZ.js", "_dropdown-menu-DKdrXVD1.js", "_auth-client-C8WifPV2.js", "_skeleton-BimQrrV-.js", "_create-artist-dialog-UMieFrvC.js", "_create-label-dialog-CPutPDCb.js", "_create-contributor-dialog-bHaIXYGw.js", "_send-DtqpyuMn.js", "_genre-select-BveQTmBa.js", "_users-uEpTONEC.js", "_separator-CMLPp7Z2.js", "_useQuery-CFAncLHa.js", "_useMutation-DGkS69KN.js", "_checkbox-Dk7Edl6C.js", "_country-dropdown-DE40VcIC.js", "_popover-DzeimUGg.js"]}, "src/routes/dashboard/track/$id.tsx?tsr-split=component": {"file": "assets/_id-chqZCvk2.js", "name": "_id", "src": "src/routes/dashboard/track/$id.tsx?tsr-split=component", "isDynamicEntry": true, "imports": ["../../../../../~start/default-client-entry.tsx", "_useQuery-CFAncLHa.js", "_auth-client-C8WifPV2.js", "_button-Ispz1G12.js", "_card-PyhbSuya.js", "_badge-B7y-QlNI.js", "_separator-CMLPp7Z2.js", "_track-status-dialog-BIHBsjot.js", "_genre-select-BveQTmBa.js", "_file-text-BuKzmpXw.js", "_square-pen-DzJw3_Uf.js", "_user-DCDNZ7An.js", "_users-uEpTONEC.js", "_label-CNQvdrLZ.js", "_useMutation-DGkS69KN.js", "_dialog-iGlJJq5Q.js", "_select-Cv6EF9My.js", "_circle-alert-DwqW4ucM.js", "_popover-DzeimUGg.js", "_circle-check-big-BA7hvIno.js", "_clock-BWWmp-9w.js"]}, "src/routes/dashboard/track/index.tsx?tsr-split=component": {"file": "assets/index-DF0fuaGH.js", "name": "index", "src": "src/routes/dashboard/track/index.tsx?tsr-split=component", "isDynamicEntry": true, "imports": ["../../../../../~start/default-client-entry.tsx", "_auth-client-C8WifPV2.js", "_table-gaCg9Yy-.js", "_useQuery-CFAncLHa.js", "_useMutation-DGkS69KN.js", "_button-Ispz1G12.js", "_dropdown-menu-DKdrXVD1.js", "_label-CNQvdrLZ.js", "_select-Cv6EF9My.js", "_dialog-iGlJJq5Q.js", "_genre-select-BveQTmBa.js", "_track-status-dialog-BIHBsjot.js", "_checkbox-Dk7Edl6C.js", "_badge-B7y-QlNI.js", "_eye-C2utTrD7.js", "_square-pen-DzJw3_Uf.js", "_circle-alert-DwqW4ucM.js", "_circle-x-Dhf4rEsb.js", "_scroll-area-BkIMWkxZ.js", "_popover-DzeimUGg.js", "_card-PyhbSuya.js", "_user-DCDNZ7An.js", "_users-uEpTONEC.js", "_circle-check-big-BA7hvIno.js", "_clock-BWWmp-9w.js"]}, "src/routes/dashboard/user-management/index.tsx?tsr-split=component": {"file": "assets/index-DkezRj7Q.js", "name": "index", "src": "src/routes/dashboard/user-management/index.tsx?tsr-split=component", "isDynamicEntry": true, "imports": ["../../../../../~start/default-client-entry.tsx", "_auth-client-C8WifPV2.js", "_table-gaCg9Yy-.js", "_useQuery-CFAncLHa.js", "_button-Ispz1G12.js", "_checkbox-Dk7Edl6C.js", "_dropdown-menu-DKdrXVD1.js", "_label-CNQvdrLZ.js", "_popover-DzeimUGg.js", "_select-Cv6EF9My.js", "_dialog-iGlJJq5Q.js", "_badge-B7y-QlNI.js", "_circle-check-big-BA7hvIno.js", "_circle-x-Dhf4rEsb.js", "_user-DCDNZ7An.js", "_circle-alert-DwqW4ucM.js", "_list-filter-BY1QXxSs.js", "_filter-DkLeLFHN.js", "_card-PyhbSuya.js", "_skeleton-BimQrrV-.js", "_triangle-alert-BretsIXJ.js", "_users-uEpTONEC.js", "_clock-BWWmp-9w.js", "_scroll-area-BkIMWkxZ.js"]}, "src/routes/index.tsx?tsr-split=component": {"file": "assets/index-BA-4xiZ7.js", "name": "index", "src": "src/routes/index.tsx?tsr-split=component", "isDynamicEntry": true, "imports": ["../../../../../~start/default-client-entry.tsx", "_useQuery-CFAncLHa.js"]}, "src/routes/reset-password.tsx?tsr-split=component": {"file": "assets/reset-password-DMNgKCpM.js", "name": "reset-password", "src": "src/routes/reset-password.tsx?tsr-split=component", "isDynamicEntry": true, "imports": ["../../../../../~start/default-client-entry.tsx", "_useForm-BIMU_iAr.js", "_button-Ispz1G12.js", "_label-CNQvdrLZ.js", "_card-PyhbSuya.js", "_useMutation-DGkS69KN.js", "_circle-check-big-BA7hvIno.js", "_circle-x-Dhf4rEsb.js", "_types-BF4s_UBG.js"]}}