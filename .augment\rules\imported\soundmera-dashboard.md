---
type: "always_apply"
---

# Soundmera Music Distribution Dashboard Rules

## Project Overview
Soundmera is a private music distribution dashboard with role-based access control. Only logged-in users can access the app - no public registration is allowed. Admins create individual user accounts.

## Core Architecture

### Authentication & Authorization
- **Private Access Only**: No public sign-up functionality
- **Admin-Created Users**: Only admins can create new user accounts
- **Two User Roles**:
  - `artist`: Can manage their music content and submit releases
  - `admin`: Full system access including user management and release approval

### Database Schema Considerations
```
Users: id, email, password, role (artist|admin), created_by_admin_id, status, created_at
Artists: id, user_id, name, bio, image, social_links, created_at
Tracks: id, artist_id, title, duration, file_path, metadata, created_at
Writers: id, name, role, splits_percentage, created_at
Releases: id, artist_id, title, release_date, status (draft|pending|approved|rejected), type, artwork, created_at
ReleaseTracks: release_id, track_id, track_order
ReleaseWriters: release_id, track_id, writer_id
UpdateRequests: id, release_id, request_type (update|takedown), status (pending|approved|rejected), reason, created_at
```

## Artist Role Capabilities

### Content Management
- **Artist Profiles**: Add, update, delete artist information
- **Track Management**: Upload, edit, and delete tracks with metadata
- **Writer Management**: Add, update, delete writer information and splits

### Release Management
- **Create Releases**: Bundle tracks into releases (albums, EPs, singles)
- **Release Submission**: Submit releases for admin approval
- **Update/Takedown Requests**: 
  - Only available for approved releases
  - Requires admin approval for both update and takedown requests
  - Should include reason/justification field

## Admin Role Capabilities

### User Management
- **Create Users**: Generate artist accounts with temporary passwords
- **User Status**: Enable/disable user accounts
- **Role Management**: Assign and modify user roles

### Release Approval Workflow
- **Review Releases**: View pending releases with all details
- **Approve/Reject**: Make decisions on release submissions
- **Bulk Actions**: Handle multiple releases efficiently

### Request Management
- **Update Requests**: Approve/deny artist requests to update approved releases
- **Takedown Requests**: Handle removal requests for live releases
- **Request History**: Track all approval/rejection decisions

## Technical Implementation Guidelines

### Frontend Architecture
- Use **shadcn/ui components** for consistent UI design
- Implement **role-based route protection**
- **Responsive design** for dashboard layouts
- **Form validation** for all content submissions

### State Management
- User authentication state
- Role-based permissions
- Release workflow states
- Real-time notifications for admins

### API Design Patterns
```
Authentication:
POST /api/auth/login
POST /api/auth/logout
GET /api/auth/me

Artist Management:
GET /api/artists
POST /api/artists
PUT /api/artists/:id
DELETE /api/artists/:id

Release Workflow:
POST /api/releases (create draft)
PUT /api/releases/:id/submit (submit for approval)
PUT /api/releases/:id/approve (admin only)
PUT /api/releases/:id/reject (admin only)
POST /api/releases/:id/request-update (artist only)
POST /api/releases/:id/request-takedown (artist only)
```

### Security Considerations
- **JWT Authentication** with role-based claims
- **Input Validation** for all form submissions
- **File Upload Security** for tracks and artwork
- **Rate Limiting** on API endpoints
- **Audit Logging** for admin actions

## UI/UX Patterns

### Dashboard Layout
- **Sidebar Navigation** with role-specific menu items
- **Status Indicators** for releases (draft, pending, approved, rejected)
- **Progress Tracking** for multi-step release creation
- **Notification System** for request updates

### Artist Interface
- **Content Library** view for tracks and releases
- **Release Builder** with drag-and-drop track ordering
- **Request Status** tracking with timeline view

### Admin Interface
- **User Management** table with search and filters
- **Release Queue** with priority sorting
- **Request Dashboard** for update/takedown approvals
- **Analytics Dashboard** with key metrics

## File Organization
```
/components
  /auth - Authentication components
  /artist - Artist-specific components
  /admin - Admin-specific components
  /shared - Common UI components
  /forms - Form components with validation
/pages
  /api - API routes
  /artist - Artist dashboard pages
  /admin - Admin dashboard pages
  /auth - Login/logout pages
/lib
  /auth - Authentication utilities
  /db - Database connection and models
  /validations - Form validation schemas
/hooks - Custom React hooks for data fetching
/types - TypeScript type definitions
```

## Development Workflow
1. **Database Setup**: Create tables with proper relationships
2. **Authentication**: Implement login-only access
3. **Role System**: Build role-based route protection
4. **Artist Features**: Content management functionality
5. **Admin Features**: User management and approval workflows
6. **Request System**: Update/takedown request handling
7. **Testing**: Unit and integration tests for critical paths

## Key Features Priority
1. User authentication and role management
2. Artist profile and content management
3. Release creation and submission workflow
4. Admin approval system
5. Update/takedown request system
6. Notification system
7. Analytics and reporting

