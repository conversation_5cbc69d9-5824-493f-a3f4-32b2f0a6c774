import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { useTRPCClient } from "@/utils/trpc";
import { useMutation } from "@tanstack/react-query";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { CheckCircle, XCircle, Loader2, Mail } from "lucide-react";
import { Button } from "@/components/ui/button";

export const Route = createFileRoute("/change-email")({
  component: ChangeEmailComponent,
  validateSearch: (search: Record<string, unknown>) => {
    return {
      token: (search.token as string) || "",
    };
  },
});

function ChangeEmailComponent() {
  const navigate = useNavigate();
  const { token } = Route.useSearch();
  const trpcClient = useTRPCClient();
  const [status, setStatus] = useState<"loading" | "success" | "error">(
    "loading"
  );
  const [errorMessage, setErrorMessage] = useState("");

  const verifyEmailChangeMutation = useMutation({
    mutationFn: (token: string) =>
      trpcClient.email.verifyEmailChange.mutate({ token }),
    onSuccess: () => {
      setStatus("success");
      toast.success("Email address changed successfully!");

      // Redirect to dashboard after a short delay
      setTimeout(() => {
        navigate({ to: "/dashboard" });
      }, 3000);
    },
    onError: (error: any) => {
      setStatus("error");
      setErrorMessage(
        error.message ||
          "Failed to change email address. The link may be expired or invalid."
      );
      toast.error("Email change failed");
    },
  });

  useEffect(() => {
    if (!token) {
      setStatus("error");
      setErrorMessage("Invalid email change link. No token provided.");
      return;
    }

    verifyEmailChangeMutation.mutate(token);
  }, [token]);

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
              <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
            </div>
            <CardTitle>Verifying Email Change</CardTitle>
            <CardDescription>
              Please wait while we verify your email change request...
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  if (status === "success") {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <CardTitle className="text-green-900">
              Email Changed Successfully!
            </CardTitle>
            <CardDescription>
              Your email address has been updated successfully. You will be
              redirected to your dashboard shortly.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <Button
              onClick={() => navigate({ to: "/dashboard" })}
              className="w-full"
            >
              <Mail className="h-4 w-4" />
              Go to Dashboard
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
            <XCircle className="h-6 w-6 text-red-600" />
          </div>
          <CardTitle className="text-red-900">Email Change Failed</CardTitle>
          <CardDescription className="text-red-700">
            {errorMessage}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center text-sm text-muted-foreground">
            <p>Possible reasons for failure:</p>
            <ul className="mt-2 text-left space-y-1">
              <li>• The verification link has expired</li>
              <li>• The link has already been used</li>
              <li>• The link is invalid or corrupted</li>
            </ul>
          </div>

          <div className="flex flex-col space-y-2">
            <Button
              onClick={() => navigate({ to: "/dashboard/profile" })}
              className="w-full"
            >
              <Mail className="h-4 w-4" />
              Try Again from Profile
            </Button>
            <Button
              variant="outline"
              onClick={() => navigate({ to: "/dashboard" })}
              className="w-full"
            >
              Back to Dashboard
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
