import Loader from "@/components/loader";
import { authClient } from "@/lib/auth-client";
import UserTable from "./components/user-table";
import UserSummaryCards from "./components/user-summary-cards";
import { createFileRoute } from "@tanstack/react-router";
import { useEffect } from "react";
import { toast } from "sonner";

export const Route = createFileRoute("/dashboard/user-management/")({
  component: RouteComponent,
  head: () => ({
    meta: [
      {
        title: "User Management - Soundmera App",
      }
    ]
  })
});

function RouteComponent() {
  const navigate = Route.useNavigate();
  const { data: session, isPending } = authClient.useSession();

  useEffect(() => {
    if (!session && !isPending) {
      navigate({
        to: "/auth",
      });
      return;
    }

    // Check if user has admin role
    if (session && session.user && session.user.role !== "admin") {
      toast.error("Access denied. Admin role required.");
      navigate({
        to: "/dashboard",
      });
      return;
    }
  }, [session, isPending, navigate]);

  if (isPending) {
    return <Loader />;
  }

  // Additional check to prevent rendering if not admin
  if (!session || !session.user || session.user.role !== "admin") {
    return <Loader />;
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="pl-2 text-xl font-bold">User Management</div>
      <div className="p-2">
        <UserSummaryCards />
      </div>
      <div className="p-2">
        <UserTable />
      </div>
    </div>
  );
}
