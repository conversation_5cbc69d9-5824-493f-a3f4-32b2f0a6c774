
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file should be your main import to use Prisma. Through it you get access to all the models, enums, and input types.
 *
 * 🟢 You can import this file directly.
 */

import * as process from 'node:process'
import * as path from 'node:path'
import { fileURLToPath } from 'node:url'
const __dirname = path.dirname(fileURLToPath(import.meta.url))

import * as runtime from "@prisma/client/runtime/library"
import * as $Enums from "./enums"
import * as $Class from "./internal/class"
import * as Prisma from "./internal/prismaNamespace"

export * as $Enums from './enums'
/**
 * ## Prisma Client
 * 
 * Type-safe database client for TypeScript
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more Artists
 * const artists = await prisma.artist.findMany()
 * ```
 * 
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export const PrismaClient = $Class.getPrismaClientClass(__dirname)
export type PrismaClient<ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions, Log = $Class.LogOptions<ClientOptions>, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = $Class.PrismaClient<ClientOptions, Log, ExtArgs>
export { Prisma }


// file annotations for bundling tools to include these files
path.join(__dirname, "query_engine-windows.dll.node")
path.join(process.cwd(), "prisma/generated/query_engine-windows.dll.node")

/**
 * Model Artist
 * 
 */
export type Artist = Prisma.ArtistModel
/**
 * Model ArtistIdentifier
 * 
 */
export type ArtistIdentifier = Prisma.ArtistIdentifierModel
/**
 * Model User
 * 
 */
export type User = Prisma.UserModel
/**
 * Model Session
 * 
 */
export type Session = Prisma.SessionModel
/**
 * Model Account
 * 
 */
export type Account = Prisma.AccountModel
/**
 * Model Verification
 * 
 */
export type Verification = Prisma.VerificationModel
/**
 * Model Contributor
 * 
 */
export type Contributor = Prisma.ContributorModel
/**
 * Model Label
 * 
 */
export type Label = Prisma.LabelModel
/**
 * Model Release
 * 
 */
export type Release = Prisma.ReleaseModel
/**
 * Model ReleaseArtist
 * 
 */
export type ReleaseArtist = Prisma.ReleaseArtistModel
/**
 * Model ReleaseTrack
 * 
 */
export type ReleaseTrack = Prisma.ReleaseTrackModel
/**
 * Model ReleaseCoverArt
 * 
 */
export type ReleaseCoverArt = Prisma.ReleaseCoverArtModel
/**
 * Model Track
 * 
 */
export type Track = Prisma.TrackModel
/**
 * Model TrackArtist
 * 
 */
export type TrackArtist = Prisma.TrackArtistModel
/**
 * Model TrackContributor
 * 
 */
export type TrackContributor = Prisma.TrackContributorModel
/**
 * Model TrackFile
 * 
 */
export type TrackFile = Prisma.TrackFileModel

export type Service = $Enums.Service
export const Service = $Enums.Service

export type ReleaseFormat = $Enums.ReleaseFormat
export const ReleaseFormat = $Enums.ReleaseFormat

export type ExplicitContent = $Enums.ExplicitContent
export const ExplicitContent = $Enums.ExplicitContent

export type ArtistRole = $Enums.ArtistRole
export const ArtistRole = $Enums.ArtistRole

export type ReleaseStatus = $Enums.ReleaseStatus
export const ReleaseStatus = $Enums.ReleaseStatus

export type RightsClaim = $Enums.RightsClaim
export const RightsClaim = $Enums.RightsClaim

export type TrackStatus = $Enums.TrackStatus
export const TrackStatus = $Enums.TrackStatus
