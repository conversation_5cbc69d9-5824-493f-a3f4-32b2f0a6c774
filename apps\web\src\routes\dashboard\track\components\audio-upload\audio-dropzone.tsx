import React, { useState } from "react";
import { useDropzone } from "react-dropzone";
import { cn } from "@/lib/utils";
import {
  useUploader,
  formatFileSize,
} from "@/components/upload/uploader-provider";
import { UploadCloud, AlertCircle } from "lucide-react";

interface AudioDropzoneProps {
  className?: string;
  disabled?: boolean;
}

export const AudioDropzone: React.FC<AudioDropzoneProps> = ({
  className,
  disabled,
}) => {
  const { fileStates, addFiles } = useUploader();
  const [error, setError] = useState<string>();

  const maxFiles = 1;
  const maxSize = 200 * 1024 * 1024; // 200MB
  const isMaxFilesReached = fileStates.length >= maxFiles;
  const isDisabled = disabled || isMaxFilesReached;

  const {
    getRootProps,
    getInputProps,
    isDragActive,
    isDragAccept,
    isDragReject,
  } = useDropzone({
    disabled: isDisabled,
    accept: {
      "audio/wav": [".wav"],
    },
    maxSize,
    onDrop: (acceptedFiles, rejectedFiles) => {
      setError(undefined);

      // Handle rejections
      if (rejectedFiles.length > 0) {
        const rejection = rejectedFiles[0];
        if (rejection?.errors[0]) {
          const errorCode = rejection.errors[0].code;
          const messages: Record<string, string> = {
            "file-too-large": `File is too large. Max size is ${formatFileSize(
              maxSize
            )}`,
            "file-invalid-type":
              "Invalid audio file type. Please upload WAV files only.",
            "too-many-files": `You can only upload ${maxFiles} file maximum.`,
          };
          setError(messages[errorCode] || "File upload failed");
        }
        return;
      }

      // Check file limit
      if (acceptedFiles.length + fileStates.length > maxFiles) {
        setError(`You can only upload ${maxFiles} file maximum.`);
        return;
      }

      addFiles(acceptedFiles);
    },
  });

  const dropzoneClasses = cn(
    "relative rounded-lg p-8 border-2 border-dashed transition-colors duration-200 ease-in-out cursor-pointer",
    "flex flex-col items-center justify-center min-h-[200px]",
    isDragActive && "border-blue-500 bg-blue-50 dark:bg-blue-950",
    isDragAccept && "border-green-500 bg-green-50 dark:bg-green-950",
    isDragReject && "border-red-500 bg-red-50 dark:bg-red-950",
    isDisabled
      ? "border-gray-300 bg-gray-50 dark:bg-gray-800 cursor-not-allowed opacity-50"
      : "border-gray-300 hover:border-gray-400 dark:border-gray-600 dark:hover:border-gray-500",
    className
  );

  return (
    <div className="space-y-4">
      <div {...getRootProps()} className={dropzoneClasses}>
        <input {...getInputProps()} />
        <div className="flex flex-col items-center gap-4 text-center">
          <div className="p-4 rounded-full bg-muted">
            <UploadCloud className="h-8 w-8 text-muted-foreground" />
          </div>
          <div className="space-y-2">
            {isDragActive ? (
              <p className="text-lg font-medium text-blue-600 dark:text-blue-400">
                Drop your audio files here...
              </p>
            ) : (
              <>
                <p className="text-lg font-medium">
                  Drag & drop your WAV file here, or click to browse
                </p>
                <p className="text-sm text-muted-foreground">
                  Supports WAV files only
                </p>
                <p className="text-xs text-muted-foreground">
                  Max {maxFiles} file • Max {formatFileSize(maxSize)}
                </p>
              </>
            )}
          </div>
        </div>
      </div>

      {error && (
        <div className="flex items-center gap-2 p-3 text-sm text-red-700 bg-red-50 dark:bg-red-900/10 dark:text-red-400 rounded-lg">
          <AlertCircle className="h-4 w-4 shrink-0" />
          {error}
        </div>
      )}
    </div>
  );
};
