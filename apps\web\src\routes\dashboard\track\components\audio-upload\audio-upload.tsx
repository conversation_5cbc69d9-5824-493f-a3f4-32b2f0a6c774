import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Music } from "lucide-react";
import {
  UploaderProvider,
  type UploadFn,
  type DeleteFn,
} from "@/components/upload/uploader-provider";
import { useEdgeStore } from "@/lib/storage";
import { AudioDropzone } from "./audio-dropzone";
import { AudioFileList } from "./audio-file-list";

// Main AudioUpload component
export function AudioUpload() {
  const { edgestore } = useEdgeStore();

  // Define the upload function for EdgeStore
  const uploadFn: UploadFn = React.useCallback(
    async ({ file, onProgressChange, signal }) => {
      try {
        const result = await edgestore.audio.upload({
          file,
          signal,
          onProgressChange,
        });

        return { url: result.url };
      } catch (error) {
        console.error("Upload error:", error);
        throw error;
      }
    },
    [edgestore]
  );

  // Define the delete function for EdgeStore
  const deleteFn: DeleteFn = React.useCallback(
    async ({ url }) => {
      try {
        await edgestore.audio.delete({
          url,
        });
      } catch (error) {
        console.error("Delete error:", error);
        throw error;
      }
    },
    [edgestore]
  );

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Music className="h-5 w-5" />
          Audio Files
        </CardTitle>
        <CardDescription>
          Upload your audio track. Supported format: WAV only (Max 200MB)
        </CardDescription>
      </CardHeader>
      <CardContent>
        <UploaderProvider
          uploadFn={uploadFn}
          deleteFn={deleteFn}
          autoUpload={false}
        >
          <div className="space-y-6">
            <AudioDropzone />
            <AudioFileList />
          </div>
        </UploaderProvider>
      </CardContent>
    </Card>
  );
}
