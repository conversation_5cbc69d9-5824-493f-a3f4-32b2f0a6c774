import * as React from "react";
import {
  <PERSON>ame,
  LifeBuoy,
  Map,
  <PERSON><PERSON>,
  Send,
  Settings2,
  Music,
  Disc3,
  Users,
  Building2,
  Piano,
} from "lucide-react";

import { NavMain } from "@/components/nav-main";
import { NavSecondary } from "@/components/nav-secondary";
import { NavUser } from "@/components/nav-user";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import { authClient } from "@/lib/auth-client";
import { CreateArtistDialog } from "@/routes/dashboard/artist/components/create-artist-dialog";
import { CreateLabelDialog } from "@/routes/dashboard/label/components/create-label-dialog";
import { CreateContributorDialog } from "@/routes/dashboard/contributor/components/create-contributor-dialog";
import {
  useSidebarRenderers,
  type SidebarCustomRenderers,
} from "@/lib/sidebar-renderers";

const data = {
  navMain: [
    {
      title: "Releases",
      url: "#",
      icon: Disc3,
      isActive: true,
      items: [
        {
          title: "New Release",
          url: "#",
        },
        {
          title: "All Releases",
          url: "#",
        },
        {
          title: "Need Corrections",
          url: "#",
        },
      ],
    },
    {
      title: "Tracks",
      url: "#",
      icon: Music,
      items: [
        {
          title: "New Track",
          url: "#",
        },
        {
          title: "All Tracks",
          url: "/dashboard/track",
        },
      ],
    },
    {
      title: "Artists",
      url: "#",
      icon: Piano,
      items: [
        {
          title: "New Artist",
          url: "#",
        },
        {
          title: "All Artists",
          url: "/dashboard/artist",
        },
      ],
    },
    {
      title: "Labels",
      url: "#",
      icon: Building2,
      items: [
        {
          title: "New Label",
          url: "#",
        },
        {
          title: "All Labels",
          url: "/dashboard/label",
        },
      ],
    },
    {
      title: "Contributors",
      url: "#",
      icon: Users,
      items: [
        {
          title: "New Contributor",
          url: "#",
        },
        {
          title: "All Contributors",
          url: "/dashboard/contributor",
        },
      ],
    },
    {
      title: "Admin",
      url: "#",
      icon: Settings2,
      items: [
        {
          title: "User Management",
          url: "/dashboard/user-management",
        },
        {
          title: "Team",
          url: "#",
        },
        {
          title: "Billing",
          url: "#",
        },
        {
          title: "Limits",
          url: "#",
        },
      ],
    },
  ],
  navSecondary: [
    {
      title: "Support",
      url: "#",
      icon: LifeBuoy,
    },
    {
      title: "Feedback",
      url: "#",
      icon: Send,
    },
  ],
  projects: [
    {
      name: "Design Engineering",
      url: "#",
      icon: Frame,
    },
    {
      name: "Sales & Marketing",
      url: "#",
      icon: PieChart,
    },
    {
      name: "Travel",
      url: "#",
      icon: Map,
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { data: session } = authClient.useSession();

  // Define custom renderer configurations
  const rendererConfigs: SidebarCustomRenderers = {
    "New Artist": {
      type: "dialog",
      component: CreateArtistDialog,
      navigationPath: "/dashboard/artist",
      successPropName: "onArtistCreated",
    },
    "New Label": {
      type: "dialog",
      component: CreateLabelDialog,
      navigationPath: "/dashboard/label",
      successPropName: "onLabelCreated",
    },
    "New Contributor": {
      type: "dialog",
      component: CreateContributorDialog,
      navigationPath: "/dashboard/contributor",
      successPropName: "onContributorCreated",
    },
    // Add more custom renderers here as needed:
    // "New Release": {
    //   type: "dialog",
    //   component: CreateReleaseDialog,
    //   navigationPath: "/dashboard/releases",
    //   successPropName: "onReleaseCreated",
    // },
  };

  // Generate custom renderers using the hook
  const customRenderers = useSidebarRenderers(rendererConfigs);

  // Filter navigation items based on user role
  const filteredNavMain = React.useMemo(() => {
    return data.navMain.filter((item) => {
      // Show Admin section only for users with admin role
      if (item.title === "Admin") {
        return session?.user?.role === "admin";
      }
      // Show all other sections for all authenticated users
      return true;
    });
  }, [session?.user?.role]);

  return (
    <Sidebar variant="inset" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <a href="/dashboard">
                <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                  <Music className="size-4" />
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-medium">Soundmera</span>
                  <span className="truncate text-xs">
                    Unleash your creativity!
                  </span>
                </div>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent className="flex flex-col overflow-hidden">
        <NavMain items={filteredNavMain} customRenderers={customRenderers} />
        <NavSecondary items={data.navSecondary} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  );
}
