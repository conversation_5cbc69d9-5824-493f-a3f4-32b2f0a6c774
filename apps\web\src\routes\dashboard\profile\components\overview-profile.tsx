import { Save, Send } from "lucide-react";
import { <PERSON><PERSON>lert } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { useForm } from "@tanstack/react-form";
import { toast } from "sonner";
import { z } from "zod";
import { authClient } from "@/lib/auth-client";
import { Navigate } from "@tanstack/react-router";
import { Alert } from "@/components/ui/alert";
import { useTRPCClient } from "@/utils/trpc";
import { useMutation } from "@tanstack/react-query";

interface OverviewProfileProps {
  session: {
    user: {
      name: string;
      email: string;
      phone?: string | null;
      emailVerified?: boolean;
    };
  } | null;
}

export default function OverviewProfile({ session }: OverviewProfileProps) {
  const trpcClient = useTRPCClient();

  const sendVerificationEmailMutation = useMutation({
    mutationFn: () => trpcClient.email.sendVerificationEmail.mutate(),
    onSuccess: () => {
      toast.success("Verification email sent! Please check your inbox.");
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to send verification email");
    },
  });

  const form = useForm({
    defaultValues: {
      name: session?.user?.name || "",
      email: session?.user?.email || "",
      phone: session?.user?.phone || "",
      emailVerified: session?.user?.emailVerified || false,
    },
    onSubmit: async ({ value }) => {
      await authClient.updateUser(
        {
          name: value.name,
          phone: value.phone,
        },
        {
          onSuccess: () => {
            toast.success("Profile updated successfully");
            Navigate({
              to: "/dashboard",
            });
          },
          onError: (error) => {
            toast.error(error.error.message);
          },
        }
      );
    },
    validators: {
      onSubmit: z.object({
        name: z.string().min(1, "Full name is required"),
        phone: z
          .string()
          .min(1, "Phone number is required")
          .refine(
            (value) => !value.includes("+"),
            "Phone number cannot contain +"
          ),
        email: z.string().email("Invalid email address"),
        emailVerified: z.boolean(),
      }),
    },
  });

  return (
    <>
      <div className="flex flex-col mb-4 sm:mb-5">
        <div className="text-md sm:text-lg font-bold">Personal Information</div>
        <div className="text-xs sm:text-sm text-muted-foreground">
          Update your personal information here
        </div>
      </div>
      <Separator className="my-4" />
      <form
        onSubmit={(e) => {
          e.preventDefault();
          e.stopPropagation();
          void form.handleSubmit();
        }}
        className="space-y-6 sm:space-y-8"
      >
        <div>
          <form.Field name="name">
            {(field) => (
              <div className="grid grid-cols-1 sm:grid-cols-1 lg:grid-cols-5 gap-4 lg:gap-6 xl:gap-8 items-start">
                <div className="lg:col-span-2 space-y-1">
                  <Label htmlFor={field.name} className="text-sm font-medium">
                    Full Name
                  </Label>
                  <p className="text-xs sm:text-sm text-muted-foreground leading-relaxed">
                    Your legal name is essential for payment verification and
                    will be used to validate your identity during any financial
                    transactions
                  </p>
                </div>
                <div className="lg:col-span-3 space-y-2">
                  <Input
                    id={field.name}
                    name={field.name}
                    type="text"
                    placeholder="Enter your full name"
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    className="w-full"
                  />
                  {field.state.meta.errors.map((error) => (
                    <p
                      key={error?.message}
                      className="text-destructive text-sm"
                    >
                      {error?.message}
                    </p>
                  ))}
                </div>
              </div>
            )}
          </form.Field>
        </div>

        {!session?.user.emailVerified && (
          <Alert
            layout="row"
            icon={
              <TriangleAlert
                className="text-amber-500"
                size={16}
                strokeWidth={2}
              />
            }
          >
            <div className="flex grow justify-between gap-3">
              <p className="text-sm">
                Your email address is currently not verified. Please verify your
                email address.
              </p>
              <button
                onClick={() => sendVerificationEmailMutation.mutate()}
                disabled={sendVerificationEmailMutation.isPending}
                className="group whitespace-nowrap text-sm font-medium hover:text-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {sendVerificationEmailMutation.isPending
                  ? "Sending..."
                  : "Verify Email"}
                <Send
                  className="-mt-0.5 ms-1 inline-flex opacity-60 transition-transform group-hover:translate-x-0.5"
                  size={16}
                  strokeWidth={2}
                />
              </button>
            </div>
          </Alert>
        )}

        <div>
          <form.Field name="email">
            {(field) => (
              <div className="grid grid-cols-1 sm:grid-cols-1 lg:grid-cols-5 gap-4 lg:gap-6 xl:gap-8 items-start">
                <div className="lg:col-span-2 space-y-1">
                  <Label htmlFor={field.name} className="text-sm font-medium">
                    Email Address
                  </Label>
                  <p className="text-xs sm:text-sm text-muted-foreground leading-relaxed">
                    Your email address is used for account access and receiving
                    important notifications
                  </p>
                </div>
                <div className="lg:col-span-3 space-y-2">
                  <Input
                    id={field.name}
                    name={field.name}
                    type="email"
                    placeholder="Enter your email"
                    disabled
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    className="w-full"
                  />
                  <p className="text-xs sm:text-sm text-muted-foreground leading-relaxed">
                    Request to change email address on account security tab
                  </p>
                  {field.state.meta.errors.map((error) => (
                    <p
                      key={error?.message}
                      className="text-destructive text-sm"
                    >
                      {error?.message}
                    </p>
                  ))}
                </div>
              </div>
            )}
          </form.Field>
        </div>

        <div>
          <form.Field name="phone">
            {(field) => (
              <div className="grid grid-cols-1 sm:grid-cols-1 lg:grid-cols-5 gap-4 lg:gap-6 xl:gap-8 items-start">
                <div className="lg:col-span-2 space-y-1">
                  <Label htmlFor={field.name} className="text-sm font-medium">
                    Phone Number
                  </Label>
                  <p className="text-xs sm:text-sm text-muted-foreground leading-relaxed">
                    WhatsApp number is preferred for further support and
                    verification purposes
                  </p>
                </div>
                <div className="lg:col-span-3 space-y-2">
                  <Input
                    id={field.name}
                    name={field.name}
                    type="tel"
                    placeholder="Enter your phone number"
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    className="w-full"
                  />
                  {field.state.meta.errors.map((error) => (
                    <p
                      key={error?.message}
                      className="text-destructive text-sm"
                    >
                      {error?.message}
                    </p>
                  ))}
                </div>
              </div>
            )}
          </form.Field>
        </div>
        <Separator className="my-4" />
        <div className="grid grid-cols-1 sm:grid-cols-1 lg:grid-cols-5 gap-4 lg:gap-6 xl:gap-8">
          <div className="lg:col-span-2"></div>
          <div className="lg:col-span-3">
            <form.Subscribe
              selector={(state) => [state.canSubmit, state.isSubmitting]}
            >
              {([canSubmit, isSubmitting]) => (
                <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
                  <Button
                    type="submit"
                    disabled={!canSubmit}
                    className="w-full sm:w-auto sm:min-w-[160px] h-10"
                    size="default"
                  >
                    {isSubmitting ? (
                      <>
                        <Save className="h-4 w-4 animate-spin" />
                        <span>Saving...</span>
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4" />
                        <span>Save Changes</span>
                      </>
                    )}
                  </Button>
                </div>
              )}
            </form.Subscribe>
          </div>
        </div>
      </form>
    </>
  );
}
