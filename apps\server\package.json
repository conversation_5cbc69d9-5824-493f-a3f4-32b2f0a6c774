{"name": "server", "main": "src/index.ts", "type": "module", "scripts": {"build": "tsc && tsc-alias", "check-types": "tsc --noEmit", "compile": "bun build --compile --minify --sourcemap --bytecode ./src/index.ts --outfile server", "dev": "tsx watch src/index.ts", "start": "node dist/src/index.js", "db:push": "prisma db push --schema ./prisma/schema", "db:studio": "prisma studio", "db:generate": "prisma generate --schema ./prisma/schema", "db:migrate": "prisma migrate dev"}, "prisma": {"schema": "./schema"}, "dependencies": {"@aws-sdk/client-s3": "^3.844.0", "@aws-sdk/s3-request-presigner": "^3.844.0", "@edgestore/react": "^0.5.3", "@edgestore/server": "^0.5.3", "@hono/node-server": "^1.14.0", "@hono/trpc-server": "^0.3.4", "@prisma/client": "^6.9.0", "@trpc/client": "^11.0.0", "@trpc/server": "^11.0.0", "better-auth": "^1.2.9", "dotenv": "^16.4.7", "hono": "^4.7.6", "loops": "^5.0.1", "zod": "^3.25.76"}, "devDependencies": {"@types/node": "^22.13.11", "prisma": "^6.9.0", "tsc-alias": "^1.8.11", "tsx": "^4.19.2", "typescript": "^5.8.2"}}