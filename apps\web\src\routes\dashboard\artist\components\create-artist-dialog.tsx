import { useState, useEffect } from "react";
import { Loader2, PlusI<PERSON>, TrashIcon } from "lucide-react";
import { toast } from "sonner";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";

import { useTRPCClient } from "@/utils/trpc";
import { authClient } from "@/lib/auth-client";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { GenreSelect } from "@/components/ui/genre-select";
import type { ArtistIdentifier } from "@/routes/dashboard/artist/components/artist-table";
import { CountryDropdown } from "@/components/ui/country-dropdown";

interface CreateArtistDialogProps {
  children: React.ReactNode;
  onArtistCreated: () => void;
}

const SERVICE_OPTIONS = [
  { value: "YOUTUBE", label: "YouTube" },
  { value: "SPOTIFY", label: "Spotify" },
  { value: "APPLE_MUSIC", label: "Apple Music" },
  { value: "SOUNDCLOUD", label: "SoundCloud" },
  { value: "AUDIOMACK", label: "Audiomack" },
] as const;

const REQUIRED_SERVICES = ["SPOTIFY", "APPLE_MUSIC"] as const;

export function CreateArtistDialog({
  children,
  onArtistCreated,
}: CreateArtistDialogProps) {
  const [open, setOpen] = useState(false);
  const navigate = useNavigate();
  const trpcClient = useTRPCClient();
  const { data: session } = authClient.useSession();
  const isAdmin = session?.user?.role === "admin";
  const [formData, setFormData] = useState({
    name: "",
    instagram: "",
    biography: "",
    country: "",
    genre: "",
    labelId: "",
    identifiers: [] as ArtistIdentifier[],
    spotifyNoProfile: false,
    appleMusicNoProfile: false,
  });

  const {
    data: labels,
    refetch: refetchLabels,
    isLoading: labelsLoading,
  } = useQuery({
    queryKey: ["labelsForSelect"],
    queryFn: () => trpcClient.label.getForSelect.query(),
    enabled: open,
  });

  const [createLabelOpen, setCreateLabelOpen] = useState(false);
  const [newLabelName, setNewLabelName] = useState("");

  const createLabelMutation = useMutation({
    mutationFn: async () => {
      return trpcClient.label.create.mutate({ name: newLabelName.trim() });
    },
    onSuccess: async (data) => {
      toast.success("Label created successfully");
      await refetchLabels();
      setFormData((prev) => ({ ...prev, labelId: data.id }));
      setNewLabelName("");
      setCreateLabelOpen(false);
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to create label");
    },
  });

  // Initialize required platform identifiers when dialog opens
  useEffect(() => {
    if (open) {
      const requiredIdentifiers: ArtistIdentifier[] = [];

      // Add Spotify if not exists
      if (!formData.identifiers.some((id) => id.service === "SPOTIFY")) {
        requiredIdentifiers.push({ service: "SPOTIFY", identifier: "" });
      }

      // Add Apple Music if not exists
      if (!formData.identifiers.some((id) => id.service === "APPLE_MUSIC")) {
        requiredIdentifiers.push({ service: "APPLE_MUSIC", identifier: "" });
      }

      if (requiredIdentifiers.length > 0) {
        setFormData((prev) => ({
          ...prev,
          identifiers: [...prev.identifiers, ...requiredIdentifiers],
        }));
      }
    }
  }, [open]);

  const createArtistMutation = useMutation({
    mutationFn: async (data: typeof formData) => {
      // Filter out identifiers that are empty
      const validIdentifiers = data.identifiers.filter(
        (identifier) => identifier.identifier.trim() !== ""
      );

      return trpcClient.artist.create.mutate({
        name: data.name,
        instagram: data.instagram || undefined,
        biography: data.biography || undefined,
        country: data.country || undefined,
        genre: data.genre || undefined,
        labelId: data.labelId || undefined,
        identifiers: validIdentifiers.length > 0 ? validIdentifiers : undefined,
      });
    },
    onSuccess: (data) => {
      toast.success("Artist created successfully");
      setOpen(false);
      setFormData({
        name: "",
        instagram: "",
        biography: "",
        country: "",
        genre: "",
        labelId: "",
        identifiers: [],
        spotifyNoProfile: false,
        appleMusicNoProfile: false,
      });
      onArtistCreated();

      // Redirect to the artist details page
      if (data?.id) {
        navigate({
          to: "/dashboard/artist/$id",
          params: { id: data.id },
        });
      }
    },
    onError: (error: any) => {
      console.error("Failed to create artist:", error);
      toast.error(
        "Failed to create artist: " + (error.message || "Unknown error")
      );
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast.error("Artist name is required");
      return;
    }

    // Validate required platform identifiers
    const spotifyIdentifier = formData.identifiers.find(
      (id) => id.service === "SPOTIFY"
    );
    const appleMusicIdentifier = formData.identifiers.find(
      (id) => id.service === "APPLE_MUSIC"
    );

    if (
      !formData.spotifyNoProfile &&
      (!spotifyIdentifier || !spotifyIdentifier.identifier.trim())
    ) {
      toast.error(
        "Spotify identifier is required or mark as 'Create a new profile for me'"
      );
      return;
    }

    if (
      !formData.appleMusicNoProfile &&
      (!appleMusicIdentifier || !appleMusicIdentifier.identifier.trim())
    ) {
      toast.error(
        "Apple Music identifier is required or mark as 'Create a new profile for me'"
      );
      return;
    }

    try {
      await createArtistMutation.mutateAsync({
        ...formData,
        name: formData.name.trim(),
        instagram: formData.instagram.trim(),
        biography: formData.biography.trim(),
        country: formData.country.trim(),
        genre: formData.genre.trim(),
      });
    } catch (error) {
      // Error handling is done in the mutation
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const addIdentifier = () => {
    const allSelectedServices = formData.identifiers.map((id) => id.service);
    const firstAvailable = SERVICE_OPTIONS.find(
      (option) => !allSelectedServices.includes(option.value)
    );

    if (!firstAvailable) {
      toast.error("All available platforms have been added.");
      return;
    }

    setFormData((prev) => ({
      ...prev,
      identifiers: [
        ...prev.identifiers,
        { service: firstAvailable.value, identifier: "" },
      ],
    }));
  };

  const removeIdentifier = (index: number) => {
    const identifier = formData.identifiers[index];
    // Don't allow removing required services
    if (REQUIRED_SERVICES.includes(identifier.service as any)) {
      toast.error(
        `${identifier.service.replace(
          "_",
          " "
        )} is required. Use "I don't have profile" option instead.`
      );
      return;
    }

    setFormData((prev) => ({
      ...prev,
      identifiers: prev.identifiers.filter((_, i) => i !== index),
    }));
  };

  const updateIdentifier = (
    index: number,
    field: keyof ArtistIdentifier,
    value: string
  ) => {
    setFormData((prev) => ({
      ...prev,
      identifiers: prev.identifiers.map((identifier, i) =>
        i === index ? { ...identifier, [field]: value } : identifier
      ),
    }));
  };

  const handleNoProfileChange = (
    service: "SPOTIFY" | "APPLE_MUSIC",
    checked: boolean
  ) => {
    const fieldName =
      service === "SPOTIFY" ? "spotifyNoProfile" : "appleMusicNoProfile";
    setFormData((prev) => ({
      ...prev,
      [fieldName]: checked,
    }));

    // Clear the identifier if "no profile" is checked
    if (checked) {
      setFormData((prev) => ({
        ...prev,
        identifiers: prev.identifiers.map((identifier) =>
          identifier.service === service
            ? {
                ...identifier,
                identifier: "Create New",
              }
            : identifier
        ),
      }));
    } else {
      // Clear the placeholder text when unchecked
      setFormData((prev) => ({
        ...prev,
        identifiers: prev.identifiers.map((identifier) =>
          identifier.service === service
            ? { ...identifier, identifier: "" }
            : identifier
        ),
      }));
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[1300px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Artist</DialogTitle>
          <DialogDescription>
            Add a new artist to your catalog. Spotify and Apple Music
            identifiers are required.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Basic Information */}
          <div className="space-y-4">
            {isAdmin && (
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="createdBy">Created By</Label>
                <Input
                  id="createdBy"
                  value={`${session?.user?.email} | ${session?.user?.name}`}
                  disabled
                />
              </div>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name" className="block">
                Artist Name <span className="text-red-500">*</span>
              </Label>
              <p
                className="text-muted-foreground text-xs"
                role="region"
                aria-live="polite"
              >
                This is the name that will be displayed on the artist page,
                please make sure to use the correct name.
              </p>
              <Input
                id="name"
                placeholder="Enter artist name"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                required
                disabled={createArtistMutation.isPending}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="label">Label</Label>
              <p className="text-muted-foreground text-xs">
                Assign to a record label or create a new one.
              </p>
              <Select
                value={formData.labelId}
                onValueChange={(value) => {
                  if (value === "__create__") {
                    setCreateLabelOpen(true);
                  } else {
                    handleInputChange("labelId", value);
                  }
                }}
                disabled={createArtistMutation.isPending || labelsLoading}
              >
                <SelectTrigger className="flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1 cursor-pointer">
                  <SelectValue placeholder="Select label" />
                </SelectTrigger>
                <SelectContent>
                  {labels?.map((label) => (
                    <SelectItem key={label.id} value={label.id}>
                      {label.name}
                    </SelectItem>
                  ))}
                  <SelectItem value="__create__">+ Create new label</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="genre">Genre</Label>
              <GenreSelect
                value={formData.genre}
                onValueChange={(value) => handleInputChange("genre", value)}
                disabled={createArtistMutation.isPending}
                placeholder="Select a genre"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="country">Country</Label>
              <CountryDropdown
                defaultValue={formData.country}
                onChange={(country) =>
                  handleInputChange("country", country.name)
                }
                disabled={createArtistMutation.isPending}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="instagram">Instagram Handle</Label>
              <Input
                id="instagram"
                placeholder="username (without @)"
                value={formData.instagram}
                onChange={(e) => handleInputChange("instagram", e.target.value)}
                disabled={createArtistMutation.isPending}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="biography">Biography</Label>
            <Textarea
              id="biography"
              placeholder="Brief artist biography..."
              value={formData.biography}
              onChange={(e) => handleInputChange("biography", e.target.value)}
              disabled={createArtistMutation.isPending}
              rows={3}
            />
          </div>

          {/* Platform Identifiers */}
          <div className="space-y-4">
            <div className="flex items-center justify-between mb-6 mt-6">
              <div className="space-y-1">
                <Label className="block">
                  Platform Identifiers <span className="text-red-500">*</span>
                </Label>
                <p
                  className="text-muted-foreground text-xs"
                  role="region"
                  aria-live="polite"
                >
                  Add the platform identifiers for the artist. If you don't have
                  a profile on a platform, we will create a new profile for you.
                </p>
              </div>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addIdentifier}
                disabled={createArtistMutation.isPending}
              >
                <PlusIcon className="h-4 w-4" />
                Add Platform
              </Button>
            </div>

            {formData.identifiers.map((identifier, index) => {
              const isRequired = REQUIRED_SERVICES.includes(
                identifier.service as any
              );
              const noProfileChecked =
                (identifier.service === "SPOTIFY" &&
                  formData.spotifyNoProfile) ||
                (identifier.service === "APPLE_MUSIC" &&
                  formData.appleMusicNoProfile);

              const allSelectedServices = formData.identifiers.map(
                (id) => id.service
              );
              const availableServiceOptions = SERVICE_OPTIONS.filter(
                (option) =>
                  !allSelectedServices.includes(option.value as any) ||
                  option.value === identifier.service
              );

              return (
                <div key={index} className="space-y-2">
                  <div className="flex flex-col md:flex-row gap-2 md:items-end">
                    <div className="flex-1">
                      <Label className="mb-2 block">
                        Platform{" "}
                        {isRequired && <span className="text-red-500">*</span>}
                      </Label>
                      <Select
                        value={identifier.service}
                        onValueChange={(value) =>
                          updateIdentifier(index, "service", value)
                        }
                        disabled={createArtistMutation.isPending || isRequired}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {availableServiceOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="flex-1">
                      <Label className="mb-2 block">
                        Artist Page URL{" "}
                        {isRequired && <span className="text-red-500">*</span>}
                      </Label>
                      <Input
                        placeholder="Artist Page URL"
                        value={identifier.identifier}
                        onChange={(e) =>
                          updateIdentifier(index, "identifier", e.target.value)
                        }
                        disabled={
                          createArtistMutation.isPending || noProfileChecked
                        }
                      />
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeIdentifier(index)}
                      disabled={createArtistMutation.isPending || isRequired}
                    >
                      <TrashIcon className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* "No Profile" option for required services */}
                  {isRequired && (
                    <div className="flex items-center space-x-3 mt-2">
                      <Checkbox
                        id={`no-profile-${identifier.service}`}
                        checked={noProfileChecked}
                        onCheckedChange={(checked) =>
                          handleNoProfileChange(
                            identifier.service as "SPOTIFY" | "APPLE_MUSIC",
                            !!checked
                          )
                        }
                        disabled={createArtistMutation.isPending}
                      />
                      <Label
                        htmlFor={`no-profile-${identifier.service}`}
                        className="text-sm"
                      >
                        Create a new profile on{" "}
                        {identifier.service.replace("_", " ")} for me
                      </Label>
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={createArtistMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={createArtistMutation.isPending || !formData.name.trim()}
            >
              {createArtistMutation.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                "Create Artist"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>

      <Dialog open={createLabelOpen} onOpenChange={setCreateLabelOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Label</DialogTitle>
            <DialogDescription>
              Add a new label to organize your artists and releases.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-2">
            <Label htmlFor="labelName" className="block">
              Label Name <span className="text-red-500">*</span>
            </Label>
            <p className="text-muted-foreground text-xs">
              This is the name that will be displayed on the label section,
              please make sure to use the correct name.
            </p>
            <Input
              id="labelName"
              placeholder="Enter label name"
              value={newLabelName}
              onChange={(e) => setNewLabelName(e.target.value)}
              required
              disabled={createLabelMutation.isPending}
            />
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setCreateLabelOpen(false)}
              disabled={createLabelMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type="button"
              onClick={() => createLabelMutation.mutate()}
              disabled={createLabelMutation.isPending || !newLabelName.trim()}
            >
              {createLabelMutation.isPending ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                "Create"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Dialog>
  );
}
