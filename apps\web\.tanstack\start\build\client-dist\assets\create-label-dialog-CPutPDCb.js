import{r as u,a as N,t as l,j as e,L as v}from"./main-B9Fv5CdX.js";import{u as F}from"./useMutation-DGkS69KN.js";import{a as w}from"./auth-client-C8WifPV2.js";import{j as P,k as S,l as k,m as B,n as E,o as T,p as A}from"./dialog-iGlJJq5Q.js";import{B as h}from"./button-Ispz1G12.js";import{L as p,I as x}from"./label-CNQvdrLZ.js";function z({children:b,onLabelCreated:g}){var o,c,m;const[j,n]=u.useState(!1),C=N(),{data:s}=w.useSession(),f=((o=s==null?void 0:s.user)==null?void 0:o.role)==="admin",[r,i]=u.useState({name:""}),t=F({mutationFn:async a=>C.label.create.mutate({name:a.name}),onSuccess:()=>{l.success("Label created successfully"),n(!1),i({name:""}),g()},onError:a=>{console.error("Failed to create label:",a),l.error("Failed to create label: "+(a.message||"Unknown error"))}}),y=async a=>{if(a.preventDefault(),!r.name.trim()){l.error("Label name is required");return}try{await t.mutateAsync({name:r.name.trim()})}catch{}},D=(a,d)=>{i(L=>({...L,[a]:d}))};return e.jsxs(P,{open:j,onOpenChange:n,children:[e.jsx(S,{asChild:!0,children:b}),e.jsxs(k,{className:"sm:max-w-[500px]",children:[e.jsxs(B,{children:[e.jsx(E,{children:"Create New Label"}),e.jsx(T,{children:"Add a new label to organize your artists and releases."})]}),e.jsxs("form",{onSubmit:y,className:"space-y-4",children:[e.jsxs("div",{className:"space-y-4",children:[f&&e.jsxs("div",{className:"space-y-2",children:[e.jsx(p,{htmlFor:"createdBy",children:"Created By"}),e.jsx(x,{id:"createdBy",value:`${(c=s==null?void 0:s.user)==null?void 0:c.email} | ${(m=s==null?void 0:s.user)==null?void 0:m.name}`,disabled:!0})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(p,{htmlFor:"name",className:"block",children:["Label Name ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("p",{className:"text-muted-foreground text-xs",role:"region","aria-live":"polite",children:"This is the name that will be displayed for the label. Please make sure to use the correct name."}),e.jsx(x,{id:"name",placeholder:"Enter label name",value:r.name,onChange:a=>D("name",a.target.value),required:!0,disabled:t.isPending})]})]}),e.jsxs(A,{children:[e.jsx(h,{type:"button",variant:"outline",onClick:()=>n(!1),disabled:t.isPending,children:"Cancel"}),e.jsx(h,{type:"submit",disabled:t.isPending||!r.name.trim(),children:t.isPending?e.jsxs(e.Fragment,{children:[e.jsx(v,{className:"h-4 w-4 animate-spin"}),"Creating..."]}):"Create Label"})]})]})]})]})}export{z as C};
