
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Track` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Track
 * 
 */
export type TrackModel = runtime.Types.Result.DefaultSelection<Prisma.$TrackPayload>

export type AggregateTrack = {
  _count: TrackCountAggregateOutputType | null
  _avg: TrackAvgAggregateOutputType | null
  _sum: TrackSumAggregateOutputType | null
  _min: TrackMinAggregateOutputType | null
  _max: TrackMaxAggregateOutputType | null
}

export type TrackAvgAggregateOutputType = {
  recordingYear: number | null
  publishingYear: number | null
}

export type TrackSumAggregateOutputType = {
  recordingYear: number | null
  publishingYear: number | null
}

export type TrackMinAggregateOutputType = {
  id: string | null
  title: string | null
  isrc: string | null
  trackVersion: string | null
  recordingYear: number | null
  publishingYear: number | null
  publishingHolder: string | null
  genre: string | null
  subGenre: string | null
  lyrics: string | null
  previewStart: string | null
  previewLength: string | null
  metadataLanguage: string | null
  explicit: $Enums.ExplicitContent | null
  audioLanguage: string | null
  rightsClaim: $Enums.RightsClaim | null
  status: $Enums.TrackStatus | null
  submittedAt: Date | null
  readyAt: Date | null
  userId: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type TrackMaxAggregateOutputType = {
  id: string | null
  title: string | null
  isrc: string | null
  trackVersion: string | null
  recordingYear: number | null
  publishingYear: number | null
  publishingHolder: string | null
  genre: string | null
  subGenre: string | null
  lyrics: string | null
  previewStart: string | null
  previewLength: string | null
  metadataLanguage: string | null
  explicit: $Enums.ExplicitContent | null
  audioLanguage: string | null
  rightsClaim: $Enums.RightsClaim | null
  status: $Enums.TrackStatus | null
  submittedAt: Date | null
  readyAt: Date | null
  userId: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type TrackCountAggregateOutputType = {
  id: number
  title: number
  isrc: number
  trackVersion: number
  recordingYear: number
  publishingYear: number
  publishingHolder: number
  genre: number
  subGenre: number
  lyrics: number
  previewStart: number
  previewLength: number
  metadataLanguage: number
  explicit: number
  audioLanguage: number
  rightsClaim: number
  status: number
  submittedAt: number
  readyAt: number
  userId: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type TrackAvgAggregateInputType = {
  recordingYear?: true
  publishingYear?: true
}

export type TrackSumAggregateInputType = {
  recordingYear?: true
  publishingYear?: true
}

export type TrackMinAggregateInputType = {
  id?: true
  title?: true
  isrc?: true
  trackVersion?: true
  recordingYear?: true
  publishingYear?: true
  publishingHolder?: true
  genre?: true
  subGenre?: true
  lyrics?: true
  previewStart?: true
  previewLength?: true
  metadataLanguage?: true
  explicit?: true
  audioLanguage?: true
  rightsClaim?: true
  status?: true
  submittedAt?: true
  readyAt?: true
  userId?: true
  createdAt?: true
  updatedAt?: true
}

export type TrackMaxAggregateInputType = {
  id?: true
  title?: true
  isrc?: true
  trackVersion?: true
  recordingYear?: true
  publishingYear?: true
  publishingHolder?: true
  genre?: true
  subGenre?: true
  lyrics?: true
  previewStart?: true
  previewLength?: true
  metadataLanguage?: true
  explicit?: true
  audioLanguage?: true
  rightsClaim?: true
  status?: true
  submittedAt?: true
  readyAt?: true
  userId?: true
  createdAt?: true
  updatedAt?: true
}

export type TrackCountAggregateInputType = {
  id?: true
  title?: true
  isrc?: true
  trackVersion?: true
  recordingYear?: true
  publishingYear?: true
  publishingHolder?: true
  genre?: true
  subGenre?: true
  lyrics?: true
  previewStart?: true
  previewLength?: true
  metadataLanguage?: true
  explicit?: true
  audioLanguage?: true
  rightsClaim?: true
  status?: true
  submittedAt?: true
  readyAt?: true
  userId?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type TrackAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Track to aggregate.
   */
  where?: Prisma.TrackWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Tracks to fetch.
   */
  orderBy?: Prisma.TrackOrderByWithRelationInput | Prisma.TrackOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.TrackWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Tracks from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Tracks.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Tracks
  **/
  _count?: true | TrackCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: TrackAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: TrackSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: TrackMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: TrackMaxAggregateInputType
}

export type GetTrackAggregateType<T extends TrackAggregateArgs> = {
      [P in keyof T & keyof AggregateTrack]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateTrack[P]>
    : Prisma.GetScalarType<T[P], AggregateTrack[P]>
}




export type TrackGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.TrackWhereInput
  orderBy?: Prisma.TrackOrderByWithAggregationInput | Prisma.TrackOrderByWithAggregationInput[]
  by: Prisma.TrackScalarFieldEnum[] | Prisma.TrackScalarFieldEnum
  having?: Prisma.TrackScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: TrackCountAggregateInputType | true
  _avg?: TrackAvgAggregateInputType
  _sum?: TrackSumAggregateInputType
  _min?: TrackMinAggregateInputType
  _max?: TrackMaxAggregateInputType
}

export type TrackGroupByOutputType = {
  id: string
  title: string
  isrc: string | null
  trackVersion: string | null
  recordingYear: number
  publishingYear: number
  publishingHolder: string
  genre: string
  subGenre: string | null
  lyrics: string | null
  previewStart: string | null
  previewLength: string | null
  metadataLanguage: string
  explicit: $Enums.ExplicitContent
  audioLanguage: string
  rightsClaim: $Enums.RightsClaim
  status: $Enums.TrackStatus
  submittedAt: Date | null
  readyAt: Date | null
  userId: string
  createdAt: Date
  updatedAt: Date
  _count: TrackCountAggregateOutputType | null
  _avg: TrackAvgAggregateOutputType | null
  _sum: TrackSumAggregateOutputType | null
  _min: TrackMinAggregateOutputType | null
  _max: TrackMaxAggregateOutputType | null
}

type GetTrackGroupByPayload<T extends TrackGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<TrackGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof TrackGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], TrackGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], TrackGroupByOutputType[P]>
      }
    >
  > 



export type TrackWhereInput = {
  AND?: Prisma.TrackWhereInput | Prisma.TrackWhereInput[]
  OR?: Prisma.TrackWhereInput[]
  NOT?: Prisma.TrackWhereInput | Prisma.TrackWhereInput[]
  id?: Prisma.StringFilter<"Track"> | string
  title?: Prisma.StringFilter<"Track"> | string
  isrc?: Prisma.StringNullableFilter<"Track"> | string | null
  trackVersion?: Prisma.StringNullableFilter<"Track"> | string | null
  recordingYear?: Prisma.IntFilter<"Track"> | number
  publishingYear?: Prisma.IntFilter<"Track"> | number
  publishingHolder?: Prisma.StringFilter<"Track"> | string
  genre?: Prisma.StringFilter<"Track"> | string
  subGenre?: Prisma.StringNullableFilter<"Track"> | string | null
  lyrics?: Prisma.StringNullableFilter<"Track"> | string | null
  previewStart?: Prisma.StringNullableFilter<"Track"> | string | null
  previewLength?: Prisma.StringNullableFilter<"Track"> | string | null
  metadataLanguage?: Prisma.StringFilter<"Track"> | string
  explicit?: Prisma.EnumExplicitContentFilter<"Track"> | $Enums.ExplicitContent
  audioLanguage?: Prisma.StringFilter<"Track"> | string
  rightsClaim?: Prisma.EnumRightsClaimFilter<"Track"> | $Enums.RightsClaim
  status?: Prisma.EnumTrackStatusFilter<"Track"> | $Enums.TrackStatus
  submittedAt?: Prisma.DateTimeNullableFilter<"Track"> | Date | string | null
  readyAt?: Prisma.DateTimeNullableFilter<"Track"> | Date | string | null
  userId?: Prisma.StringFilter<"Track"> | string
  createdAt?: Prisma.DateTimeFilter<"Track"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Track"> | Date | string
  trackFiles?: Prisma.TrackFileListRelationFilter
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
  artists?: Prisma.TrackArtistListRelationFilter
  contributors?: Prisma.TrackContributorListRelationFilter
  releases?: Prisma.ReleaseTrackListRelationFilter
}

export type TrackOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  isrc?: Prisma.SortOrderInput | Prisma.SortOrder
  trackVersion?: Prisma.SortOrderInput | Prisma.SortOrder
  recordingYear?: Prisma.SortOrder
  publishingYear?: Prisma.SortOrder
  publishingHolder?: Prisma.SortOrder
  genre?: Prisma.SortOrder
  subGenre?: Prisma.SortOrderInput | Prisma.SortOrder
  lyrics?: Prisma.SortOrderInput | Prisma.SortOrder
  previewStart?: Prisma.SortOrderInput | Prisma.SortOrder
  previewLength?: Prisma.SortOrderInput | Prisma.SortOrder
  metadataLanguage?: Prisma.SortOrder
  explicit?: Prisma.SortOrder
  audioLanguage?: Prisma.SortOrder
  rightsClaim?: Prisma.SortOrder
  status?: Prisma.SortOrder
  submittedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  readyAt?: Prisma.SortOrderInput | Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  trackFiles?: Prisma.TrackFileOrderByRelationAggregateInput
  user?: Prisma.UserOrderByWithRelationInput
  artists?: Prisma.TrackArtistOrderByRelationAggregateInput
  contributors?: Prisma.TrackContributorOrderByRelationAggregateInput
  releases?: Prisma.ReleaseTrackOrderByRelationAggregateInput
}

export type TrackWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.TrackWhereInput | Prisma.TrackWhereInput[]
  OR?: Prisma.TrackWhereInput[]
  NOT?: Prisma.TrackWhereInput | Prisma.TrackWhereInput[]
  title?: Prisma.StringFilter<"Track"> | string
  isrc?: Prisma.StringNullableFilter<"Track"> | string | null
  trackVersion?: Prisma.StringNullableFilter<"Track"> | string | null
  recordingYear?: Prisma.IntFilter<"Track"> | number
  publishingYear?: Prisma.IntFilter<"Track"> | number
  publishingHolder?: Prisma.StringFilter<"Track"> | string
  genre?: Prisma.StringFilter<"Track"> | string
  subGenre?: Prisma.StringNullableFilter<"Track"> | string | null
  lyrics?: Prisma.StringNullableFilter<"Track"> | string | null
  previewStart?: Prisma.StringNullableFilter<"Track"> | string | null
  previewLength?: Prisma.StringNullableFilter<"Track"> | string | null
  metadataLanguage?: Prisma.StringFilter<"Track"> | string
  explicit?: Prisma.EnumExplicitContentFilter<"Track"> | $Enums.ExplicitContent
  audioLanguage?: Prisma.StringFilter<"Track"> | string
  rightsClaim?: Prisma.EnumRightsClaimFilter<"Track"> | $Enums.RightsClaim
  status?: Prisma.EnumTrackStatusFilter<"Track"> | $Enums.TrackStatus
  submittedAt?: Prisma.DateTimeNullableFilter<"Track"> | Date | string | null
  readyAt?: Prisma.DateTimeNullableFilter<"Track"> | Date | string | null
  userId?: Prisma.StringFilter<"Track"> | string
  createdAt?: Prisma.DateTimeFilter<"Track"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Track"> | Date | string
  trackFiles?: Prisma.TrackFileListRelationFilter
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
  artists?: Prisma.TrackArtistListRelationFilter
  contributors?: Prisma.TrackContributorListRelationFilter
  releases?: Prisma.ReleaseTrackListRelationFilter
}, "id">

export type TrackOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  isrc?: Prisma.SortOrderInput | Prisma.SortOrder
  trackVersion?: Prisma.SortOrderInput | Prisma.SortOrder
  recordingYear?: Prisma.SortOrder
  publishingYear?: Prisma.SortOrder
  publishingHolder?: Prisma.SortOrder
  genre?: Prisma.SortOrder
  subGenre?: Prisma.SortOrderInput | Prisma.SortOrder
  lyrics?: Prisma.SortOrderInput | Prisma.SortOrder
  previewStart?: Prisma.SortOrderInput | Prisma.SortOrder
  previewLength?: Prisma.SortOrderInput | Prisma.SortOrder
  metadataLanguage?: Prisma.SortOrder
  explicit?: Prisma.SortOrder
  audioLanguage?: Prisma.SortOrder
  rightsClaim?: Prisma.SortOrder
  status?: Prisma.SortOrder
  submittedAt?: Prisma.SortOrderInput | Prisma.SortOrder
  readyAt?: Prisma.SortOrderInput | Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.TrackCountOrderByAggregateInput
  _avg?: Prisma.TrackAvgOrderByAggregateInput
  _max?: Prisma.TrackMaxOrderByAggregateInput
  _min?: Prisma.TrackMinOrderByAggregateInput
  _sum?: Prisma.TrackSumOrderByAggregateInput
}

export type TrackScalarWhereWithAggregatesInput = {
  AND?: Prisma.TrackScalarWhereWithAggregatesInput | Prisma.TrackScalarWhereWithAggregatesInput[]
  OR?: Prisma.TrackScalarWhereWithAggregatesInput[]
  NOT?: Prisma.TrackScalarWhereWithAggregatesInput | Prisma.TrackScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Track"> | string
  title?: Prisma.StringWithAggregatesFilter<"Track"> | string
  isrc?: Prisma.StringNullableWithAggregatesFilter<"Track"> | string | null
  trackVersion?: Prisma.StringNullableWithAggregatesFilter<"Track"> | string | null
  recordingYear?: Prisma.IntWithAggregatesFilter<"Track"> | number
  publishingYear?: Prisma.IntWithAggregatesFilter<"Track"> | number
  publishingHolder?: Prisma.StringWithAggregatesFilter<"Track"> | string
  genre?: Prisma.StringWithAggregatesFilter<"Track"> | string
  subGenre?: Prisma.StringNullableWithAggregatesFilter<"Track"> | string | null
  lyrics?: Prisma.StringNullableWithAggregatesFilter<"Track"> | string | null
  previewStart?: Prisma.StringNullableWithAggregatesFilter<"Track"> | string | null
  previewLength?: Prisma.StringNullableWithAggregatesFilter<"Track"> | string | null
  metadataLanguage?: Prisma.StringWithAggregatesFilter<"Track"> | string
  explicit?: Prisma.EnumExplicitContentWithAggregatesFilter<"Track"> | $Enums.ExplicitContent
  audioLanguage?: Prisma.StringWithAggregatesFilter<"Track"> | string
  rightsClaim?: Prisma.EnumRightsClaimWithAggregatesFilter<"Track"> | $Enums.RightsClaim
  status?: Prisma.EnumTrackStatusWithAggregatesFilter<"Track"> | $Enums.TrackStatus
  submittedAt?: Prisma.DateTimeNullableWithAggregatesFilter<"Track"> | Date | string | null
  readyAt?: Prisma.DateTimeNullableWithAggregatesFilter<"Track"> | Date | string | null
  userId?: Prisma.StringWithAggregatesFilter<"Track"> | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Track"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"Track"> | Date | string
}

export type TrackCreateInput = {
  id?: string
  title: string
  isrc?: string | null
  trackVersion?: string | null
  recordingYear: number
  publishingYear: number
  publishingHolder: string
  genre: string
  subGenre?: string | null
  lyrics?: string | null
  previewStart?: string | null
  previewLength?: string | null
  metadataLanguage: string
  explicit: $Enums.ExplicitContent
  audioLanguage: string
  rightsClaim: $Enums.RightsClaim
  status?: $Enums.TrackStatus
  submittedAt?: Date | string | null
  readyAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  trackFiles?: Prisma.TrackFileCreateNestedManyWithoutTrackInput
  user: Prisma.UserCreateNestedOneWithoutTracksInput
  artists?: Prisma.TrackArtistCreateNestedManyWithoutTrackInput
  contributors?: Prisma.TrackContributorCreateNestedManyWithoutTrackInput
  releases?: Prisma.ReleaseTrackCreateNestedManyWithoutTrackInput
}

export type TrackUncheckedCreateInput = {
  id?: string
  title: string
  isrc?: string | null
  trackVersion?: string | null
  recordingYear: number
  publishingYear: number
  publishingHolder: string
  genre: string
  subGenre?: string | null
  lyrics?: string | null
  previewStart?: string | null
  previewLength?: string | null
  metadataLanguage: string
  explicit: $Enums.ExplicitContent
  audioLanguage: string
  rightsClaim: $Enums.RightsClaim
  status?: $Enums.TrackStatus
  submittedAt?: Date | string | null
  readyAt?: Date | string | null
  userId: string
  createdAt?: Date | string
  updatedAt?: Date | string
  trackFiles?: Prisma.TrackFileUncheckedCreateNestedManyWithoutTrackInput
  artists?: Prisma.TrackArtistUncheckedCreateNestedManyWithoutTrackInput
  contributors?: Prisma.TrackContributorUncheckedCreateNestedManyWithoutTrackInput
  releases?: Prisma.ReleaseTrackUncheckedCreateNestedManyWithoutTrackInput
}

export type TrackUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  isrc?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  trackVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  recordingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingHolder?: Prisma.StringFieldUpdateOperationsInput | string
  genre?: Prisma.StringFieldUpdateOperationsInput | string
  subGenre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lyrics?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  previewStart?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  previewLength?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metadataLanguage?: Prisma.StringFieldUpdateOperationsInput | string
  explicit?: Prisma.EnumExplicitContentFieldUpdateOperationsInput | $Enums.ExplicitContent
  audioLanguage?: Prisma.StringFieldUpdateOperationsInput | string
  rightsClaim?: Prisma.EnumRightsClaimFieldUpdateOperationsInput | $Enums.RightsClaim
  status?: Prisma.EnumTrackStatusFieldUpdateOperationsInput | $Enums.TrackStatus
  submittedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  readyAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  trackFiles?: Prisma.TrackFileUpdateManyWithoutTrackNestedInput
  user?: Prisma.UserUpdateOneRequiredWithoutTracksNestedInput
  artists?: Prisma.TrackArtistUpdateManyWithoutTrackNestedInput
  contributors?: Prisma.TrackContributorUpdateManyWithoutTrackNestedInput
  releases?: Prisma.ReleaseTrackUpdateManyWithoutTrackNestedInput
}

export type TrackUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  isrc?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  trackVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  recordingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingHolder?: Prisma.StringFieldUpdateOperationsInput | string
  genre?: Prisma.StringFieldUpdateOperationsInput | string
  subGenre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lyrics?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  previewStart?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  previewLength?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metadataLanguage?: Prisma.StringFieldUpdateOperationsInput | string
  explicit?: Prisma.EnumExplicitContentFieldUpdateOperationsInput | $Enums.ExplicitContent
  audioLanguage?: Prisma.StringFieldUpdateOperationsInput | string
  rightsClaim?: Prisma.EnumRightsClaimFieldUpdateOperationsInput | $Enums.RightsClaim
  status?: Prisma.EnumTrackStatusFieldUpdateOperationsInput | $Enums.TrackStatus
  submittedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  readyAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  trackFiles?: Prisma.TrackFileUncheckedUpdateManyWithoutTrackNestedInput
  artists?: Prisma.TrackArtistUncheckedUpdateManyWithoutTrackNestedInput
  contributors?: Prisma.TrackContributorUncheckedUpdateManyWithoutTrackNestedInput
  releases?: Prisma.ReleaseTrackUncheckedUpdateManyWithoutTrackNestedInput
}

export type TrackCreateManyInput = {
  id?: string
  title: string
  isrc?: string | null
  trackVersion?: string | null
  recordingYear: number
  publishingYear: number
  publishingHolder: string
  genre: string
  subGenre?: string | null
  lyrics?: string | null
  previewStart?: string | null
  previewLength?: string | null
  metadataLanguage: string
  explicit: $Enums.ExplicitContent
  audioLanguage: string
  rightsClaim: $Enums.RightsClaim
  status?: $Enums.TrackStatus
  submittedAt?: Date | string | null
  readyAt?: Date | string | null
  userId: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type TrackUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  isrc?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  trackVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  recordingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingHolder?: Prisma.StringFieldUpdateOperationsInput | string
  genre?: Prisma.StringFieldUpdateOperationsInput | string
  subGenre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lyrics?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  previewStart?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  previewLength?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metadataLanguage?: Prisma.StringFieldUpdateOperationsInput | string
  explicit?: Prisma.EnumExplicitContentFieldUpdateOperationsInput | $Enums.ExplicitContent
  audioLanguage?: Prisma.StringFieldUpdateOperationsInput | string
  rightsClaim?: Prisma.EnumRightsClaimFieldUpdateOperationsInput | $Enums.RightsClaim
  status?: Prisma.EnumTrackStatusFieldUpdateOperationsInput | $Enums.TrackStatus
  submittedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  readyAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TrackUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  isrc?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  trackVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  recordingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingHolder?: Prisma.StringFieldUpdateOperationsInput | string
  genre?: Prisma.StringFieldUpdateOperationsInput | string
  subGenre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lyrics?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  previewStart?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  previewLength?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metadataLanguage?: Prisma.StringFieldUpdateOperationsInput | string
  explicit?: Prisma.EnumExplicitContentFieldUpdateOperationsInput | $Enums.ExplicitContent
  audioLanguage?: Prisma.StringFieldUpdateOperationsInput | string
  rightsClaim?: Prisma.EnumRightsClaimFieldUpdateOperationsInput | $Enums.RightsClaim
  status?: Prisma.EnumTrackStatusFieldUpdateOperationsInput | $Enums.TrackStatus
  submittedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  readyAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type TrackListRelationFilter = {
  every?: Prisma.TrackWhereInput
  some?: Prisma.TrackWhereInput
  none?: Prisma.TrackWhereInput
}

export type TrackOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type TrackScalarRelationFilter = {
  is?: Prisma.TrackWhereInput
  isNot?: Prisma.TrackWhereInput
}

export type TrackCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  isrc?: Prisma.SortOrder
  trackVersion?: Prisma.SortOrder
  recordingYear?: Prisma.SortOrder
  publishingYear?: Prisma.SortOrder
  publishingHolder?: Prisma.SortOrder
  genre?: Prisma.SortOrder
  subGenre?: Prisma.SortOrder
  lyrics?: Prisma.SortOrder
  previewStart?: Prisma.SortOrder
  previewLength?: Prisma.SortOrder
  metadataLanguage?: Prisma.SortOrder
  explicit?: Prisma.SortOrder
  audioLanguage?: Prisma.SortOrder
  rightsClaim?: Prisma.SortOrder
  status?: Prisma.SortOrder
  submittedAt?: Prisma.SortOrder
  readyAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type TrackAvgOrderByAggregateInput = {
  recordingYear?: Prisma.SortOrder
  publishingYear?: Prisma.SortOrder
}

export type TrackMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  isrc?: Prisma.SortOrder
  trackVersion?: Prisma.SortOrder
  recordingYear?: Prisma.SortOrder
  publishingYear?: Prisma.SortOrder
  publishingHolder?: Prisma.SortOrder
  genre?: Prisma.SortOrder
  subGenre?: Prisma.SortOrder
  lyrics?: Prisma.SortOrder
  previewStart?: Prisma.SortOrder
  previewLength?: Prisma.SortOrder
  metadataLanguage?: Prisma.SortOrder
  explicit?: Prisma.SortOrder
  audioLanguage?: Prisma.SortOrder
  rightsClaim?: Prisma.SortOrder
  status?: Prisma.SortOrder
  submittedAt?: Prisma.SortOrder
  readyAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type TrackMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  title?: Prisma.SortOrder
  isrc?: Prisma.SortOrder
  trackVersion?: Prisma.SortOrder
  recordingYear?: Prisma.SortOrder
  publishingYear?: Prisma.SortOrder
  publishingHolder?: Prisma.SortOrder
  genre?: Prisma.SortOrder
  subGenre?: Prisma.SortOrder
  lyrics?: Prisma.SortOrder
  previewStart?: Prisma.SortOrder
  previewLength?: Prisma.SortOrder
  metadataLanguage?: Prisma.SortOrder
  explicit?: Prisma.SortOrder
  audioLanguage?: Prisma.SortOrder
  rightsClaim?: Prisma.SortOrder
  status?: Prisma.SortOrder
  submittedAt?: Prisma.SortOrder
  readyAt?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type TrackSumOrderByAggregateInput = {
  recordingYear?: Prisma.SortOrder
  publishingYear?: Prisma.SortOrder
}

export type TrackCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.TrackCreateWithoutUserInput, Prisma.TrackUncheckedCreateWithoutUserInput> | Prisma.TrackCreateWithoutUserInput[] | Prisma.TrackUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.TrackCreateOrConnectWithoutUserInput | Prisma.TrackCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.TrackCreateManyUserInputEnvelope
  connect?: Prisma.TrackWhereUniqueInput | Prisma.TrackWhereUniqueInput[]
}

export type TrackUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.TrackCreateWithoutUserInput, Prisma.TrackUncheckedCreateWithoutUserInput> | Prisma.TrackCreateWithoutUserInput[] | Prisma.TrackUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.TrackCreateOrConnectWithoutUserInput | Prisma.TrackCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.TrackCreateManyUserInputEnvelope
  connect?: Prisma.TrackWhereUniqueInput | Prisma.TrackWhereUniqueInput[]
}

export type TrackUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.TrackCreateWithoutUserInput, Prisma.TrackUncheckedCreateWithoutUserInput> | Prisma.TrackCreateWithoutUserInput[] | Prisma.TrackUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.TrackCreateOrConnectWithoutUserInput | Prisma.TrackCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.TrackUpsertWithWhereUniqueWithoutUserInput | Prisma.TrackUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.TrackCreateManyUserInputEnvelope
  set?: Prisma.TrackWhereUniqueInput | Prisma.TrackWhereUniqueInput[]
  disconnect?: Prisma.TrackWhereUniqueInput | Prisma.TrackWhereUniqueInput[]
  delete?: Prisma.TrackWhereUniqueInput | Prisma.TrackWhereUniqueInput[]
  connect?: Prisma.TrackWhereUniqueInput | Prisma.TrackWhereUniqueInput[]
  update?: Prisma.TrackUpdateWithWhereUniqueWithoutUserInput | Prisma.TrackUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.TrackUpdateManyWithWhereWithoutUserInput | Prisma.TrackUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.TrackScalarWhereInput | Prisma.TrackScalarWhereInput[]
}

export type TrackUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.TrackCreateWithoutUserInput, Prisma.TrackUncheckedCreateWithoutUserInput> | Prisma.TrackCreateWithoutUserInput[] | Prisma.TrackUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.TrackCreateOrConnectWithoutUserInput | Prisma.TrackCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.TrackUpsertWithWhereUniqueWithoutUserInput | Prisma.TrackUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.TrackCreateManyUserInputEnvelope
  set?: Prisma.TrackWhereUniqueInput | Prisma.TrackWhereUniqueInput[]
  disconnect?: Prisma.TrackWhereUniqueInput | Prisma.TrackWhereUniqueInput[]
  delete?: Prisma.TrackWhereUniqueInput | Prisma.TrackWhereUniqueInput[]
  connect?: Prisma.TrackWhereUniqueInput | Prisma.TrackWhereUniqueInput[]
  update?: Prisma.TrackUpdateWithWhereUniqueWithoutUserInput | Prisma.TrackUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.TrackUpdateManyWithWhereWithoutUserInput | Prisma.TrackUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.TrackScalarWhereInput | Prisma.TrackScalarWhereInput[]
}

export type TrackCreateNestedOneWithoutReleasesInput = {
  create?: Prisma.XOR<Prisma.TrackCreateWithoutReleasesInput, Prisma.TrackUncheckedCreateWithoutReleasesInput>
  connectOrCreate?: Prisma.TrackCreateOrConnectWithoutReleasesInput
  connect?: Prisma.TrackWhereUniqueInput
}

export type TrackUpdateOneRequiredWithoutReleasesNestedInput = {
  create?: Prisma.XOR<Prisma.TrackCreateWithoutReleasesInput, Prisma.TrackUncheckedCreateWithoutReleasesInput>
  connectOrCreate?: Prisma.TrackCreateOrConnectWithoutReleasesInput
  upsert?: Prisma.TrackUpsertWithoutReleasesInput
  connect?: Prisma.TrackWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.TrackUpdateToOneWithWhereWithoutReleasesInput, Prisma.TrackUpdateWithoutReleasesInput>, Prisma.TrackUncheckedUpdateWithoutReleasesInput>
}

export type EnumRightsClaimFieldUpdateOperationsInput = {
  set?: $Enums.RightsClaim
}

export type EnumTrackStatusFieldUpdateOperationsInput = {
  set?: $Enums.TrackStatus
}

export type TrackCreateNestedOneWithoutArtistsInput = {
  create?: Prisma.XOR<Prisma.TrackCreateWithoutArtistsInput, Prisma.TrackUncheckedCreateWithoutArtistsInput>
  connectOrCreate?: Prisma.TrackCreateOrConnectWithoutArtistsInput
  connect?: Prisma.TrackWhereUniqueInput
}

export type TrackUpdateOneRequiredWithoutArtistsNestedInput = {
  create?: Prisma.XOR<Prisma.TrackCreateWithoutArtistsInput, Prisma.TrackUncheckedCreateWithoutArtistsInput>
  connectOrCreate?: Prisma.TrackCreateOrConnectWithoutArtistsInput
  upsert?: Prisma.TrackUpsertWithoutArtistsInput
  connect?: Prisma.TrackWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.TrackUpdateToOneWithWhereWithoutArtistsInput, Prisma.TrackUpdateWithoutArtistsInput>, Prisma.TrackUncheckedUpdateWithoutArtistsInput>
}

export type TrackCreateNestedOneWithoutContributorsInput = {
  create?: Prisma.XOR<Prisma.TrackCreateWithoutContributorsInput, Prisma.TrackUncheckedCreateWithoutContributorsInput>
  connectOrCreate?: Prisma.TrackCreateOrConnectWithoutContributorsInput
  connect?: Prisma.TrackWhereUniqueInput
}

export type TrackUpdateOneRequiredWithoutContributorsNestedInput = {
  create?: Prisma.XOR<Prisma.TrackCreateWithoutContributorsInput, Prisma.TrackUncheckedCreateWithoutContributorsInput>
  connectOrCreate?: Prisma.TrackCreateOrConnectWithoutContributorsInput
  upsert?: Prisma.TrackUpsertWithoutContributorsInput
  connect?: Prisma.TrackWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.TrackUpdateToOneWithWhereWithoutContributorsInput, Prisma.TrackUpdateWithoutContributorsInput>, Prisma.TrackUncheckedUpdateWithoutContributorsInput>
}

export type TrackCreateNestedOneWithoutTrackFilesInput = {
  create?: Prisma.XOR<Prisma.TrackCreateWithoutTrackFilesInput, Prisma.TrackUncheckedCreateWithoutTrackFilesInput>
  connectOrCreate?: Prisma.TrackCreateOrConnectWithoutTrackFilesInput
  connect?: Prisma.TrackWhereUniqueInput
}

export type TrackUpdateOneRequiredWithoutTrackFilesNestedInput = {
  create?: Prisma.XOR<Prisma.TrackCreateWithoutTrackFilesInput, Prisma.TrackUncheckedCreateWithoutTrackFilesInput>
  connectOrCreate?: Prisma.TrackCreateOrConnectWithoutTrackFilesInput
  upsert?: Prisma.TrackUpsertWithoutTrackFilesInput
  connect?: Prisma.TrackWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.TrackUpdateToOneWithWhereWithoutTrackFilesInput, Prisma.TrackUpdateWithoutTrackFilesInput>, Prisma.TrackUncheckedUpdateWithoutTrackFilesInput>
}

export type TrackCreateWithoutUserInput = {
  id?: string
  title: string
  isrc?: string | null
  trackVersion?: string | null
  recordingYear: number
  publishingYear: number
  publishingHolder: string
  genre: string
  subGenre?: string | null
  lyrics?: string | null
  previewStart?: string | null
  previewLength?: string | null
  metadataLanguage: string
  explicit: $Enums.ExplicitContent
  audioLanguage: string
  rightsClaim: $Enums.RightsClaim
  status?: $Enums.TrackStatus
  submittedAt?: Date | string | null
  readyAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  trackFiles?: Prisma.TrackFileCreateNestedManyWithoutTrackInput
  artists?: Prisma.TrackArtistCreateNestedManyWithoutTrackInput
  contributors?: Prisma.TrackContributorCreateNestedManyWithoutTrackInput
  releases?: Prisma.ReleaseTrackCreateNestedManyWithoutTrackInput
}

export type TrackUncheckedCreateWithoutUserInput = {
  id?: string
  title: string
  isrc?: string | null
  trackVersion?: string | null
  recordingYear: number
  publishingYear: number
  publishingHolder: string
  genre: string
  subGenre?: string | null
  lyrics?: string | null
  previewStart?: string | null
  previewLength?: string | null
  metadataLanguage: string
  explicit: $Enums.ExplicitContent
  audioLanguage: string
  rightsClaim: $Enums.RightsClaim
  status?: $Enums.TrackStatus
  submittedAt?: Date | string | null
  readyAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  trackFiles?: Prisma.TrackFileUncheckedCreateNestedManyWithoutTrackInput
  artists?: Prisma.TrackArtistUncheckedCreateNestedManyWithoutTrackInput
  contributors?: Prisma.TrackContributorUncheckedCreateNestedManyWithoutTrackInput
  releases?: Prisma.ReleaseTrackUncheckedCreateNestedManyWithoutTrackInput
}

export type TrackCreateOrConnectWithoutUserInput = {
  where: Prisma.TrackWhereUniqueInput
  create: Prisma.XOR<Prisma.TrackCreateWithoutUserInput, Prisma.TrackUncheckedCreateWithoutUserInput>
}

export type TrackCreateManyUserInputEnvelope = {
  data: Prisma.TrackCreateManyUserInput | Prisma.TrackCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type TrackUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.TrackWhereUniqueInput
  update: Prisma.XOR<Prisma.TrackUpdateWithoutUserInput, Prisma.TrackUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.TrackCreateWithoutUserInput, Prisma.TrackUncheckedCreateWithoutUserInput>
}

export type TrackUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.TrackWhereUniqueInput
  data: Prisma.XOR<Prisma.TrackUpdateWithoutUserInput, Prisma.TrackUncheckedUpdateWithoutUserInput>
}

export type TrackUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.TrackScalarWhereInput
  data: Prisma.XOR<Prisma.TrackUpdateManyMutationInput, Prisma.TrackUncheckedUpdateManyWithoutUserInput>
}

export type TrackScalarWhereInput = {
  AND?: Prisma.TrackScalarWhereInput | Prisma.TrackScalarWhereInput[]
  OR?: Prisma.TrackScalarWhereInput[]
  NOT?: Prisma.TrackScalarWhereInput | Prisma.TrackScalarWhereInput[]
  id?: Prisma.StringFilter<"Track"> | string
  title?: Prisma.StringFilter<"Track"> | string
  isrc?: Prisma.StringNullableFilter<"Track"> | string | null
  trackVersion?: Prisma.StringNullableFilter<"Track"> | string | null
  recordingYear?: Prisma.IntFilter<"Track"> | number
  publishingYear?: Prisma.IntFilter<"Track"> | number
  publishingHolder?: Prisma.StringFilter<"Track"> | string
  genre?: Prisma.StringFilter<"Track"> | string
  subGenre?: Prisma.StringNullableFilter<"Track"> | string | null
  lyrics?: Prisma.StringNullableFilter<"Track"> | string | null
  previewStart?: Prisma.StringNullableFilter<"Track"> | string | null
  previewLength?: Prisma.StringNullableFilter<"Track"> | string | null
  metadataLanguage?: Prisma.StringFilter<"Track"> | string
  explicit?: Prisma.EnumExplicitContentFilter<"Track"> | $Enums.ExplicitContent
  audioLanguage?: Prisma.StringFilter<"Track"> | string
  rightsClaim?: Prisma.EnumRightsClaimFilter<"Track"> | $Enums.RightsClaim
  status?: Prisma.EnumTrackStatusFilter<"Track"> | $Enums.TrackStatus
  submittedAt?: Prisma.DateTimeNullableFilter<"Track"> | Date | string | null
  readyAt?: Prisma.DateTimeNullableFilter<"Track"> | Date | string | null
  userId?: Prisma.StringFilter<"Track"> | string
  createdAt?: Prisma.DateTimeFilter<"Track"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Track"> | Date | string
}

export type TrackCreateWithoutReleasesInput = {
  id?: string
  title: string
  isrc?: string | null
  trackVersion?: string | null
  recordingYear: number
  publishingYear: number
  publishingHolder: string
  genre: string
  subGenre?: string | null
  lyrics?: string | null
  previewStart?: string | null
  previewLength?: string | null
  metadataLanguage: string
  explicit: $Enums.ExplicitContent
  audioLanguage: string
  rightsClaim: $Enums.RightsClaim
  status?: $Enums.TrackStatus
  submittedAt?: Date | string | null
  readyAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  trackFiles?: Prisma.TrackFileCreateNestedManyWithoutTrackInput
  user: Prisma.UserCreateNestedOneWithoutTracksInput
  artists?: Prisma.TrackArtistCreateNestedManyWithoutTrackInput
  contributors?: Prisma.TrackContributorCreateNestedManyWithoutTrackInput
}

export type TrackUncheckedCreateWithoutReleasesInput = {
  id?: string
  title: string
  isrc?: string | null
  trackVersion?: string | null
  recordingYear: number
  publishingYear: number
  publishingHolder: string
  genre: string
  subGenre?: string | null
  lyrics?: string | null
  previewStart?: string | null
  previewLength?: string | null
  metadataLanguage: string
  explicit: $Enums.ExplicitContent
  audioLanguage: string
  rightsClaim: $Enums.RightsClaim
  status?: $Enums.TrackStatus
  submittedAt?: Date | string | null
  readyAt?: Date | string | null
  userId: string
  createdAt?: Date | string
  updatedAt?: Date | string
  trackFiles?: Prisma.TrackFileUncheckedCreateNestedManyWithoutTrackInput
  artists?: Prisma.TrackArtistUncheckedCreateNestedManyWithoutTrackInput
  contributors?: Prisma.TrackContributorUncheckedCreateNestedManyWithoutTrackInput
}

export type TrackCreateOrConnectWithoutReleasesInput = {
  where: Prisma.TrackWhereUniqueInput
  create: Prisma.XOR<Prisma.TrackCreateWithoutReleasesInput, Prisma.TrackUncheckedCreateWithoutReleasesInput>
}

export type TrackUpsertWithoutReleasesInput = {
  update: Prisma.XOR<Prisma.TrackUpdateWithoutReleasesInput, Prisma.TrackUncheckedUpdateWithoutReleasesInput>
  create: Prisma.XOR<Prisma.TrackCreateWithoutReleasesInput, Prisma.TrackUncheckedCreateWithoutReleasesInput>
  where?: Prisma.TrackWhereInput
}

export type TrackUpdateToOneWithWhereWithoutReleasesInput = {
  where?: Prisma.TrackWhereInput
  data: Prisma.XOR<Prisma.TrackUpdateWithoutReleasesInput, Prisma.TrackUncheckedUpdateWithoutReleasesInput>
}

export type TrackUpdateWithoutReleasesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  isrc?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  trackVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  recordingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingHolder?: Prisma.StringFieldUpdateOperationsInput | string
  genre?: Prisma.StringFieldUpdateOperationsInput | string
  subGenre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lyrics?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  previewStart?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  previewLength?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metadataLanguage?: Prisma.StringFieldUpdateOperationsInput | string
  explicit?: Prisma.EnumExplicitContentFieldUpdateOperationsInput | $Enums.ExplicitContent
  audioLanguage?: Prisma.StringFieldUpdateOperationsInput | string
  rightsClaim?: Prisma.EnumRightsClaimFieldUpdateOperationsInput | $Enums.RightsClaim
  status?: Prisma.EnumTrackStatusFieldUpdateOperationsInput | $Enums.TrackStatus
  submittedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  readyAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  trackFiles?: Prisma.TrackFileUpdateManyWithoutTrackNestedInput
  user?: Prisma.UserUpdateOneRequiredWithoutTracksNestedInput
  artists?: Prisma.TrackArtistUpdateManyWithoutTrackNestedInput
  contributors?: Prisma.TrackContributorUpdateManyWithoutTrackNestedInput
}

export type TrackUncheckedUpdateWithoutReleasesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  isrc?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  trackVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  recordingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingHolder?: Prisma.StringFieldUpdateOperationsInput | string
  genre?: Prisma.StringFieldUpdateOperationsInput | string
  subGenre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lyrics?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  previewStart?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  previewLength?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metadataLanguage?: Prisma.StringFieldUpdateOperationsInput | string
  explicit?: Prisma.EnumExplicitContentFieldUpdateOperationsInput | $Enums.ExplicitContent
  audioLanguage?: Prisma.StringFieldUpdateOperationsInput | string
  rightsClaim?: Prisma.EnumRightsClaimFieldUpdateOperationsInput | $Enums.RightsClaim
  status?: Prisma.EnumTrackStatusFieldUpdateOperationsInput | $Enums.TrackStatus
  submittedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  readyAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  trackFiles?: Prisma.TrackFileUncheckedUpdateManyWithoutTrackNestedInput
  artists?: Prisma.TrackArtistUncheckedUpdateManyWithoutTrackNestedInput
  contributors?: Prisma.TrackContributorUncheckedUpdateManyWithoutTrackNestedInput
}

export type TrackCreateWithoutArtistsInput = {
  id?: string
  title: string
  isrc?: string | null
  trackVersion?: string | null
  recordingYear: number
  publishingYear: number
  publishingHolder: string
  genre: string
  subGenre?: string | null
  lyrics?: string | null
  previewStart?: string | null
  previewLength?: string | null
  metadataLanguage: string
  explicit: $Enums.ExplicitContent
  audioLanguage: string
  rightsClaim: $Enums.RightsClaim
  status?: $Enums.TrackStatus
  submittedAt?: Date | string | null
  readyAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  trackFiles?: Prisma.TrackFileCreateNestedManyWithoutTrackInput
  user: Prisma.UserCreateNestedOneWithoutTracksInput
  contributors?: Prisma.TrackContributorCreateNestedManyWithoutTrackInput
  releases?: Prisma.ReleaseTrackCreateNestedManyWithoutTrackInput
}

export type TrackUncheckedCreateWithoutArtistsInput = {
  id?: string
  title: string
  isrc?: string | null
  trackVersion?: string | null
  recordingYear: number
  publishingYear: number
  publishingHolder: string
  genre: string
  subGenre?: string | null
  lyrics?: string | null
  previewStart?: string | null
  previewLength?: string | null
  metadataLanguage: string
  explicit: $Enums.ExplicitContent
  audioLanguage: string
  rightsClaim: $Enums.RightsClaim
  status?: $Enums.TrackStatus
  submittedAt?: Date | string | null
  readyAt?: Date | string | null
  userId: string
  createdAt?: Date | string
  updatedAt?: Date | string
  trackFiles?: Prisma.TrackFileUncheckedCreateNestedManyWithoutTrackInput
  contributors?: Prisma.TrackContributorUncheckedCreateNestedManyWithoutTrackInput
  releases?: Prisma.ReleaseTrackUncheckedCreateNestedManyWithoutTrackInput
}

export type TrackCreateOrConnectWithoutArtistsInput = {
  where: Prisma.TrackWhereUniqueInput
  create: Prisma.XOR<Prisma.TrackCreateWithoutArtistsInput, Prisma.TrackUncheckedCreateWithoutArtistsInput>
}

export type TrackUpsertWithoutArtistsInput = {
  update: Prisma.XOR<Prisma.TrackUpdateWithoutArtistsInput, Prisma.TrackUncheckedUpdateWithoutArtistsInput>
  create: Prisma.XOR<Prisma.TrackCreateWithoutArtistsInput, Prisma.TrackUncheckedCreateWithoutArtistsInput>
  where?: Prisma.TrackWhereInput
}

export type TrackUpdateToOneWithWhereWithoutArtistsInput = {
  where?: Prisma.TrackWhereInput
  data: Prisma.XOR<Prisma.TrackUpdateWithoutArtistsInput, Prisma.TrackUncheckedUpdateWithoutArtistsInput>
}

export type TrackUpdateWithoutArtistsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  isrc?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  trackVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  recordingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingHolder?: Prisma.StringFieldUpdateOperationsInput | string
  genre?: Prisma.StringFieldUpdateOperationsInput | string
  subGenre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lyrics?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  previewStart?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  previewLength?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metadataLanguage?: Prisma.StringFieldUpdateOperationsInput | string
  explicit?: Prisma.EnumExplicitContentFieldUpdateOperationsInput | $Enums.ExplicitContent
  audioLanguage?: Prisma.StringFieldUpdateOperationsInput | string
  rightsClaim?: Prisma.EnumRightsClaimFieldUpdateOperationsInput | $Enums.RightsClaim
  status?: Prisma.EnumTrackStatusFieldUpdateOperationsInput | $Enums.TrackStatus
  submittedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  readyAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  trackFiles?: Prisma.TrackFileUpdateManyWithoutTrackNestedInput
  user?: Prisma.UserUpdateOneRequiredWithoutTracksNestedInput
  contributors?: Prisma.TrackContributorUpdateManyWithoutTrackNestedInput
  releases?: Prisma.ReleaseTrackUpdateManyWithoutTrackNestedInput
}

export type TrackUncheckedUpdateWithoutArtistsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  isrc?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  trackVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  recordingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingHolder?: Prisma.StringFieldUpdateOperationsInput | string
  genre?: Prisma.StringFieldUpdateOperationsInput | string
  subGenre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lyrics?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  previewStart?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  previewLength?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metadataLanguage?: Prisma.StringFieldUpdateOperationsInput | string
  explicit?: Prisma.EnumExplicitContentFieldUpdateOperationsInput | $Enums.ExplicitContent
  audioLanguage?: Prisma.StringFieldUpdateOperationsInput | string
  rightsClaim?: Prisma.EnumRightsClaimFieldUpdateOperationsInput | $Enums.RightsClaim
  status?: Prisma.EnumTrackStatusFieldUpdateOperationsInput | $Enums.TrackStatus
  submittedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  readyAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  trackFiles?: Prisma.TrackFileUncheckedUpdateManyWithoutTrackNestedInput
  contributors?: Prisma.TrackContributorUncheckedUpdateManyWithoutTrackNestedInput
  releases?: Prisma.ReleaseTrackUncheckedUpdateManyWithoutTrackNestedInput
}

export type TrackCreateWithoutContributorsInput = {
  id?: string
  title: string
  isrc?: string | null
  trackVersion?: string | null
  recordingYear: number
  publishingYear: number
  publishingHolder: string
  genre: string
  subGenre?: string | null
  lyrics?: string | null
  previewStart?: string | null
  previewLength?: string | null
  metadataLanguage: string
  explicit: $Enums.ExplicitContent
  audioLanguage: string
  rightsClaim: $Enums.RightsClaim
  status?: $Enums.TrackStatus
  submittedAt?: Date | string | null
  readyAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  trackFiles?: Prisma.TrackFileCreateNestedManyWithoutTrackInput
  user: Prisma.UserCreateNestedOneWithoutTracksInput
  artists?: Prisma.TrackArtistCreateNestedManyWithoutTrackInput
  releases?: Prisma.ReleaseTrackCreateNestedManyWithoutTrackInput
}

export type TrackUncheckedCreateWithoutContributorsInput = {
  id?: string
  title: string
  isrc?: string | null
  trackVersion?: string | null
  recordingYear: number
  publishingYear: number
  publishingHolder: string
  genre: string
  subGenre?: string | null
  lyrics?: string | null
  previewStart?: string | null
  previewLength?: string | null
  metadataLanguage: string
  explicit: $Enums.ExplicitContent
  audioLanguage: string
  rightsClaim: $Enums.RightsClaim
  status?: $Enums.TrackStatus
  submittedAt?: Date | string | null
  readyAt?: Date | string | null
  userId: string
  createdAt?: Date | string
  updatedAt?: Date | string
  trackFiles?: Prisma.TrackFileUncheckedCreateNestedManyWithoutTrackInput
  artists?: Prisma.TrackArtistUncheckedCreateNestedManyWithoutTrackInput
  releases?: Prisma.ReleaseTrackUncheckedCreateNestedManyWithoutTrackInput
}

export type TrackCreateOrConnectWithoutContributorsInput = {
  where: Prisma.TrackWhereUniqueInput
  create: Prisma.XOR<Prisma.TrackCreateWithoutContributorsInput, Prisma.TrackUncheckedCreateWithoutContributorsInput>
}

export type TrackUpsertWithoutContributorsInput = {
  update: Prisma.XOR<Prisma.TrackUpdateWithoutContributorsInput, Prisma.TrackUncheckedUpdateWithoutContributorsInput>
  create: Prisma.XOR<Prisma.TrackCreateWithoutContributorsInput, Prisma.TrackUncheckedCreateWithoutContributorsInput>
  where?: Prisma.TrackWhereInput
}

export type TrackUpdateToOneWithWhereWithoutContributorsInput = {
  where?: Prisma.TrackWhereInput
  data: Prisma.XOR<Prisma.TrackUpdateWithoutContributorsInput, Prisma.TrackUncheckedUpdateWithoutContributorsInput>
}

export type TrackUpdateWithoutContributorsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  isrc?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  trackVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  recordingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingHolder?: Prisma.StringFieldUpdateOperationsInput | string
  genre?: Prisma.StringFieldUpdateOperationsInput | string
  subGenre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lyrics?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  previewStart?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  previewLength?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metadataLanguage?: Prisma.StringFieldUpdateOperationsInput | string
  explicit?: Prisma.EnumExplicitContentFieldUpdateOperationsInput | $Enums.ExplicitContent
  audioLanguage?: Prisma.StringFieldUpdateOperationsInput | string
  rightsClaim?: Prisma.EnumRightsClaimFieldUpdateOperationsInput | $Enums.RightsClaim
  status?: Prisma.EnumTrackStatusFieldUpdateOperationsInput | $Enums.TrackStatus
  submittedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  readyAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  trackFiles?: Prisma.TrackFileUpdateManyWithoutTrackNestedInput
  user?: Prisma.UserUpdateOneRequiredWithoutTracksNestedInput
  artists?: Prisma.TrackArtistUpdateManyWithoutTrackNestedInput
  releases?: Prisma.ReleaseTrackUpdateManyWithoutTrackNestedInput
}

export type TrackUncheckedUpdateWithoutContributorsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  isrc?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  trackVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  recordingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingHolder?: Prisma.StringFieldUpdateOperationsInput | string
  genre?: Prisma.StringFieldUpdateOperationsInput | string
  subGenre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lyrics?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  previewStart?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  previewLength?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metadataLanguage?: Prisma.StringFieldUpdateOperationsInput | string
  explicit?: Prisma.EnumExplicitContentFieldUpdateOperationsInput | $Enums.ExplicitContent
  audioLanguage?: Prisma.StringFieldUpdateOperationsInput | string
  rightsClaim?: Prisma.EnumRightsClaimFieldUpdateOperationsInput | $Enums.RightsClaim
  status?: Prisma.EnumTrackStatusFieldUpdateOperationsInput | $Enums.TrackStatus
  submittedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  readyAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  trackFiles?: Prisma.TrackFileUncheckedUpdateManyWithoutTrackNestedInput
  artists?: Prisma.TrackArtistUncheckedUpdateManyWithoutTrackNestedInput
  releases?: Prisma.ReleaseTrackUncheckedUpdateManyWithoutTrackNestedInput
}

export type TrackCreateWithoutTrackFilesInput = {
  id?: string
  title: string
  isrc?: string | null
  trackVersion?: string | null
  recordingYear: number
  publishingYear: number
  publishingHolder: string
  genre: string
  subGenre?: string | null
  lyrics?: string | null
  previewStart?: string | null
  previewLength?: string | null
  metadataLanguage: string
  explicit: $Enums.ExplicitContent
  audioLanguage: string
  rightsClaim: $Enums.RightsClaim
  status?: $Enums.TrackStatus
  submittedAt?: Date | string | null
  readyAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutTracksInput
  artists?: Prisma.TrackArtistCreateNestedManyWithoutTrackInput
  contributors?: Prisma.TrackContributorCreateNestedManyWithoutTrackInput
  releases?: Prisma.ReleaseTrackCreateNestedManyWithoutTrackInput
}

export type TrackUncheckedCreateWithoutTrackFilesInput = {
  id?: string
  title: string
  isrc?: string | null
  trackVersion?: string | null
  recordingYear: number
  publishingYear: number
  publishingHolder: string
  genre: string
  subGenre?: string | null
  lyrics?: string | null
  previewStart?: string | null
  previewLength?: string | null
  metadataLanguage: string
  explicit: $Enums.ExplicitContent
  audioLanguage: string
  rightsClaim: $Enums.RightsClaim
  status?: $Enums.TrackStatus
  submittedAt?: Date | string | null
  readyAt?: Date | string | null
  userId: string
  createdAt?: Date | string
  updatedAt?: Date | string
  artists?: Prisma.TrackArtistUncheckedCreateNestedManyWithoutTrackInput
  contributors?: Prisma.TrackContributorUncheckedCreateNestedManyWithoutTrackInput
  releases?: Prisma.ReleaseTrackUncheckedCreateNestedManyWithoutTrackInput
}

export type TrackCreateOrConnectWithoutTrackFilesInput = {
  where: Prisma.TrackWhereUniqueInput
  create: Prisma.XOR<Prisma.TrackCreateWithoutTrackFilesInput, Prisma.TrackUncheckedCreateWithoutTrackFilesInput>
}

export type TrackUpsertWithoutTrackFilesInput = {
  update: Prisma.XOR<Prisma.TrackUpdateWithoutTrackFilesInput, Prisma.TrackUncheckedUpdateWithoutTrackFilesInput>
  create: Prisma.XOR<Prisma.TrackCreateWithoutTrackFilesInput, Prisma.TrackUncheckedCreateWithoutTrackFilesInput>
  where?: Prisma.TrackWhereInput
}

export type TrackUpdateToOneWithWhereWithoutTrackFilesInput = {
  where?: Prisma.TrackWhereInput
  data: Prisma.XOR<Prisma.TrackUpdateWithoutTrackFilesInput, Prisma.TrackUncheckedUpdateWithoutTrackFilesInput>
}

export type TrackUpdateWithoutTrackFilesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  isrc?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  trackVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  recordingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingHolder?: Prisma.StringFieldUpdateOperationsInput | string
  genre?: Prisma.StringFieldUpdateOperationsInput | string
  subGenre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lyrics?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  previewStart?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  previewLength?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metadataLanguage?: Prisma.StringFieldUpdateOperationsInput | string
  explicit?: Prisma.EnumExplicitContentFieldUpdateOperationsInput | $Enums.ExplicitContent
  audioLanguage?: Prisma.StringFieldUpdateOperationsInput | string
  rightsClaim?: Prisma.EnumRightsClaimFieldUpdateOperationsInput | $Enums.RightsClaim
  status?: Prisma.EnumTrackStatusFieldUpdateOperationsInput | $Enums.TrackStatus
  submittedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  readyAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutTracksNestedInput
  artists?: Prisma.TrackArtistUpdateManyWithoutTrackNestedInput
  contributors?: Prisma.TrackContributorUpdateManyWithoutTrackNestedInput
  releases?: Prisma.ReleaseTrackUpdateManyWithoutTrackNestedInput
}

export type TrackUncheckedUpdateWithoutTrackFilesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  isrc?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  trackVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  recordingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingHolder?: Prisma.StringFieldUpdateOperationsInput | string
  genre?: Prisma.StringFieldUpdateOperationsInput | string
  subGenre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lyrics?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  previewStart?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  previewLength?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metadataLanguage?: Prisma.StringFieldUpdateOperationsInput | string
  explicit?: Prisma.EnumExplicitContentFieldUpdateOperationsInput | $Enums.ExplicitContent
  audioLanguage?: Prisma.StringFieldUpdateOperationsInput | string
  rightsClaim?: Prisma.EnumRightsClaimFieldUpdateOperationsInput | $Enums.RightsClaim
  status?: Prisma.EnumTrackStatusFieldUpdateOperationsInput | $Enums.TrackStatus
  submittedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  readyAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  artists?: Prisma.TrackArtistUncheckedUpdateManyWithoutTrackNestedInput
  contributors?: Prisma.TrackContributorUncheckedUpdateManyWithoutTrackNestedInput
  releases?: Prisma.ReleaseTrackUncheckedUpdateManyWithoutTrackNestedInput
}

export type TrackCreateManyUserInput = {
  id?: string
  title: string
  isrc?: string | null
  trackVersion?: string | null
  recordingYear: number
  publishingYear: number
  publishingHolder: string
  genre: string
  subGenre?: string | null
  lyrics?: string | null
  previewStart?: string | null
  previewLength?: string | null
  metadataLanguage: string
  explicit: $Enums.ExplicitContent
  audioLanguage: string
  rightsClaim: $Enums.RightsClaim
  status?: $Enums.TrackStatus
  submittedAt?: Date | string | null
  readyAt?: Date | string | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type TrackUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  isrc?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  trackVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  recordingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingHolder?: Prisma.StringFieldUpdateOperationsInput | string
  genre?: Prisma.StringFieldUpdateOperationsInput | string
  subGenre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lyrics?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  previewStart?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  previewLength?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metadataLanguage?: Prisma.StringFieldUpdateOperationsInput | string
  explicit?: Prisma.EnumExplicitContentFieldUpdateOperationsInput | $Enums.ExplicitContent
  audioLanguage?: Prisma.StringFieldUpdateOperationsInput | string
  rightsClaim?: Prisma.EnumRightsClaimFieldUpdateOperationsInput | $Enums.RightsClaim
  status?: Prisma.EnumTrackStatusFieldUpdateOperationsInput | $Enums.TrackStatus
  submittedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  readyAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  trackFiles?: Prisma.TrackFileUpdateManyWithoutTrackNestedInput
  artists?: Prisma.TrackArtistUpdateManyWithoutTrackNestedInput
  contributors?: Prisma.TrackContributorUpdateManyWithoutTrackNestedInput
  releases?: Prisma.ReleaseTrackUpdateManyWithoutTrackNestedInput
}

export type TrackUncheckedUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  isrc?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  trackVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  recordingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingHolder?: Prisma.StringFieldUpdateOperationsInput | string
  genre?: Prisma.StringFieldUpdateOperationsInput | string
  subGenre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lyrics?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  previewStart?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  previewLength?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metadataLanguage?: Prisma.StringFieldUpdateOperationsInput | string
  explicit?: Prisma.EnumExplicitContentFieldUpdateOperationsInput | $Enums.ExplicitContent
  audioLanguage?: Prisma.StringFieldUpdateOperationsInput | string
  rightsClaim?: Prisma.EnumRightsClaimFieldUpdateOperationsInput | $Enums.RightsClaim
  status?: Prisma.EnumTrackStatusFieldUpdateOperationsInput | $Enums.TrackStatus
  submittedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  readyAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  trackFiles?: Prisma.TrackFileUncheckedUpdateManyWithoutTrackNestedInput
  artists?: Prisma.TrackArtistUncheckedUpdateManyWithoutTrackNestedInput
  contributors?: Prisma.TrackContributorUncheckedUpdateManyWithoutTrackNestedInput
  releases?: Prisma.ReleaseTrackUncheckedUpdateManyWithoutTrackNestedInput
}

export type TrackUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  title?: Prisma.StringFieldUpdateOperationsInput | string
  isrc?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  trackVersion?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  recordingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingYear?: Prisma.IntFieldUpdateOperationsInput | number
  publishingHolder?: Prisma.StringFieldUpdateOperationsInput | string
  genre?: Prisma.StringFieldUpdateOperationsInput | string
  subGenre?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  lyrics?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  previewStart?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  previewLength?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  metadataLanguage?: Prisma.StringFieldUpdateOperationsInput | string
  explicit?: Prisma.EnumExplicitContentFieldUpdateOperationsInput | $Enums.ExplicitContent
  audioLanguage?: Prisma.StringFieldUpdateOperationsInput | string
  rightsClaim?: Prisma.EnumRightsClaimFieldUpdateOperationsInput | $Enums.RightsClaim
  status?: Prisma.EnumTrackStatusFieldUpdateOperationsInput | $Enums.TrackStatus
  submittedAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  readyAt?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}


/**
 * Count Type TrackCountOutputType
 */

export type TrackCountOutputType = {
  trackFiles: number
  artists: number
  contributors: number
  releases: number
}

export type TrackCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  trackFiles?: boolean | TrackCountOutputTypeCountTrackFilesArgs
  artists?: boolean | TrackCountOutputTypeCountArtistsArgs
  contributors?: boolean | TrackCountOutputTypeCountContributorsArgs
  releases?: boolean | TrackCountOutputTypeCountReleasesArgs
}

/**
 * TrackCountOutputType without action
 */
export type TrackCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackCountOutputType
   */
  select?: Prisma.TrackCountOutputTypeSelect<ExtArgs> | null
}

/**
 * TrackCountOutputType without action
 */
export type TrackCountOutputTypeCountTrackFilesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.TrackFileWhereInput
}

/**
 * TrackCountOutputType without action
 */
export type TrackCountOutputTypeCountArtistsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.TrackArtistWhereInput
}

/**
 * TrackCountOutputType without action
 */
export type TrackCountOutputTypeCountContributorsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.TrackContributorWhereInput
}

/**
 * TrackCountOutputType without action
 */
export type TrackCountOutputTypeCountReleasesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ReleaseTrackWhereInput
}


export type TrackSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  title?: boolean
  isrc?: boolean
  trackVersion?: boolean
  recordingYear?: boolean
  publishingYear?: boolean
  publishingHolder?: boolean
  genre?: boolean
  subGenre?: boolean
  lyrics?: boolean
  previewStart?: boolean
  previewLength?: boolean
  metadataLanguage?: boolean
  explicit?: boolean
  audioLanguage?: boolean
  rightsClaim?: boolean
  status?: boolean
  submittedAt?: boolean
  readyAt?: boolean
  userId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  trackFiles?: boolean | Prisma.Track$trackFilesArgs<ExtArgs>
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  artists?: boolean | Prisma.Track$artistsArgs<ExtArgs>
  contributors?: boolean | Prisma.Track$contributorsArgs<ExtArgs>
  releases?: boolean | Prisma.Track$releasesArgs<ExtArgs>
  _count?: boolean | Prisma.TrackCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["track"]>

export type TrackSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  title?: boolean
  isrc?: boolean
  trackVersion?: boolean
  recordingYear?: boolean
  publishingYear?: boolean
  publishingHolder?: boolean
  genre?: boolean
  subGenre?: boolean
  lyrics?: boolean
  previewStart?: boolean
  previewLength?: boolean
  metadataLanguage?: boolean
  explicit?: boolean
  audioLanguage?: boolean
  rightsClaim?: boolean
  status?: boolean
  submittedAt?: boolean
  readyAt?: boolean
  userId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}, ExtArgs["result"]["track"]>

export type TrackSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  title?: boolean
  isrc?: boolean
  trackVersion?: boolean
  recordingYear?: boolean
  publishingYear?: boolean
  publishingHolder?: boolean
  genre?: boolean
  subGenre?: boolean
  lyrics?: boolean
  previewStart?: boolean
  previewLength?: boolean
  metadataLanguage?: boolean
  explicit?: boolean
  audioLanguage?: boolean
  rightsClaim?: boolean
  status?: boolean
  submittedAt?: boolean
  readyAt?: boolean
  userId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}, ExtArgs["result"]["track"]>

export type TrackSelectScalar = {
  id?: boolean
  title?: boolean
  isrc?: boolean
  trackVersion?: boolean
  recordingYear?: boolean
  publishingYear?: boolean
  publishingHolder?: boolean
  genre?: boolean
  subGenre?: boolean
  lyrics?: boolean
  previewStart?: boolean
  previewLength?: boolean
  metadataLanguage?: boolean
  explicit?: boolean
  audioLanguage?: boolean
  rightsClaim?: boolean
  status?: boolean
  submittedAt?: boolean
  readyAt?: boolean
  userId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type TrackOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "title" | "isrc" | "trackVersion" | "recordingYear" | "publishingYear" | "publishingHolder" | "genre" | "subGenre" | "lyrics" | "previewStart" | "previewLength" | "metadataLanguage" | "explicit" | "audioLanguage" | "rightsClaim" | "status" | "submittedAt" | "readyAt" | "userId" | "createdAt" | "updatedAt", ExtArgs["result"]["track"]>
export type TrackInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  trackFiles?: boolean | Prisma.Track$trackFilesArgs<ExtArgs>
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  artists?: boolean | Prisma.Track$artistsArgs<ExtArgs>
  contributors?: boolean | Prisma.Track$contributorsArgs<ExtArgs>
  releases?: boolean | Prisma.Track$releasesArgs<ExtArgs>
  _count?: boolean | Prisma.TrackCountOutputTypeDefaultArgs<ExtArgs>
}
export type TrackIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}
export type TrackIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}

export type $TrackPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Track"
  objects: {
    trackFiles: Prisma.$TrackFilePayload<ExtArgs>[]
    user: Prisma.$UserPayload<ExtArgs>
    artists: Prisma.$TrackArtistPayload<ExtArgs>[]
    contributors: Prisma.$TrackContributorPayload<ExtArgs>[]
    releases: Prisma.$ReleaseTrackPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    title: string
    isrc: string | null
    trackVersion: string | null
    recordingYear: number
    publishingYear: number
    publishingHolder: string
    genre: string
    subGenre: string | null
    lyrics: string | null
    previewStart: string | null
    previewLength: string | null
    metadataLanguage: string
    explicit: $Enums.ExplicitContent
    audioLanguage: string
    rightsClaim: $Enums.RightsClaim
    status: $Enums.TrackStatus
    submittedAt: Date | null
    readyAt: Date | null
    userId: string
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["track"]>
  composites: {}
}

export type TrackGetPayload<S extends boolean | null | undefined | TrackDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$TrackPayload, S>

export type TrackCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<TrackFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: TrackCountAggregateInputType | true
  }

export interface TrackDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Track'], meta: { name: 'Track' } }
  /**
   * Find zero or one Track that matches the filter.
   * @param {TrackFindUniqueArgs} args - Arguments to find a Track
   * @example
   * // Get one Track
   * const track = await prisma.track.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends TrackFindUniqueArgs>(args: Prisma.SelectSubset<T, TrackFindUniqueArgs<ExtArgs>>): Prisma.Prisma__TrackClient<runtime.Types.Result.GetResult<Prisma.$TrackPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Track that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {TrackFindUniqueOrThrowArgs} args - Arguments to find a Track
   * @example
   * // Get one Track
   * const track = await prisma.track.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends TrackFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, TrackFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__TrackClient<runtime.Types.Result.GetResult<Prisma.$TrackPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Track that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackFindFirstArgs} args - Arguments to find a Track
   * @example
   * // Get one Track
   * const track = await prisma.track.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends TrackFindFirstArgs>(args?: Prisma.SelectSubset<T, TrackFindFirstArgs<ExtArgs>>): Prisma.Prisma__TrackClient<runtime.Types.Result.GetResult<Prisma.$TrackPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Track that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackFindFirstOrThrowArgs} args - Arguments to find a Track
   * @example
   * // Get one Track
   * const track = await prisma.track.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends TrackFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, TrackFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__TrackClient<runtime.Types.Result.GetResult<Prisma.$TrackPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Tracks that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Tracks
   * const tracks = await prisma.track.findMany()
   * 
   * // Get first 10 Tracks
   * const tracks = await prisma.track.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const trackWithIdOnly = await prisma.track.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends TrackFindManyArgs>(args?: Prisma.SelectSubset<T, TrackFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TrackPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Track.
   * @param {TrackCreateArgs} args - Arguments to create a Track.
   * @example
   * // Create one Track
   * const Track = await prisma.track.create({
   *   data: {
   *     // ... data to create a Track
   *   }
   * })
   * 
   */
  create<T extends TrackCreateArgs>(args: Prisma.SelectSubset<T, TrackCreateArgs<ExtArgs>>): Prisma.Prisma__TrackClient<runtime.Types.Result.GetResult<Prisma.$TrackPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Tracks.
   * @param {TrackCreateManyArgs} args - Arguments to create many Tracks.
   * @example
   * // Create many Tracks
   * const track = await prisma.track.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends TrackCreateManyArgs>(args?: Prisma.SelectSubset<T, TrackCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Tracks and returns the data saved in the database.
   * @param {TrackCreateManyAndReturnArgs} args - Arguments to create many Tracks.
   * @example
   * // Create many Tracks
   * const track = await prisma.track.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Tracks and only return the `id`
   * const trackWithIdOnly = await prisma.track.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends TrackCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, TrackCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TrackPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a Track.
   * @param {TrackDeleteArgs} args - Arguments to delete one Track.
   * @example
   * // Delete one Track
   * const Track = await prisma.track.delete({
   *   where: {
   *     // ... filter to delete one Track
   *   }
   * })
   * 
   */
  delete<T extends TrackDeleteArgs>(args: Prisma.SelectSubset<T, TrackDeleteArgs<ExtArgs>>): Prisma.Prisma__TrackClient<runtime.Types.Result.GetResult<Prisma.$TrackPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Track.
   * @param {TrackUpdateArgs} args - Arguments to update one Track.
   * @example
   * // Update one Track
   * const track = await prisma.track.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends TrackUpdateArgs>(args: Prisma.SelectSubset<T, TrackUpdateArgs<ExtArgs>>): Prisma.Prisma__TrackClient<runtime.Types.Result.GetResult<Prisma.$TrackPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Tracks.
   * @param {TrackDeleteManyArgs} args - Arguments to filter Tracks to delete.
   * @example
   * // Delete a few Tracks
   * const { count } = await prisma.track.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends TrackDeleteManyArgs>(args?: Prisma.SelectSubset<T, TrackDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Tracks.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Tracks
   * const track = await prisma.track.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends TrackUpdateManyArgs>(args: Prisma.SelectSubset<T, TrackUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Tracks and returns the data updated in the database.
   * @param {TrackUpdateManyAndReturnArgs} args - Arguments to update many Tracks.
   * @example
   * // Update many Tracks
   * const track = await prisma.track.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Tracks and only return the `id`
   * const trackWithIdOnly = await prisma.track.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends TrackUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, TrackUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TrackPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one Track.
   * @param {TrackUpsertArgs} args - Arguments to update or create a Track.
   * @example
   * // Update or create a Track
   * const track = await prisma.track.upsert({
   *   create: {
   *     // ... data to create a Track
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Track we want to update
   *   }
   * })
   */
  upsert<T extends TrackUpsertArgs>(args: Prisma.SelectSubset<T, TrackUpsertArgs<ExtArgs>>): Prisma.Prisma__TrackClient<runtime.Types.Result.GetResult<Prisma.$TrackPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Tracks.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackCountArgs} args - Arguments to filter Tracks to count.
   * @example
   * // Count the number of Tracks
   * const count = await prisma.track.count({
   *   where: {
   *     // ... the filter for the Tracks we want to count
   *   }
   * })
  **/
  count<T extends TrackCountArgs>(
    args?: Prisma.Subset<T, TrackCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], TrackCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Track.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends TrackAggregateArgs>(args: Prisma.Subset<T, TrackAggregateArgs>): Prisma.PrismaPromise<GetTrackAggregateType<T>>

  /**
   * Group by Track.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends TrackGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: TrackGroupByArgs['orderBy'] }
      : { orderBy?: TrackGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, TrackGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetTrackGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Track model
 */
readonly fields: TrackFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Track.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__TrackClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  trackFiles<T extends Prisma.Track$trackFilesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Track$trackFilesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TrackFilePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  user<T extends Prisma.UserDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.UserDefaultArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  artists<T extends Prisma.Track$artistsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Track$artistsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TrackArtistPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  contributors<T extends Prisma.Track$contributorsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Track$contributorsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TrackContributorPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  releases<T extends Prisma.Track$releasesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Track$releasesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ReleaseTrackPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Track model
 */
export interface TrackFieldRefs {
  readonly id: Prisma.FieldRef<"Track", 'String'>
  readonly title: Prisma.FieldRef<"Track", 'String'>
  readonly isrc: Prisma.FieldRef<"Track", 'String'>
  readonly trackVersion: Prisma.FieldRef<"Track", 'String'>
  readonly recordingYear: Prisma.FieldRef<"Track", 'Int'>
  readonly publishingYear: Prisma.FieldRef<"Track", 'Int'>
  readonly publishingHolder: Prisma.FieldRef<"Track", 'String'>
  readonly genre: Prisma.FieldRef<"Track", 'String'>
  readonly subGenre: Prisma.FieldRef<"Track", 'String'>
  readonly lyrics: Prisma.FieldRef<"Track", 'String'>
  readonly previewStart: Prisma.FieldRef<"Track", 'String'>
  readonly previewLength: Prisma.FieldRef<"Track", 'String'>
  readonly metadataLanguage: Prisma.FieldRef<"Track", 'String'>
  readonly explicit: Prisma.FieldRef<"Track", 'ExplicitContent'>
  readonly audioLanguage: Prisma.FieldRef<"Track", 'String'>
  readonly rightsClaim: Prisma.FieldRef<"Track", 'RightsClaim'>
  readonly status: Prisma.FieldRef<"Track", 'TrackStatus'>
  readonly submittedAt: Prisma.FieldRef<"Track", 'DateTime'>
  readonly readyAt: Prisma.FieldRef<"Track", 'DateTime'>
  readonly userId: Prisma.FieldRef<"Track", 'String'>
  readonly createdAt: Prisma.FieldRef<"Track", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"Track", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Track findUnique
 */
export type TrackFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Track
   */
  select?: Prisma.TrackSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Track
   */
  omit?: Prisma.TrackOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackInclude<ExtArgs> | null
  /**
   * Filter, which Track to fetch.
   */
  where: Prisma.TrackWhereUniqueInput
}

/**
 * Track findUniqueOrThrow
 */
export type TrackFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Track
   */
  select?: Prisma.TrackSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Track
   */
  omit?: Prisma.TrackOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackInclude<ExtArgs> | null
  /**
   * Filter, which Track to fetch.
   */
  where: Prisma.TrackWhereUniqueInput
}

/**
 * Track findFirst
 */
export type TrackFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Track
   */
  select?: Prisma.TrackSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Track
   */
  omit?: Prisma.TrackOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackInclude<ExtArgs> | null
  /**
   * Filter, which Track to fetch.
   */
  where?: Prisma.TrackWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Tracks to fetch.
   */
  orderBy?: Prisma.TrackOrderByWithRelationInput | Prisma.TrackOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Tracks.
   */
  cursor?: Prisma.TrackWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Tracks from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Tracks.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Tracks.
   */
  distinct?: Prisma.TrackScalarFieldEnum | Prisma.TrackScalarFieldEnum[]
}

/**
 * Track findFirstOrThrow
 */
export type TrackFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Track
   */
  select?: Prisma.TrackSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Track
   */
  omit?: Prisma.TrackOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackInclude<ExtArgs> | null
  /**
   * Filter, which Track to fetch.
   */
  where?: Prisma.TrackWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Tracks to fetch.
   */
  orderBy?: Prisma.TrackOrderByWithRelationInput | Prisma.TrackOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Tracks.
   */
  cursor?: Prisma.TrackWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Tracks from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Tracks.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Tracks.
   */
  distinct?: Prisma.TrackScalarFieldEnum | Prisma.TrackScalarFieldEnum[]
}

/**
 * Track findMany
 */
export type TrackFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Track
   */
  select?: Prisma.TrackSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Track
   */
  omit?: Prisma.TrackOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackInclude<ExtArgs> | null
  /**
   * Filter, which Tracks to fetch.
   */
  where?: Prisma.TrackWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Tracks to fetch.
   */
  orderBy?: Prisma.TrackOrderByWithRelationInput | Prisma.TrackOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Tracks.
   */
  cursor?: Prisma.TrackWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Tracks from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Tracks.
   */
  skip?: number
  distinct?: Prisma.TrackScalarFieldEnum | Prisma.TrackScalarFieldEnum[]
}

/**
 * Track create
 */
export type TrackCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Track
   */
  select?: Prisma.TrackSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Track
   */
  omit?: Prisma.TrackOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackInclude<ExtArgs> | null
  /**
   * The data needed to create a Track.
   */
  data: Prisma.XOR<Prisma.TrackCreateInput, Prisma.TrackUncheckedCreateInput>
}

/**
 * Track createMany
 */
export type TrackCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Tracks.
   */
  data: Prisma.TrackCreateManyInput | Prisma.TrackCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Track createManyAndReturn
 */
export type TrackCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Track
   */
  select?: Prisma.TrackSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Track
   */
  omit?: Prisma.TrackOmit<ExtArgs> | null
  /**
   * The data used to create many Tracks.
   */
  data: Prisma.TrackCreateManyInput | Prisma.TrackCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * Track update
 */
export type TrackUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Track
   */
  select?: Prisma.TrackSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Track
   */
  omit?: Prisma.TrackOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackInclude<ExtArgs> | null
  /**
   * The data needed to update a Track.
   */
  data: Prisma.XOR<Prisma.TrackUpdateInput, Prisma.TrackUncheckedUpdateInput>
  /**
   * Choose, which Track to update.
   */
  where: Prisma.TrackWhereUniqueInput
}

/**
 * Track updateMany
 */
export type TrackUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Tracks.
   */
  data: Prisma.XOR<Prisma.TrackUpdateManyMutationInput, Prisma.TrackUncheckedUpdateManyInput>
  /**
   * Filter which Tracks to update
   */
  where?: Prisma.TrackWhereInput
  /**
   * Limit how many Tracks to update.
   */
  limit?: number
}

/**
 * Track updateManyAndReturn
 */
export type TrackUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Track
   */
  select?: Prisma.TrackSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Track
   */
  omit?: Prisma.TrackOmit<ExtArgs> | null
  /**
   * The data used to update Tracks.
   */
  data: Prisma.XOR<Prisma.TrackUpdateManyMutationInput, Prisma.TrackUncheckedUpdateManyInput>
  /**
   * Filter which Tracks to update
   */
  where?: Prisma.TrackWhereInput
  /**
   * Limit how many Tracks to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * Track upsert
 */
export type TrackUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Track
   */
  select?: Prisma.TrackSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Track
   */
  omit?: Prisma.TrackOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackInclude<ExtArgs> | null
  /**
   * The filter to search for the Track to update in case it exists.
   */
  where: Prisma.TrackWhereUniqueInput
  /**
   * In case the Track found by the `where` argument doesn't exist, create a new Track with this data.
   */
  create: Prisma.XOR<Prisma.TrackCreateInput, Prisma.TrackUncheckedCreateInput>
  /**
   * In case the Track was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.TrackUpdateInput, Prisma.TrackUncheckedUpdateInput>
}

/**
 * Track delete
 */
export type TrackDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Track
   */
  select?: Prisma.TrackSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Track
   */
  omit?: Prisma.TrackOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackInclude<ExtArgs> | null
  /**
   * Filter which Track to delete.
   */
  where: Prisma.TrackWhereUniqueInput
}

/**
 * Track deleteMany
 */
export type TrackDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Tracks to delete
   */
  where?: Prisma.TrackWhereInput
  /**
   * Limit how many Tracks to delete.
   */
  limit?: number
}

/**
 * Track.trackFiles
 */
export type Track$trackFilesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackFile
   */
  select?: Prisma.TrackFileSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackFile
   */
  omit?: Prisma.TrackFileOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackFileInclude<ExtArgs> | null
  where?: Prisma.TrackFileWhereInput
  orderBy?: Prisma.TrackFileOrderByWithRelationInput | Prisma.TrackFileOrderByWithRelationInput[]
  cursor?: Prisma.TrackFileWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.TrackFileScalarFieldEnum | Prisma.TrackFileScalarFieldEnum[]
}

/**
 * Track.artists
 */
export type Track$artistsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackArtist
   */
  select?: Prisma.TrackArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackArtist
   */
  omit?: Prisma.TrackArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackArtistInclude<ExtArgs> | null
  where?: Prisma.TrackArtistWhereInput
  orderBy?: Prisma.TrackArtistOrderByWithRelationInput | Prisma.TrackArtistOrderByWithRelationInput[]
  cursor?: Prisma.TrackArtistWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.TrackArtistScalarFieldEnum | Prisma.TrackArtistScalarFieldEnum[]
}

/**
 * Track.contributors
 */
export type Track$contributorsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackContributor
   */
  select?: Prisma.TrackContributorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackContributor
   */
  omit?: Prisma.TrackContributorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackContributorInclude<ExtArgs> | null
  where?: Prisma.TrackContributorWhereInput
  orderBy?: Prisma.TrackContributorOrderByWithRelationInput | Prisma.TrackContributorOrderByWithRelationInput[]
  cursor?: Prisma.TrackContributorWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.TrackContributorScalarFieldEnum | Prisma.TrackContributorScalarFieldEnum[]
}

/**
 * Track.releases
 */
export type Track$releasesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseTrack
   */
  select?: Prisma.ReleaseTrackSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseTrack
   */
  omit?: Prisma.ReleaseTrackOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseTrackInclude<ExtArgs> | null
  where?: Prisma.ReleaseTrackWhereInput
  orderBy?: Prisma.ReleaseTrackOrderByWithRelationInput | Prisma.ReleaseTrackOrderByWithRelationInput[]
  cursor?: Prisma.ReleaseTrackWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.ReleaseTrackScalarFieldEnum | Prisma.ReleaseTrackScalarFieldEnum[]
}

/**
 * Track without action
 */
export type TrackDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Track
   */
  select?: Prisma.TrackSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Track
   */
  omit?: Prisma.TrackOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackInclude<ExtArgs> | null
}
