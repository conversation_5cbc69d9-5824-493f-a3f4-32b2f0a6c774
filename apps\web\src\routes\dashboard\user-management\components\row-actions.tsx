import type { Row } from "@tanstack/react-table";
import {
  EllipsisIcon,
  TrashIcon,
  CircleAlertIcon,
  ShieldIcon,
} from "lucide-react";
import { toast } from "sonner";

import { authClient } from "@/lib/auth-client";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { BanUserDialog } from "./ban-user-dialog";
import { UserDetailsDialog } from "./user-details-dialog";
import type { UserTableItem } from "./types";

interface RowActionsProps {
  row: Row<UserTableItem>;
  onUserDeleted: () => void;
}

export function RowActions({ row, onUserDeleted }: RowActionsProps) {
  const handleDeleteUser = async () => {
    try {
      await authClient.admin.removeUser({
        userId: row.original.id,
      });

      toast.success(`Successfully deleted user ${row.original.name}`);

      // Refetch data to update the table
      onUserDeleted();
    } catch (error: any) {
      console.error("Failed to delete user:", error);
      toast.error(
        "Failed to delete user: " + (error.message || "Unknown error")
      );
    }
  };

  const handleRevokeAllSessions = async () => {
    try {
      await authClient.admin.revokeUserSessions({
        userId: row.original.id,
      });

      toast.success(
        `Successfully revoked all sessions for ${row.original.name}`
      );
    } catch (error: any) {
      console.error("Failed to revoke sessions:", error);
      toast.error(
        "Failed to revoke sessions: " + (error.message || "Unknown error")
      );
    }
  };

  const handleUnbanUser = async () => {
    try {
      await authClient.admin.unbanUser({
        userId: row.original.id,
      });

      toast.success(`Successfully unbanned ${row.original.name}`);

      // Refetch data to update the table
      onUserDeleted(); // Reusing this function since it just refetches data
    } catch (error: any) {
      console.error("Failed to unban user:", error);
      toast.error(
        "Failed to unban user: " + (error.message || "Unknown error")
      );
    }
  };

  const handleChangeRole = async () => {
    const currentRole = row.original.role || "user";
    const newRole = currentRole === "admin" ? "user" : "admin";

    // Display roles for user feedback
    const displayCurrentRole = currentRole === "user" ? "Artist" : "Admin";
    const displayNewRole = newRole === "user" ? "Artist" : "Admin";

    try {
      await authClient.admin.setRole({
        userId: row.original.id,
        role: newRole as "admin" | "user",
      });

      toast.success(
        `Successfully changed ${row.original.name}'s role to ${displayNewRole}`
      );

      // Refetch data to update the table
      onUserDeleted(); // Reusing this function since it just refetches data
    } catch (error: any) {
      console.error("Failed to change user role:", error);
      toast.error(
        "Failed to change user role: " + (error.message || "Unknown error")
      );
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="flex justify-end">
          <Button
            size="icon"
            variant="ghost"
            className="shadow-none"
            aria-label="Edit item"
          >
            <EllipsisIcon size={16} aria-hidden="true" />
          </Button>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuGroup>
          <UserDetailsDialog user={row.original}>
            <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
              <span>View Details</span>
            </DropdownMenuItem>
          </UserDetailsDialog>
          {row.original.banned ? (
            <DropdownMenuItem onClick={handleUnbanUser}>
              <span>Unban User</span>
            </DropdownMenuItem>
          ) : (
            <BanUserDialog user={row.original} onUserBanned={onUserDeleted}>
              <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                <span>Ban User</span>
              </DropdownMenuItem>
            </BanUserDialog>
          )}
        </DropdownMenuGroup>
        <DropdownMenuGroup>
          <DropdownMenuSub>
            <DropdownMenuSubTrigger>More</DropdownMenuSubTrigger>
            <DropdownMenuPortal>
              <DropdownMenuSubContent>
                <DropdownMenuItem onClick={handleChangeRole}>
                  {row.original.role === "admin"
                    ? "Change to Artist"
                    : "Change to Admin"}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleRevokeAllSessions}>
                  Revoke All Sessions
                </DropdownMenuItem>
              </DropdownMenuSubContent>
            </DropdownMenuPortal>
          </DropdownMenuSub>
        </DropdownMenuGroup>
        <DropdownMenuSeparator className="h-[2px]" />
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <DropdownMenuItem
              className="text-destructive focus:text-destructive"
              onSelect={(e) => e.preventDefault()}
            >
              <div className="flex items-center gap-2">
                <TrashIcon
                  size={16}
                  className="text-destructive focus:text-destructive"
                />
                <span>Delete</span>
              </div>
            </DropdownMenuItem>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <div className="flex flex-col gap-2 max-sm:items-center sm:flex-row sm:gap-4">
              <div
                className="flex size-9 shrink-0 items-center justify-center rounded-full border"
                aria-hidden="true"
              >
                <CircleAlertIcon className="opacity-80" size={16} />
              </div>
              <AlertDialogHeader>
                <AlertDialogTitle>Delete User</AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to delete{" "}
                  <strong>{row.original.name}</strong>? This action cannot be
                  undone and will permanently remove the user and all associated
                  data.
                </AlertDialogDescription>
              </AlertDialogHeader>
            </div>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDeleteUser}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                Delete User
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
