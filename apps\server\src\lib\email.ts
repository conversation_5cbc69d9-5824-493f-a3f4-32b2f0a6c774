import { LoopsClient, APIError } from "loops";

// Initialize Loops client
const loops = new LoopsClient(process.env.LOOPS_API_KEY || "");

export interface EmailVerificationData {
  email: string;
  url: string; // The complete verification URL provided by Better Auth
  name?: string; // User's name for personalization in the email template
}

export interface PasswordResetData {
  email: string;
  resetUrl: string; // The complete password reset URL provided by Better Auth
  name?: string; // User's name for personalization in the email template
}

export interface EmailChangeData {
  email: string; // Current email address where the verification link will be sent
  newEmail: string; // The new email address the user wants to change to
  url: string; // The complete email change verification URL provided by Better Auth
  name?: string; // User's name for personalization in the email template
}

export interface NotificationData {
  email: string;
  subject: string;
  message: string;
  name?: string;
}

export class EmailService {
  /**
   * Check if email service is properly configured
   */
  private static validateConfig(): { isValid: boolean; error?: string } {
    if (!process.env.LOOPS_API_KEY) {
      return { isValid: false, error: "LOOPS_API_KEY is not configured" };
    }
    if (!process.env.LOOPS_EMAIL_VERIFICATION_TEMPLATE_ID) {
      return {
        isValid: false,
        error: "LOOPS_EMAIL_VERIFICATION_TEMPLATE_ID is not configured",
      };
    }
    return { isValid: true };
  }

  /**
   * Send email verification email
   */
  static async sendVerificationEmail(
    data: EmailVerificationData
  ): Promise<{ success: boolean; error?: string }> {
    const configCheck = this.validateConfig();
    if (!configCheck.isValid) {
      return { success: false, error: configCheck.error };
    }

    try {
      const response = await loops.sendTransactionalEmail({
        transactionalId: process.env.LOOPS_EMAIL_VERIFICATION_TEMPLATE_ID!,
        email: data.email,
        dataVariables: {
          url: data.url,
          name: data.name || "User",
        },
      });

      if (!response.success) {
        return {
          success: false,
          error: "Failed to send verification email",
        };
      }

      return { success: true };
    } catch (error) {
      console.error("Error sending verification email:", error);

      if (error instanceof APIError) {
        return {
          success: false,
          error: JSON.stringify(error.json) || "API error occurred",
        };
      }

      return {
        success: false,
        error: "Internal server error",
      };
    }
  }

  /**
   * Send password reset email
   */
  static async sendPasswordResetEmail(
    data: PasswordResetData
  ): Promise<{ success: boolean; error?: string }> {
    if (!process.env.LOOPS_PASSWORD_RESET_TEMPLATE_ID) {
      return {
        success: false,
        error: "LOOPS_PASSWORD_RESET_TEMPLATE_ID is not configured",
      };
    }

    try {
      const response = await loops.sendTransactionalEmail({
        transactionalId: process.env.LOOPS_PASSWORD_RESET_TEMPLATE_ID!,
        email: data.email,
        dataVariables: {
          url: data.resetUrl,
          name: data.name || "User",
        },
      });

      if (!response.success) {
        return {
          success: false,
          error: "Failed to send password reset email",
        };
      }

      return { success: true };
    } catch (error) {
      console.error("Error sending password reset email:", error);

      if (error instanceof APIError) {
        return {
          success: false,
          error: JSON.stringify(error.json) || "API error occurred",
        };
      }

      return {
        success: false,
        error: "Internal server error",
      };
    }
  }

  /**
   * Send email change verification email
   */
  static async sendEmailChangeVerification(
    data: EmailChangeData
  ): Promise<{ success: boolean; error?: string }> {
    // Validate that the email change template ID is configured
    if (!process.env.LOOPS_EMAIL_CHANGE_TEMPLATE_ID) {
      return {
        success: false,
        error: "LOOPS_EMAIL_CHANGE_TEMPLATE_ID is not configured",
      };
    }

    try {
      const response = await loops.sendTransactionalEmail({
        transactionalId: process.env.LOOPS_EMAIL_CHANGE_TEMPLATE_ID!,
        email: data.email, // Send to current email address for security
        dataVariables: {
          url: data.url,
          newEmail: data.newEmail,
          name: data.name || "User",
        },
      });

      if (!response.success) {
        return {
          success: false,
          error: "Failed to send email change verification email",
        };
      }

      return { success: true };
    } catch (error) {
      console.error("Error sending email change verification email:", error);

      if (error instanceof APIError) {
        return {
          success: false,
          error: JSON.stringify(error.json) || "API error occurred",
        };
      }

      return {
        success: false,
        error: "Internal server error",
      };
    }
  }

  /**
   * Send notification email
   */
  static async sendNotificationEmail(
    data: NotificationData
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await loops.sendTransactionalEmail({
        transactionalId: process.env.LOOPS_NOTIFICATION_TEMPLATE_ID!,
        email: data.email,
        dataVariables: {
          subject: data.subject,
          message: data.message,
          name: data.name || "User",
        },
      });

      if (!response.success) {
        return {
          success: false,
          error: "Failed to send notification email",
        };
      }

      return { success: true };
    } catch (error) {
      console.error("Error sending notification email:", error);

      if (error instanceof APIError) {
        return {
          success: false,
          error: JSON.stringify(error.json) || "API error occurred",
        };
      }

      return {
        success: false,
        error: "Internal server error",
      };
    }
  }

  /**
   * Generic method to send any transactional email
   */
  static async sendTransactionalEmail(
    transactionalId: string,
    email: string,
    dataVariables: Record<string, string | number>
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await loops.sendTransactionalEmail({
        transactionalId,
        email,
        dataVariables,
      });

      if (!response.success) {
        return {
          success: false,
          error: "Failed to send email",
        };
      }

      return { success: true };
    } catch (error) {
      console.error("Error sending transactional email:", error);

      if (error instanceof APIError) {
        return {
          success: false,
          error: JSON.stringify(error.json) || "API error occurred",
        };
      }

      return {
        success: false,
        error: "Internal server error",
      };
    }
  }

  /**
   * Validate email service configuration
   */
  static validateConfiguration(): {
    isValid: boolean;
    missingVars: string[];
    missingOptionalVars: string[];
  } {
    const requiredVars = [
      "LOOPS_API_KEY",
      "LOOPS_EMAIL_VERIFICATION_TEMPLATE_ID",
    ];

    const optionalVars = [
      "LOOPS_PASSWORD_RESET_TEMPLATE_ID",
      "LOOPS_EMAIL_CHANGE_TEMPLATE_ID",
      "LOOPS_NOTIFICATION_TEMPLATE_ID",
    ];

    const missingVars = requiredVars.filter((varName) => !process.env[varName]);
    const missingOptionalVars = optionalVars.filter(
      (varName) => !process.env[varName]
    );

    return {
      isValid: missingVars.length === 0,
      missingVars,
      missingOptionalVars,
    };
  }
}

export default EmailService;
