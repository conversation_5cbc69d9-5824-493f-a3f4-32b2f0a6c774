// Better Auth UserWithRole type (from the API response)
export type UserWithRole = {
  id: string;
  name: string;
  email: string;
  emailVerified: boolean;
  image?: string | null;
  phone?: string | null;
  role?: string | null;
  banned?: boolean | null;
  banReason?: string | null;
  banExpires?: Date | null;
  createdAt: Date;
  updatedAt: Date;
};

// Extended type for table display with computed fields
export type UserTableItem = UserWithRole & {
  status: "Active" | "Banned" | "Pending";
  displayRole: string;
};
