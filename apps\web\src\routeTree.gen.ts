/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as ResetPasswordRouteImport } from './routes/reset-password'
import { Route as ChangeEmailRouteImport } from './routes/change-email'
import { Route as DashboardRouteRouteImport } from './routes/dashboard/route'
import { Route as AuthRouteRouteImport } from './routes/auth/route'
import { Route as IndexRouteImport } from './routes/index'
import { Route as DashboardIndexRouteImport } from './routes/dashboard/index'
import { Route as AuthIndexRouteImport } from './routes/auth/index'
import { Route as DashboardUserManagementIndexRouteImport } from './routes/dashboard/user-management/index'
import { Route as DashboardTrackIndexRouteImport } from './routes/dashboard/track/index'
import { Route as DashboardProfileIndexRouteImport } from './routes/dashboard/profile/index'
import { Route as DashboardLabelIndexRouteImport } from './routes/dashboard/label/index'
import { Route as DashboardContributorIndexRouteImport } from './routes/dashboard/contributor/index'
import { Route as DashboardArtistIndexRouteImport } from './routes/dashboard/artist/index'
import { Route as DashboardArtistIdRouteImport } from './routes/dashboard/artist/$id'

const ResetPasswordRoute = ResetPasswordRouteImport.update({
  id: '/reset-password',
  path: '/reset-password',
  getParentRoute: () => rootRouteImport,
} as any)
const ChangeEmailRoute = ChangeEmailRouteImport.update({
  id: '/change-email',
  path: '/change-email',
  getParentRoute: () => rootRouteImport,
} as any)
const DashboardRouteRoute = DashboardRouteRouteImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthRouteRoute = AuthRouteRouteImport.update({
  id: '/auth',
  path: '/auth',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const DashboardIndexRoute = DashboardIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => DashboardRouteRoute,
} as any)
const AuthIndexRoute = AuthIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthRouteRoute,
} as any)
const DashboardUserManagementIndexRoute =
  DashboardUserManagementIndexRouteImport.update({
    id: '/user-management/',
    path: '/user-management/',
    getParentRoute: () => DashboardRouteRoute,
  } as any)
const DashboardTrackIndexRoute = DashboardTrackIndexRouteImport.update({
  id: '/track/',
  path: '/track/',
  getParentRoute: () => DashboardRouteRoute,
} as any)
const DashboardProfileIndexRoute = DashboardProfileIndexRouteImport.update({
  id: '/profile/',
  path: '/profile/',
  getParentRoute: () => DashboardRouteRoute,
} as any)
const DashboardLabelIndexRoute = DashboardLabelIndexRouteImport.update({
  id: '/label/',
  path: '/label/',
  getParentRoute: () => DashboardRouteRoute,
} as any)
const DashboardContributorIndexRoute =
  DashboardContributorIndexRouteImport.update({
    id: '/contributor/',
    path: '/contributor/',
    getParentRoute: () => DashboardRouteRoute,
  } as any)
const DashboardArtistIndexRoute = DashboardArtistIndexRouteImport.update({
  id: '/artist/',
  path: '/artist/',
  getParentRoute: () => DashboardRouteRoute,
} as any)
const DashboardArtistIdRoute = DashboardArtistIdRouteImport.update({
  id: '/artist/$id',
  path: '/artist/$id',
  getParentRoute: () => DashboardRouteRoute,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/auth': typeof AuthRouteRouteWithChildren
  '/dashboard': typeof DashboardRouteRouteWithChildren
  '/change-email': typeof ChangeEmailRoute
  '/reset-password': typeof ResetPasswordRoute
  '/auth/': typeof AuthIndexRoute
  '/dashboard/': typeof DashboardIndexRoute
  '/dashboard/artist/$id': typeof DashboardArtistIdRoute
  '/dashboard/artist': typeof DashboardArtistIndexRoute
  '/dashboard/contributor': typeof DashboardContributorIndexRoute
  '/dashboard/label': typeof DashboardLabelIndexRoute
  '/dashboard/profile': typeof DashboardProfileIndexRoute
  '/dashboard/track': typeof DashboardTrackIndexRoute
  '/dashboard/user-management': typeof DashboardUserManagementIndexRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/change-email': typeof ChangeEmailRoute
  '/reset-password': typeof ResetPasswordRoute
  '/auth': typeof AuthIndexRoute
  '/dashboard': typeof DashboardIndexRoute
  '/dashboard/artist/$id': typeof DashboardArtistIdRoute
  '/dashboard/artist': typeof DashboardArtistIndexRoute
  '/dashboard/contributor': typeof DashboardContributorIndexRoute
  '/dashboard/label': typeof DashboardLabelIndexRoute
  '/dashboard/profile': typeof DashboardProfileIndexRoute
  '/dashboard/track': typeof DashboardTrackIndexRoute
  '/dashboard/user-management': typeof DashboardUserManagementIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/auth': typeof AuthRouteRouteWithChildren
  '/dashboard': typeof DashboardRouteRouteWithChildren
  '/change-email': typeof ChangeEmailRoute
  '/reset-password': typeof ResetPasswordRoute
  '/auth/': typeof AuthIndexRoute
  '/dashboard/': typeof DashboardIndexRoute
  '/dashboard/artist/$id': typeof DashboardArtistIdRoute
  '/dashboard/artist/': typeof DashboardArtistIndexRoute
  '/dashboard/contributor/': typeof DashboardContributorIndexRoute
  '/dashboard/label/': typeof DashboardLabelIndexRoute
  '/dashboard/profile/': typeof DashboardProfileIndexRoute
  '/dashboard/track/': typeof DashboardTrackIndexRoute
  '/dashboard/user-management/': typeof DashboardUserManagementIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/auth'
    | '/dashboard'
    | '/change-email'
    | '/reset-password'
    | '/auth/'
    | '/dashboard/'
    | '/dashboard/artist/$id'
    | '/dashboard/artist'
    | '/dashboard/contributor'
    | '/dashboard/label'
    | '/dashboard/profile'
    | '/dashboard/track'
    | '/dashboard/user-management'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/change-email'
    | '/reset-password'
    | '/auth'
    | '/dashboard'
    | '/dashboard/artist/$id'
    | '/dashboard/artist'
    | '/dashboard/contributor'
    | '/dashboard/label'
    | '/dashboard/profile'
    | '/dashboard/track'
    | '/dashboard/user-management'
  id:
    | '__root__'
    | '/'
    | '/auth'
    | '/dashboard'
    | '/change-email'
    | '/reset-password'
    | '/auth/'
    | '/dashboard/'
    | '/dashboard/artist/$id'
    | '/dashboard/artist/'
    | '/dashboard/contributor/'
    | '/dashboard/label/'
    | '/dashboard/profile/'
    | '/dashboard/track/'
    | '/dashboard/user-management/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AuthRouteRoute: typeof AuthRouteRouteWithChildren
  DashboardRouteRoute: typeof DashboardRouteRouteWithChildren
  ChangeEmailRoute: typeof ChangeEmailRoute
  ResetPasswordRoute: typeof ResetPasswordRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/reset-password': {
      id: '/reset-password'
      path: '/reset-password'
      fullPath: '/reset-password'
      preLoaderRoute: typeof ResetPasswordRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/change-email': {
      id: '/change-email'
      path: '/change-email'
      fullPath: '/change-email'
      preLoaderRoute: typeof ChangeEmailRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/dashboard': {
      id: '/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardRouteRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/auth': {
      id: '/auth'
      path: '/auth'
      fullPath: '/auth'
      preLoaderRoute: typeof AuthRouteRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/dashboard/': {
      id: '/dashboard/'
      path: '/'
      fullPath: '/dashboard/'
      preLoaderRoute: typeof DashboardIndexRouteImport
      parentRoute: typeof DashboardRouteRoute
    }
    '/auth/': {
      id: '/auth/'
      path: '/'
      fullPath: '/auth/'
      preLoaderRoute: typeof AuthIndexRouteImport
      parentRoute: typeof AuthRouteRoute
    }
    '/dashboard/user-management/': {
      id: '/dashboard/user-management/'
      path: '/user-management'
      fullPath: '/dashboard/user-management'
      preLoaderRoute: typeof DashboardUserManagementIndexRouteImport
      parentRoute: typeof DashboardRouteRoute
    }
    '/dashboard/track/': {
      id: '/dashboard/track/'
      path: '/track'
      fullPath: '/dashboard/track'
      preLoaderRoute: typeof DashboardTrackIndexRouteImport
      parentRoute: typeof DashboardRouteRoute
    }
    '/dashboard/profile/': {
      id: '/dashboard/profile/'
      path: '/profile'
      fullPath: '/dashboard/profile'
      preLoaderRoute: typeof DashboardProfileIndexRouteImport
      parentRoute: typeof DashboardRouteRoute
    }
    '/dashboard/label/': {
      id: '/dashboard/label/'
      path: '/label'
      fullPath: '/dashboard/label'
      preLoaderRoute: typeof DashboardLabelIndexRouteImport
      parentRoute: typeof DashboardRouteRoute
    }
    '/dashboard/contributor/': {
      id: '/dashboard/contributor/'
      path: '/contributor'
      fullPath: '/dashboard/contributor'
      preLoaderRoute: typeof DashboardContributorIndexRouteImport
      parentRoute: typeof DashboardRouteRoute
    }
    '/dashboard/artist/': {
      id: '/dashboard/artist/'
      path: '/artist'
      fullPath: '/dashboard/artist'
      preLoaderRoute: typeof DashboardArtistIndexRouteImport
      parentRoute: typeof DashboardRouteRoute
    }
    '/dashboard/artist/$id': {
      id: '/dashboard/artist/$id'
      path: '/artist/$id'
      fullPath: '/dashboard/artist/$id'
      preLoaderRoute: typeof DashboardArtistIdRouteImport
      parentRoute: typeof DashboardRouteRoute
    }
  }
}

interface AuthRouteRouteChildren {
  AuthIndexRoute: typeof AuthIndexRoute
}

const AuthRouteRouteChildren: AuthRouteRouteChildren = {
  AuthIndexRoute: AuthIndexRoute,
}

const AuthRouteRouteWithChildren = AuthRouteRoute._addFileChildren(
  AuthRouteRouteChildren,
)

interface DashboardRouteRouteChildren {
  DashboardIndexRoute: typeof DashboardIndexRoute
  DashboardArtistIdRoute: typeof DashboardArtistIdRoute
  DashboardArtistIndexRoute: typeof DashboardArtistIndexRoute
  DashboardContributorIndexRoute: typeof DashboardContributorIndexRoute
  DashboardLabelIndexRoute: typeof DashboardLabelIndexRoute
  DashboardProfileIndexRoute: typeof DashboardProfileIndexRoute
  DashboardTrackIndexRoute: typeof DashboardTrackIndexRoute
  DashboardUserManagementIndexRoute: typeof DashboardUserManagementIndexRoute
}

const DashboardRouteRouteChildren: DashboardRouteRouteChildren = {
  DashboardIndexRoute: DashboardIndexRoute,
  DashboardArtistIdRoute: DashboardArtistIdRoute,
  DashboardArtistIndexRoute: DashboardArtistIndexRoute,
  DashboardContributorIndexRoute: DashboardContributorIndexRoute,
  DashboardLabelIndexRoute: DashboardLabelIndexRoute,
  DashboardProfileIndexRoute: DashboardProfileIndexRoute,
  DashboardTrackIndexRoute: DashboardTrackIndexRoute,
  DashboardUserManagementIndexRoute: DashboardUserManagementIndexRoute,
}

const DashboardRouteRouteWithChildren = DashboardRouteRoute._addFileChildren(
  DashboardRouteRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AuthRouteRoute: AuthRouteRouteWithChildren,
  DashboardRouteRoute: DashboardRouteRouteWithChildren,
  ChangeEmailRoute: ChangeEmailRoute,
  ResetPasswordRoute: ResetPasswordRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
