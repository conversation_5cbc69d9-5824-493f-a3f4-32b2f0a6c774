
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This is a barrel export file for all models and their related types.
 *
 * 🟢 You can import this file directly.
 */
export type * from './models/Artist'
export type * from './models/ArtistIdentifier'
export type * from './models/User'
export type * from './models/Session'
export type * from './models/Account'
export type * from './models/Verification'
export type * from './models/Contributor'
export type * from './models/Label'
export type * from './models/Release'
export type * from './models/ReleaseArtist'
export type * from './models/ReleaseTrack'
export type * from './models/ReleaseCoverArt'
export type * from './models/Track'
export type * from './models/TrackArtist'
export type * from './models/TrackContributor'
export type * from './models/TrackFile'
export type * from './commonInputTypes'