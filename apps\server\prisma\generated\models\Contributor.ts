
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Contributor` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Contributor
 * 
 */
export type ContributorModel = runtime.Types.Result.DefaultSelection<Prisma.$ContributorPayload>

export type AggregateContributor = {
  _count: ContributorCountAggregateOutputType | null
  _min: ContributorMinAggregateOutputType | null
  _max: ContributorMaxAggregateOutputType | null
}

export type ContributorMinAggregateOutputType = {
  id: string | null
  name: string | null
  userId: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type ContributorMaxAggregateOutputType = {
  id: string | null
  name: string | null
  userId: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type ContributorCountAggregateOutputType = {
  id: number
  name: number
  userId: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type ContributorMinAggregateInputType = {
  id?: true
  name?: true
  userId?: true
  createdAt?: true
  updatedAt?: true
}

export type ContributorMaxAggregateInputType = {
  id?: true
  name?: true
  userId?: true
  createdAt?: true
  updatedAt?: true
}

export type ContributorCountAggregateInputType = {
  id?: true
  name?: true
  userId?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type ContributorAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Contributor to aggregate.
   */
  where?: Prisma.ContributorWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Contributors to fetch.
   */
  orderBy?: Prisma.ContributorOrderByWithRelationInput | Prisma.ContributorOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.ContributorWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Contributors from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Contributors.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Contributors
  **/
  _count?: true | ContributorCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: ContributorMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: ContributorMaxAggregateInputType
}

export type GetContributorAggregateType<T extends ContributorAggregateArgs> = {
      [P in keyof T & keyof AggregateContributor]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateContributor[P]>
    : Prisma.GetScalarType<T[P], AggregateContributor[P]>
}




export type ContributorGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ContributorWhereInput
  orderBy?: Prisma.ContributorOrderByWithAggregationInput | Prisma.ContributorOrderByWithAggregationInput[]
  by: Prisma.ContributorScalarFieldEnum[] | Prisma.ContributorScalarFieldEnum
  having?: Prisma.ContributorScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: ContributorCountAggregateInputType | true
  _min?: ContributorMinAggregateInputType
  _max?: ContributorMaxAggregateInputType
}

export type ContributorGroupByOutputType = {
  id: string
  name: string
  userId: string
  createdAt: Date
  updatedAt: Date
  _count: ContributorCountAggregateOutputType | null
  _min: ContributorMinAggregateOutputType | null
  _max: ContributorMaxAggregateOutputType | null
}

type GetContributorGroupByPayload<T extends ContributorGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<ContributorGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof ContributorGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], ContributorGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], ContributorGroupByOutputType[P]>
      }
    >
  > 



export type ContributorWhereInput = {
  AND?: Prisma.ContributorWhereInput | Prisma.ContributorWhereInput[]
  OR?: Prisma.ContributorWhereInput[]
  NOT?: Prisma.ContributorWhereInput | Prisma.ContributorWhereInput[]
  id?: Prisma.StringFilter<"Contributor"> | string
  name?: Prisma.StringFilter<"Contributor"> | string
  userId?: Prisma.StringFilter<"Contributor"> | string
  createdAt?: Prisma.DateTimeFilter<"Contributor"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Contributor"> | Date | string
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
  tracks?: Prisma.TrackContributorListRelationFilter
}

export type ContributorOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  user?: Prisma.UserOrderByWithRelationInput
  tracks?: Prisma.TrackContributorOrderByRelationAggregateInput
}

export type ContributorWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  userId_name?: Prisma.ContributorUserIdNameCompoundUniqueInput
  AND?: Prisma.ContributorWhereInput | Prisma.ContributorWhereInput[]
  OR?: Prisma.ContributorWhereInput[]
  NOT?: Prisma.ContributorWhereInput | Prisma.ContributorWhereInput[]
  name?: Prisma.StringFilter<"Contributor"> | string
  userId?: Prisma.StringFilter<"Contributor"> | string
  createdAt?: Prisma.DateTimeFilter<"Contributor"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Contributor"> | Date | string
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
  tracks?: Prisma.TrackContributorListRelationFilter
}, "id" | "userId_name">

export type ContributorOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.ContributorCountOrderByAggregateInput
  _max?: Prisma.ContributorMaxOrderByAggregateInput
  _min?: Prisma.ContributorMinOrderByAggregateInput
}

export type ContributorScalarWhereWithAggregatesInput = {
  AND?: Prisma.ContributorScalarWhereWithAggregatesInput | Prisma.ContributorScalarWhereWithAggregatesInput[]
  OR?: Prisma.ContributorScalarWhereWithAggregatesInput[]
  NOT?: Prisma.ContributorScalarWhereWithAggregatesInput | Prisma.ContributorScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Contributor"> | string
  name?: Prisma.StringWithAggregatesFilter<"Contributor"> | string
  userId?: Prisma.StringWithAggregatesFilter<"Contributor"> | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Contributor"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"Contributor"> | Date | string
}

export type ContributorCreateInput = {
  id?: string
  name: string
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutContributorsInput
  tracks?: Prisma.TrackContributorCreateNestedManyWithoutContributorInput
}

export type ContributorUncheckedCreateInput = {
  id?: string
  name: string
  userId: string
  createdAt?: Date | string
  updatedAt?: Date | string
  tracks?: Prisma.TrackContributorUncheckedCreateNestedManyWithoutContributorInput
}

export type ContributorUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutContributorsNestedInput
  tracks?: Prisma.TrackContributorUpdateManyWithoutContributorNestedInput
}

export type ContributorUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  tracks?: Prisma.TrackContributorUncheckedUpdateManyWithoutContributorNestedInput
}

export type ContributorCreateManyInput = {
  id?: string
  name: string
  userId: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type ContributorUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ContributorUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ContributorListRelationFilter = {
  every?: Prisma.ContributorWhereInput
  some?: Prisma.ContributorWhereInput
  none?: Prisma.ContributorWhereInput
}

export type ContributorOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type ContributorUserIdNameCompoundUniqueInput = {
  userId: string
  name: string
}

export type ContributorCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type ContributorMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type ContributorMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type ContributorScalarRelationFilter = {
  is?: Prisma.ContributorWhereInput
  isNot?: Prisma.ContributorWhereInput
}

export type ContributorCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.ContributorCreateWithoutUserInput, Prisma.ContributorUncheckedCreateWithoutUserInput> | Prisma.ContributorCreateWithoutUserInput[] | Prisma.ContributorUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.ContributorCreateOrConnectWithoutUserInput | Prisma.ContributorCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.ContributorCreateManyUserInputEnvelope
  connect?: Prisma.ContributorWhereUniqueInput | Prisma.ContributorWhereUniqueInput[]
}

export type ContributorUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.ContributorCreateWithoutUserInput, Prisma.ContributorUncheckedCreateWithoutUserInput> | Prisma.ContributorCreateWithoutUserInput[] | Prisma.ContributorUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.ContributorCreateOrConnectWithoutUserInput | Prisma.ContributorCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.ContributorCreateManyUserInputEnvelope
  connect?: Prisma.ContributorWhereUniqueInput | Prisma.ContributorWhereUniqueInput[]
}

export type ContributorUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.ContributorCreateWithoutUserInput, Prisma.ContributorUncheckedCreateWithoutUserInput> | Prisma.ContributorCreateWithoutUserInput[] | Prisma.ContributorUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.ContributorCreateOrConnectWithoutUserInput | Prisma.ContributorCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.ContributorUpsertWithWhereUniqueWithoutUserInput | Prisma.ContributorUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.ContributorCreateManyUserInputEnvelope
  set?: Prisma.ContributorWhereUniqueInput | Prisma.ContributorWhereUniqueInput[]
  disconnect?: Prisma.ContributorWhereUniqueInput | Prisma.ContributorWhereUniqueInput[]
  delete?: Prisma.ContributorWhereUniqueInput | Prisma.ContributorWhereUniqueInput[]
  connect?: Prisma.ContributorWhereUniqueInput | Prisma.ContributorWhereUniqueInput[]
  update?: Prisma.ContributorUpdateWithWhereUniqueWithoutUserInput | Prisma.ContributorUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.ContributorUpdateManyWithWhereWithoutUserInput | Prisma.ContributorUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.ContributorScalarWhereInput | Prisma.ContributorScalarWhereInput[]
}

export type ContributorUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.ContributorCreateWithoutUserInput, Prisma.ContributorUncheckedCreateWithoutUserInput> | Prisma.ContributorCreateWithoutUserInput[] | Prisma.ContributorUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.ContributorCreateOrConnectWithoutUserInput | Prisma.ContributorCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.ContributorUpsertWithWhereUniqueWithoutUserInput | Prisma.ContributorUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.ContributorCreateManyUserInputEnvelope
  set?: Prisma.ContributorWhereUniqueInput | Prisma.ContributorWhereUniqueInput[]
  disconnect?: Prisma.ContributorWhereUniqueInput | Prisma.ContributorWhereUniqueInput[]
  delete?: Prisma.ContributorWhereUniqueInput | Prisma.ContributorWhereUniqueInput[]
  connect?: Prisma.ContributorWhereUniqueInput | Prisma.ContributorWhereUniqueInput[]
  update?: Prisma.ContributorUpdateWithWhereUniqueWithoutUserInput | Prisma.ContributorUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.ContributorUpdateManyWithWhereWithoutUserInput | Prisma.ContributorUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.ContributorScalarWhereInput | Prisma.ContributorScalarWhereInput[]
}

export type ContributorCreateNestedOneWithoutTracksInput = {
  create?: Prisma.XOR<Prisma.ContributorCreateWithoutTracksInput, Prisma.ContributorUncheckedCreateWithoutTracksInput>
  connectOrCreate?: Prisma.ContributorCreateOrConnectWithoutTracksInput
  connect?: Prisma.ContributorWhereUniqueInput
}

export type ContributorUpdateOneRequiredWithoutTracksNestedInput = {
  create?: Prisma.XOR<Prisma.ContributorCreateWithoutTracksInput, Prisma.ContributorUncheckedCreateWithoutTracksInput>
  connectOrCreate?: Prisma.ContributorCreateOrConnectWithoutTracksInput
  upsert?: Prisma.ContributorUpsertWithoutTracksInput
  connect?: Prisma.ContributorWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.ContributorUpdateToOneWithWhereWithoutTracksInput, Prisma.ContributorUpdateWithoutTracksInput>, Prisma.ContributorUncheckedUpdateWithoutTracksInput>
}

export type ContributorCreateWithoutUserInput = {
  id?: string
  name: string
  createdAt?: Date | string
  updatedAt?: Date | string
  tracks?: Prisma.TrackContributorCreateNestedManyWithoutContributorInput
}

export type ContributorUncheckedCreateWithoutUserInput = {
  id?: string
  name: string
  createdAt?: Date | string
  updatedAt?: Date | string
  tracks?: Prisma.TrackContributorUncheckedCreateNestedManyWithoutContributorInput
}

export type ContributorCreateOrConnectWithoutUserInput = {
  where: Prisma.ContributorWhereUniqueInput
  create: Prisma.XOR<Prisma.ContributorCreateWithoutUserInput, Prisma.ContributorUncheckedCreateWithoutUserInput>
}

export type ContributorCreateManyUserInputEnvelope = {
  data: Prisma.ContributorCreateManyUserInput | Prisma.ContributorCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type ContributorUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.ContributorWhereUniqueInput
  update: Prisma.XOR<Prisma.ContributorUpdateWithoutUserInput, Prisma.ContributorUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.ContributorCreateWithoutUserInput, Prisma.ContributorUncheckedCreateWithoutUserInput>
}

export type ContributorUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.ContributorWhereUniqueInput
  data: Prisma.XOR<Prisma.ContributorUpdateWithoutUserInput, Prisma.ContributorUncheckedUpdateWithoutUserInput>
}

export type ContributorUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.ContributorScalarWhereInput
  data: Prisma.XOR<Prisma.ContributorUpdateManyMutationInput, Prisma.ContributorUncheckedUpdateManyWithoutUserInput>
}

export type ContributorScalarWhereInput = {
  AND?: Prisma.ContributorScalarWhereInput | Prisma.ContributorScalarWhereInput[]
  OR?: Prisma.ContributorScalarWhereInput[]
  NOT?: Prisma.ContributorScalarWhereInput | Prisma.ContributorScalarWhereInput[]
  id?: Prisma.StringFilter<"Contributor"> | string
  name?: Prisma.StringFilter<"Contributor"> | string
  userId?: Prisma.StringFilter<"Contributor"> | string
  createdAt?: Prisma.DateTimeFilter<"Contributor"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Contributor"> | Date | string
}

export type ContributorCreateWithoutTracksInput = {
  id?: string
  name: string
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutContributorsInput
}

export type ContributorUncheckedCreateWithoutTracksInput = {
  id?: string
  name: string
  userId: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type ContributorCreateOrConnectWithoutTracksInput = {
  where: Prisma.ContributorWhereUniqueInput
  create: Prisma.XOR<Prisma.ContributorCreateWithoutTracksInput, Prisma.ContributorUncheckedCreateWithoutTracksInput>
}

export type ContributorUpsertWithoutTracksInput = {
  update: Prisma.XOR<Prisma.ContributorUpdateWithoutTracksInput, Prisma.ContributorUncheckedUpdateWithoutTracksInput>
  create: Prisma.XOR<Prisma.ContributorCreateWithoutTracksInput, Prisma.ContributorUncheckedCreateWithoutTracksInput>
  where?: Prisma.ContributorWhereInput
}

export type ContributorUpdateToOneWithWhereWithoutTracksInput = {
  where?: Prisma.ContributorWhereInput
  data: Prisma.XOR<Prisma.ContributorUpdateWithoutTracksInput, Prisma.ContributorUncheckedUpdateWithoutTracksInput>
}

export type ContributorUpdateWithoutTracksInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutContributorsNestedInput
}

export type ContributorUncheckedUpdateWithoutTracksInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ContributorCreateManyUserInput = {
  id?: string
  name: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type ContributorUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  tracks?: Prisma.TrackContributorUpdateManyWithoutContributorNestedInput
}

export type ContributorUncheckedUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  tracks?: Prisma.TrackContributorUncheckedUpdateManyWithoutContributorNestedInput
}

export type ContributorUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}


/**
 * Count Type ContributorCountOutputType
 */

export type ContributorCountOutputType = {
  tracks: number
}

export type ContributorCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  tracks?: boolean | ContributorCountOutputTypeCountTracksArgs
}

/**
 * ContributorCountOutputType without action
 */
export type ContributorCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ContributorCountOutputType
   */
  select?: Prisma.ContributorCountOutputTypeSelect<ExtArgs> | null
}

/**
 * ContributorCountOutputType without action
 */
export type ContributorCountOutputTypeCountTracksArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.TrackContributorWhereInput
}


export type ContributorSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  userId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  tracks?: boolean | Prisma.Contributor$tracksArgs<ExtArgs>
  _count?: boolean | Prisma.ContributorCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["contributor"]>

export type ContributorSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  userId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}, ExtArgs["result"]["contributor"]>

export type ContributorSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  userId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}, ExtArgs["result"]["contributor"]>

export type ContributorSelectScalar = {
  id?: boolean
  name?: boolean
  userId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type ContributorOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "name" | "userId" | "createdAt" | "updatedAt", ExtArgs["result"]["contributor"]>
export type ContributorInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  tracks?: boolean | Prisma.Contributor$tracksArgs<ExtArgs>
  _count?: boolean | Prisma.ContributorCountOutputTypeDefaultArgs<ExtArgs>
}
export type ContributorIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}
export type ContributorIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}

export type $ContributorPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Contributor"
  objects: {
    user: Prisma.$UserPayload<ExtArgs>
    tracks: Prisma.$TrackContributorPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    name: string
    userId: string
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["contributor"]>
  composites: {}
}

export type ContributorGetPayload<S extends boolean | null | undefined | ContributorDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$ContributorPayload, S>

export type ContributorCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<ContributorFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: ContributorCountAggregateInputType | true
  }

export interface ContributorDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Contributor'], meta: { name: 'Contributor' } }
  /**
   * Find zero or one Contributor that matches the filter.
   * @param {ContributorFindUniqueArgs} args - Arguments to find a Contributor
   * @example
   * // Get one Contributor
   * const contributor = await prisma.contributor.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends ContributorFindUniqueArgs>(args: Prisma.SelectSubset<T, ContributorFindUniqueArgs<ExtArgs>>): Prisma.Prisma__ContributorClient<runtime.Types.Result.GetResult<Prisma.$ContributorPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Contributor that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {ContributorFindUniqueOrThrowArgs} args - Arguments to find a Contributor
   * @example
   * // Get one Contributor
   * const contributor = await prisma.contributor.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends ContributorFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, ContributorFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__ContributorClient<runtime.Types.Result.GetResult<Prisma.$ContributorPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Contributor that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ContributorFindFirstArgs} args - Arguments to find a Contributor
   * @example
   * // Get one Contributor
   * const contributor = await prisma.contributor.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends ContributorFindFirstArgs>(args?: Prisma.SelectSubset<T, ContributorFindFirstArgs<ExtArgs>>): Prisma.Prisma__ContributorClient<runtime.Types.Result.GetResult<Prisma.$ContributorPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Contributor that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ContributorFindFirstOrThrowArgs} args - Arguments to find a Contributor
   * @example
   * // Get one Contributor
   * const contributor = await prisma.contributor.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends ContributorFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, ContributorFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__ContributorClient<runtime.Types.Result.GetResult<Prisma.$ContributorPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Contributors that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ContributorFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Contributors
   * const contributors = await prisma.contributor.findMany()
   * 
   * // Get first 10 Contributors
   * const contributors = await prisma.contributor.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const contributorWithIdOnly = await prisma.contributor.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends ContributorFindManyArgs>(args?: Prisma.SelectSubset<T, ContributorFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ContributorPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Contributor.
   * @param {ContributorCreateArgs} args - Arguments to create a Contributor.
   * @example
   * // Create one Contributor
   * const Contributor = await prisma.contributor.create({
   *   data: {
   *     // ... data to create a Contributor
   *   }
   * })
   * 
   */
  create<T extends ContributorCreateArgs>(args: Prisma.SelectSubset<T, ContributorCreateArgs<ExtArgs>>): Prisma.Prisma__ContributorClient<runtime.Types.Result.GetResult<Prisma.$ContributorPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Contributors.
   * @param {ContributorCreateManyArgs} args - Arguments to create many Contributors.
   * @example
   * // Create many Contributors
   * const contributor = await prisma.contributor.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends ContributorCreateManyArgs>(args?: Prisma.SelectSubset<T, ContributorCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Contributors and returns the data saved in the database.
   * @param {ContributorCreateManyAndReturnArgs} args - Arguments to create many Contributors.
   * @example
   * // Create many Contributors
   * const contributor = await prisma.contributor.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Contributors and only return the `id`
   * const contributorWithIdOnly = await prisma.contributor.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends ContributorCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, ContributorCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ContributorPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a Contributor.
   * @param {ContributorDeleteArgs} args - Arguments to delete one Contributor.
   * @example
   * // Delete one Contributor
   * const Contributor = await prisma.contributor.delete({
   *   where: {
   *     // ... filter to delete one Contributor
   *   }
   * })
   * 
   */
  delete<T extends ContributorDeleteArgs>(args: Prisma.SelectSubset<T, ContributorDeleteArgs<ExtArgs>>): Prisma.Prisma__ContributorClient<runtime.Types.Result.GetResult<Prisma.$ContributorPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Contributor.
   * @param {ContributorUpdateArgs} args - Arguments to update one Contributor.
   * @example
   * // Update one Contributor
   * const contributor = await prisma.contributor.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends ContributorUpdateArgs>(args: Prisma.SelectSubset<T, ContributorUpdateArgs<ExtArgs>>): Prisma.Prisma__ContributorClient<runtime.Types.Result.GetResult<Prisma.$ContributorPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Contributors.
   * @param {ContributorDeleteManyArgs} args - Arguments to filter Contributors to delete.
   * @example
   * // Delete a few Contributors
   * const { count } = await prisma.contributor.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends ContributorDeleteManyArgs>(args?: Prisma.SelectSubset<T, ContributorDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Contributors.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ContributorUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Contributors
   * const contributor = await prisma.contributor.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends ContributorUpdateManyArgs>(args: Prisma.SelectSubset<T, ContributorUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Contributors and returns the data updated in the database.
   * @param {ContributorUpdateManyAndReturnArgs} args - Arguments to update many Contributors.
   * @example
   * // Update many Contributors
   * const contributor = await prisma.contributor.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Contributors and only return the `id`
   * const contributorWithIdOnly = await prisma.contributor.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends ContributorUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, ContributorUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ContributorPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one Contributor.
   * @param {ContributorUpsertArgs} args - Arguments to update or create a Contributor.
   * @example
   * // Update or create a Contributor
   * const contributor = await prisma.contributor.upsert({
   *   create: {
   *     // ... data to create a Contributor
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Contributor we want to update
   *   }
   * })
   */
  upsert<T extends ContributorUpsertArgs>(args: Prisma.SelectSubset<T, ContributorUpsertArgs<ExtArgs>>): Prisma.Prisma__ContributorClient<runtime.Types.Result.GetResult<Prisma.$ContributorPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Contributors.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ContributorCountArgs} args - Arguments to filter Contributors to count.
   * @example
   * // Count the number of Contributors
   * const count = await prisma.contributor.count({
   *   where: {
   *     // ... the filter for the Contributors we want to count
   *   }
   * })
  **/
  count<T extends ContributorCountArgs>(
    args?: Prisma.Subset<T, ContributorCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], ContributorCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Contributor.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ContributorAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends ContributorAggregateArgs>(args: Prisma.Subset<T, ContributorAggregateArgs>): Prisma.PrismaPromise<GetContributorAggregateType<T>>

  /**
   * Group by Contributor.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ContributorGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends ContributorGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: ContributorGroupByArgs['orderBy'] }
      : { orderBy?: ContributorGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, ContributorGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetContributorGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Contributor model
 */
readonly fields: ContributorFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Contributor.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__ContributorClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user<T extends Prisma.UserDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.UserDefaultArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  tracks<T extends Prisma.Contributor$tracksArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Contributor$tracksArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TrackContributorPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Contributor model
 */
export interface ContributorFieldRefs {
  readonly id: Prisma.FieldRef<"Contributor", 'String'>
  readonly name: Prisma.FieldRef<"Contributor", 'String'>
  readonly userId: Prisma.FieldRef<"Contributor", 'String'>
  readonly createdAt: Prisma.FieldRef<"Contributor", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"Contributor", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Contributor findUnique
 */
export type ContributorFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Contributor
   */
  select?: Prisma.ContributorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Contributor
   */
  omit?: Prisma.ContributorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ContributorInclude<ExtArgs> | null
  /**
   * Filter, which Contributor to fetch.
   */
  where: Prisma.ContributorWhereUniqueInput
}

/**
 * Contributor findUniqueOrThrow
 */
export type ContributorFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Contributor
   */
  select?: Prisma.ContributorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Contributor
   */
  omit?: Prisma.ContributorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ContributorInclude<ExtArgs> | null
  /**
   * Filter, which Contributor to fetch.
   */
  where: Prisma.ContributorWhereUniqueInput
}

/**
 * Contributor findFirst
 */
export type ContributorFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Contributor
   */
  select?: Prisma.ContributorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Contributor
   */
  omit?: Prisma.ContributorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ContributorInclude<ExtArgs> | null
  /**
   * Filter, which Contributor to fetch.
   */
  where?: Prisma.ContributorWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Contributors to fetch.
   */
  orderBy?: Prisma.ContributorOrderByWithRelationInput | Prisma.ContributorOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Contributors.
   */
  cursor?: Prisma.ContributorWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Contributors from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Contributors.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Contributors.
   */
  distinct?: Prisma.ContributorScalarFieldEnum | Prisma.ContributorScalarFieldEnum[]
}

/**
 * Contributor findFirstOrThrow
 */
export type ContributorFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Contributor
   */
  select?: Prisma.ContributorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Contributor
   */
  omit?: Prisma.ContributorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ContributorInclude<ExtArgs> | null
  /**
   * Filter, which Contributor to fetch.
   */
  where?: Prisma.ContributorWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Contributors to fetch.
   */
  orderBy?: Prisma.ContributorOrderByWithRelationInput | Prisma.ContributorOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Contributors.
   */
  cursor?: Prisma.ContributorWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Contributors from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Contributors.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Contributors.
   */
  distinct?: Prisma.ContributorScalarFieldEnum | Prisma.ContributorScalarFieldEnum[]
}

/**
 * Contributor findMany
 */
export type ContributorFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Contributor
   */
  select?: Prisma.ContributorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Contributor
   */
  omit?: Prisma.ContributorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ContributorInclude<ExtArgs> | null
  /**
   * Filter, which Contributors to fetch.
   */
  where?: Prisma.ContributorWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Contributors to fetch.
   */
  orderBy?: Prisma.ContributorOrderByWithRelationInput | Prisma.ContributorOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Contributors.
   */
  cursor?: Prisma.ContributorWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Contributors from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Contributors.
   */
  skip?: number
  distinct?: Prisma.ContributorScalarFieldEnum | Prisma.ContributorScalarFieldEnum[]
}

/**
 * Contributor create
 */
export type ContributorCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Contributor
   */
  select?: Prisma.ContributorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Contributor
   */
  omit?: Prisma.ContributorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ContributorInclude<ExtArgs> | null
  /**
   * The data needed to create a Contributor.
   */
  data: Prisma.XOR<Prisma.ContributorCreateInput, Prisma.ContributorUncheckedCreateInput>
}

/**
 * Contributor createMany
 */
export type ContributorCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Contributors.
   */
  data: Prisma.ContributorCreateManyInput | Prisma.ContributorCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Contributor createManyAndReturn
 */
export type ContributorCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Contributor
   */
  select?: Prisma.ContributorSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Contributor
   */
  omit?: Prisma.ContributorOmit<ExtArgs> | null
  /**
   * The data used to create many Contributors.
   */
  data: Prisma.ContributorCreateManyInput | Prisma.ContributorCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ContributorIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * Contributor update
 */
export type ContributorUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Contributor
   */
  select?: Prisma.ContributorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Contributor
   */
  omit?: Prisma.ContributorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ContributorInclude<ExtArgs> | null
  /**
   * The data needed to update a Contributor.
   */
  data: Prisma.XOR<Prisma.ContributorUpdateInput, Prisma.ContributorUncheckedUpdateInput>
  /**
   * Choose, which Contributor to update.
   */
  where: Prisma.ContributorWhereUniqueInput
}

/**
 * Contributor updateMany
 */
export type ContributorUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Contributors.
   */
  data: Prisma.XOR<Prisma.ContributorUpdateManyMutationInput, Prisma.ContributorUncheckedUpdateManyInput>
  /**
   * Filter which Contributors to update
   */
  where?: Prisma.ContributorWhereInput
  /**
   * Limit how many Contributors to update.
   */
  limit?: number
}

/**
 * Contributor updateManyAndReturn
 */
export type ContributorUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Contributor
   */
  select?: Prisma.ContributorSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Contributor
   */
  omit?: Prisma.ContributorOmit<ExtArgs> | null
  /**
   * The data used to update Contributors.
   */
  data: Prisma.XOR<Prisma.ContributorUpdateManyMutationInput, Prisma.ContributorUncheckedUpdateManyInput>
  /**
   * Filter which Contributors to update
   */
  where?: Prisma.ContributorWhereInput
  /**
   * Limit how many Contributors to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ContributorIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * Contributor upsert
 */
export type ContributorUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Contributor
   */
  select?: Prisma.ContributorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Contributor
   */
  omit?: Prisma.ContributorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ContributorInclude<ExtArgs> | null
  /**
   * The filter to search for the Contributor to update in case it exists.
   */
  where: Prisma.ContributorWhereUniqueInput
  /**
   * In case the Contributor found by the `where` argument doesn't exist, create a new Contributor with this data.
   */
  create: Prisma.XOR<Prisma.ContributorCreateInput, Prisma.ContributorUncheckedCreateInput>
  /**
   * In case the Contributor was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.ContributorUpdateInput, Prisma.ContributorUncheckedUpdateInput>
}

/**
 * Contributor delete
 */
export type ContributorDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Contributor
   */
  select?: Prisma.ContributorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Contributor
   */
  omit?: Prisma.ContributorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ContributorInclude<ExtArgs> | null
  /**
   * Filter which Contributor to delete.
   */
  where: Prisma.ContributorWhereUniqueInput
}

/**
 * Contributor deleteMany
 */
export type ContributorDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Contributors to delete
   */
  where?: Prisma.ContributorWhereInput
  /**
   * Limit how many Contributors to delete.
   */
  limit?: number
}

/**
 * Contributor.tracks
 */
export type Contributor$tracksArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackContributor
   */
  select?: Prisma.TrackContributorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackContributor
   */
  omit?: Prisma.TrackContributorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackContributorInclude<ExtArgs> | null
  where?: Prisma.TrackContributorWhereInput
  orderBy?: Prisma.TrackContributorOrderByWithRelationInput | Prisma.TrackContributorOrderByWithRelationInput[]
  cursor?: Prisma.TrackContributorWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.TrackContributorScalarFieldEnum | Prisma.TrackContributorScalarFieldEnum[]
}

/**
 * Contributor without action
 */
export type ContributorDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Contributor
   */
  select?: Prisma.ContributorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Contributor
   */
  omit?: Prisma.ContributorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ContributorInclude<ExtArgs> | null
}
