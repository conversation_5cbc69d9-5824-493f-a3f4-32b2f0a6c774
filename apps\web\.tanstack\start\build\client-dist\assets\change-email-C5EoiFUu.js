import{u as b,b as p,a as C,r as l,j as e,L as y,t as h}from"./main-B9Fv5CdX.js";import{u as w}from"./useMutation-DGkS69KN.js";import{C as i,a as n,b as c,c as d,d as x}from"./card-PyhbSuya.js";import{B as o}from"./button-Ispz1G12.js";import{C as v}from"./circle-check-big-BA7hvIno.js";import{M as f}from"./mail-9LfZMTcO.js";import{C as k}from"./circle-x-Dhf4rEsb.js";const R=function(){const s=b(),{token:a}=p.useSearch(),j=C(),[m,r]=l.useState("loading"),[g,u]=l.useState(""),N=w({mutationFn:t=>j.email.verifyEmailChange.mutate({token:t}),onSuccess:()=>{r("success"),h.success("Email address changed successfully!"),setTimeout(()=>{s({to:"/dashboard"})},3e3)},onError:t=>{r("error"),u(t.message||"Failed to change email address. The link may be expired or invalid."),h.error("Email change failed")}});return l.useEffect(()=>{if(!a){r("error"),u("Invalid email change link. No token provided.");return}N.mutate(a)},[a]),m==="loading"?e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background",children:e.jsx(i,{className:"w-full max-w-md",children:e.jsxs(n,{className:"text-center",children:[e.jsx("div",{className:"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100",children:e.jsx(y,{className:"h-6 w-6 animate-spin text-blue-600"})}),e.jsx(c,{children:"Verifying Email Change"}),e.jsx(d,{children:"Please wait while we verify your email change request..."})]})})}):m==="success"?e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background",children:e.jsxs(i,{className:"w-full max-w-md",children:[e.jsxs(n,{className:"text-center",children:[e.jsx("div",{className:"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100",children:e.jsx(v,{className:"h-6 w-6 text-green-600"})}),e.jsx(c,{className:"text-green-900",children:"Email Changed Successfully!"}),e.jsx(d,{children:"Your email address has been updated successfully. You will be redirected to your dashboard shortly."})]}),e.jsx(x,{className:"text-center",children:e.jsxs(o,{onClick:()=>s({to:"/dashboard"}),className:"w-full",children:[e.jsx(f,{className:"h-4 w-4"}),"Go to Dashboard"]})})]})}):e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background",children:e.jsxs(i,{className:"w-full max-w-md",children:[e.jsxs(n,{className:"text-center",children:[e.jsx("div",{className:"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100",children:e.jsx(k,{className:"h-6 w-6 text-red-600"})}),e.jsx(c,{className:"text-red-900",children:"Email Change Failed"}),e.jsx(d,{className:"text-red-700",children:g})]}),e.jsxs(x,{className:"space-y-4",children:[e.jsxs("div",{className:"text-center text-sm text-muted-foreground",children:[e.jsx("p",{children:"Possible reasons for failure:"}),e.jsxs("ul",{className:"mt-2 text-left space-y-1",children:[e.jsx("li",{children:"• The verification link has expired"}),e.jsx("li",{children:"• The link has already been used"}),e.jsx("li",{children:"• The link is invalid or corrupted"})]})]}),e.jsxs("div",{className:"flex flex-col space-y-2",children:[e.jsxs(o,{onClick:()=>s({to:"/dashboard/profile"}),className:"w-full",children:[e.jsx(f,{className:"h-4 w-4"}),"Try Again from Profile"]}),e.jsx(o,{variant:"outline",onClick:()=>s({to:"/dashboard"}),className:"w-full",children:"Back to Dashboard"})]})]})]})})};export{R as component};
