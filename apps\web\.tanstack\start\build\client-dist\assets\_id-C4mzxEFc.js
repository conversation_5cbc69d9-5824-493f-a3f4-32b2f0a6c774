import{c as o,A as u,a as C,j as e,k as i,t as f,m as M}from"./main-B9Fv5CdX.js";import{u as _}from"./useQuery-CFAncLHa.js";import{a as P}from"./auth-client-C8WifPV2.js";import{B as d}from"./button-Ispz1G12.js";import{C as l,a as n,b as c,d as m}from"./card-PyhbSuya.js";import{U as B,P as D}from"./platform-badge-Dh7vRBtY.js";import{M as L,c as F}from"./genre-select-BveQTmBa.js";import{A as I,F as U,C as q}from"./file-text-BuKzmpXw.js";import{S as z}from"./square-pen-DzJw3_Uf.js";import{U as j}from"./user-DCDNZ7An.js";import{M as S}from"./mail-9LfZMTcO.js";import"./useMutation-DGkS69KN.js";import"./dialog-iGlJJq5Q.js";import"./label-CNQvdrLZ.js";import"./select-Cv6EF9My.js";import"./checkbox-Dk7Edl6C.js";import"./country-dropdown-DE40VcIC.js";import"./popover-DzeimUGg.js";import"./badge-B7y-QlNI.js";/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const T=[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]],E=o("Building",T);/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const R=[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]],$=o("Instagram",R);/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const G=[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]],H=o("MapPin",G),oe=function(){var p,g;const{id:r}=u.useParams(),x=u.useNavigate(),N=C(),{data:t,isPending:y}=P.useSession(),v=((p=t==null?void 0:t.user)==null?void 0:p.role)==="admin",{data:s,isLoading:k,error:w,refetch:h}=_({queryKey:["artist",r],queryFn:async()=>{const a=await N.artist.getById.query({id:r});return{...a,createdAt:new Date(a.createdAt),updatedAt:new Date(a.updatedAt)}},enabled:!!r});if(y)return e.jsx(i,{});if(!t||!t.user)return x({to:"/auth"}),e.jsx(i,{});if(k)return e.jsx(i,{});if(w)return f.error("Failed to load artist details"),e.jsxs("div",{className:"flex flex-col items-center justify-center h-96 space-y-4",children:[e.jsx("p",{className:"text-muted-foreground",children:"Failed to load artist details"}),e.jsx(d,{onClick:()=>h(),children:"Try Again"})]});if(!s)return e.jsxs("div",{className:"flex flex-col items-center justify-center h-96 space-y-4",children:[e.jsx("p",{className:"text-muted-foreground",children:"Artist not found"}),e.jsx(d,{onClick:()=>x({to:"/dashboard/artist"}),children:"Back to Artists"})]});const b=()=>{h(),f.success("Artist updated successfully")};return e.jsxs("div",{className:"container mx-auto p-2 space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs(M,{to:"/dashboard/artist",className:"inline-flex items-center text-sm text-muted-foreground hover:text-foreground hover:underline mb-4",children:[e.jsx(I,{className:"h-4 w-4 mr-1"}),"Back to Artists"]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold",children:s.name}),e.jsx("p",{className:"text-muted-foreground text-sm",children:"Artist Details"})]}),e.jsx(B,{artist:s,onArtistUpdated:b,children:e.jsxs(d,{children:[e.jsx(z,{className:"h-4 w-4"}),"Edit Artist"]})})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-[2fr_1fr] gap-2",children:[e.jsxs(l,{className:"gap-2 overflow-hidden",children:[e.jsx(n,{children:e.jsx(c,{className:"text-xl",children:"Basic Information"})}),e.jsx(m,{children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"grid grid-cols-[120px_1fr] gap-4 md:gap-20 items-start",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(j,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{className:"text-sm text-muted-foreground",children:"Artist Name"})]}),e.jsx("span",{className:"break-all",children:s.name})]}),e.jsxs("div",{className:"grid grid-cols-[120px_1fr] gap-4 md:gap-20 items-start",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(L,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{className:"text-sm text-muted-foreground",children:"Genre"})]}),e.jsx("span",{className:"break-all",children:s.genre?F(s.genre):"-"})]}),e.jsxs("div",{className:"grid grid-cols-[120px_1fr] gap-4 md:gap-20 items-start",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(H,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{className:"text-sm text-muted-foreground",children:"Country"})]}),e.jsx("span",{className:"break-all",children:s.country||"-"})]}),e.jsxs("div",{className:"grid grid-cols-[120px_1fr] gap-4 md:gap-20 items-start",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(E,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{className:"text-sm text-muted-foreground",children:"Label"})]}),e.jsx("span",{className:"break-all",children:((g=s.label)==null?void 0:g.name)||"-"})]}),e.jsxs("div",{className:"grid grid-cols-[120px_1fr] gap-4 md:gap-20 items-start",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx($,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{className:"text-sm text-muted-foreground",children:"Instagram"})]}),e.jsx("span",{className:"break-all",children:s.instagram?e.jsx("a",{href:`https://instagram.com/${s.instagram}`,target:"_blank",rel:"noopener noreferrer",className:"text-primary hover:underline break-all",children:s.instagram}):"-"})]}),e.jsxs("div",{className:"grid grid-cols-[120px_1fr] gap-4 md:gap-20 items-start",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(U,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{className:"text-sm text-muted-foreground",children:"Biography"})]}),e.jsx("span",{className:"break-all",children:s.biography})]}),v&&e.jsx(e.Fragment,{children:e.jsxs("div",{className:"border-t pt-3 mt-3 space-y-2",children:[e.jsxs("div",{className:"grid grid-cols-[120px_1fr] gap-4 md:gap-20 items-start",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(j,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{className:"text-sm text-muted-foreground",children:"Created By"})]}),e.jsx("span",{className:"break-all",children:s.user.name})]}),e.jsxs("div",{className:"grid grid-cols-[120px_1fr] gap-4 md:gap-20 items-start",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(S,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{className:"text-sm text-muted-foreground",children:"Email"})]}),e.jsx("span",{className:"break-all",children:s.user.email})]}),e.jsxs("div",{className:"grid grid-cols-[120px_1fr] gap-4 md:gap-20 items-start",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(q,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{className:"text-sm text-muted-foreground",children:"Created Date"})]}),e.jsx("span",{className:"break-all",children:s.createdAt.toLocaleDateString()})]})]})})]})})]}),e.jsxs(l,{className:"overflow-hidden gap-2",children:[e.jsx(n,{children:e.jsx(c,{children:"Platforms"})}),e.jsx(m,{children:s.identifiers&&s.identifiers.length>0?e.jsx("div",{className:"flex flex-col gap-3",children:s.identifiers.map((a,A)=>e.jsx(D,{service:a.service,identifier:a.identifier,width:20,height:20,className:"text-sm justify-start w-full h-10"},A))}):e.jsx("p",{className:"text-muted-foreground",children:"No platforms configured"})})]})]}),e.jsxs(l,{children:[e.jsx(n,{children:e.jsx(c,{children:"Projects (to-do later)"})}),e.jsx(m,{children:e.jsx("p",{className:"text-muted-foreground",children:"This section will contain artist projects and releases."})})]})]})};export{oe as component};
