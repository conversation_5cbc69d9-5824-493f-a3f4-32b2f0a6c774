
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `User` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model User
 * 
 */
export type UserModel = runtime.Types.Result.DefaultSelection<Prisma.$UserPayload>

export type AggregateUser = {
  _count: UserCountAggregateOutputType | null
  _min: UserMinAggregateOutputType | null
  _max: UserMaxAggregateOutputType | null
}

export type UserMinAggregateOutputType = {
  id: string | null
  name: string | null
  email: string | null
  emailVerified: boolean | null
  image: string | null
  phone: string | null
  role: string | null
  banned: boolean | null
  banReason: string | null
  banExpires: Date | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type UserMaxAggregateOutputType = {
  id: string | null
  name: string | null
  email: string | null
  emailVerified: boolean | null
  image: string | null
  phone: string | null
  role: string | null
  banned: boolean | null
  banReason: string | null
  banExpires: Date | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type UserCountAggregateOutputType = {
  id: number
  name: number
  email: number
  emailVerified: number
  image: number
  phone: number
  role: number
  banned: number
  banReason: number
  banExpires: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type UserMinAggregateInputType = {
  id?: true
  name?: true
  email?: true
  emailVerified?: true
  image?: true
  phone?: true
  role?: true
  banned?: true
  banReason?: true
  banExpires?: true
  createdAt?: true
  updatedAt?: true
}

export type UserMaxAggregateInputType = {
  id?: true
  name?: true
  email?: true
  emailVerified?: true
  image?: true
  phone?: true
  role?: true
  banned?: true
  banReason?: true
  banExpires?: true
  createdAt?: true
  updatedAt?: true
}

export type UserCountAggregateInputType = {
  id?: true
  name?: true
  email?: true
  emailVerified?: true
  image?: true
  phone?: true
  role?: true
  banned?: true
  banReason?: true
  banExpires?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type UserAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which User to aggregate.
   */
  where?: Prisma.UserWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Users to fetch.
   */
  orderBy?: Prisma.UserOrderByWithRelationInput | Prisma.UserOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.UserWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Users from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Users.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Users
  **/
  _count?: true | UserCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: UserMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: UserMaxAggregateInputType
}

export type GetUserAggregateType<T extends UserAggregateArgs> = {
      [P in keyof T & keyof AggregateUser]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateUser[P]>
    : Prisma.GetScalarType<T[P], AggregateUser[P]>
}




export type UserGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.UserWhereInput
  orderBy?: Prisma.UserOrderByWithAggregationInput | Prisma.UserOrderByWithAggregationInput[]
  by: Prisma.UserScalarFieldEnum[] | Prisma.UserScalarFieldEnum
  having?: Prisma.UserScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: UserCountAggregateInputType | true
  _min?: UserMinAggregateInputType
  _max?: UserMaxAggregateInputType
}

export type UserGroupByOutputType = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image: string | null
  phone: string | null
  role: string | null
  banned: boolean | null
  banReason: string | null
  banExpires: Date | null
  createdAt: Date
  updatedAt: Date
  _count: UserCountAggregateOutputType | null
  _min: UserMinAggregateOutputType | null
  _max: UserMaxAggregateOutputType | null
}

type GetUserGroupByPayload<T extends UserGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<UserGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof UserGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], UserGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], UserGroupByOutputType[P]>
      }
    >
  > 



export type UserWhereInput = {
  AND?: Prisma.UserWhereInput | Prisma.UserWhereInput[]
  OR?: Prisma.UserWhereInput[]
  NOT?: Prisma.UserWhereInput | Prisma.UserWhereInput[]
  id?: Prisma.StringFilter<"User"> | string
  name?: Prisma.StringFilter<"User"> | string
  email?: Prisma.StringFilter<"User"> | string
  emailVerified?: Prisma.BoolFilter<"User"> | boolean
  image?: Prisma.StringNullableFilter<"User"> | string | null
  phone?: Prisma.StringNullableFilter<"User"> | string | null
  role?: Prisma.StringNullableFilter<"User"> | string | null
  banned?: Prisma.BoolNullableFilter<"User"> | boolean | null
  banReason?: Prisma.StringNullableFilter<"User"> | string | null
  banExpires?: Prisma.DateTimeNullableFilter<"User"> | Date | string | null
  createdAt?: Prisma.DateTimeFilter<"User"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"User"> | Date | string
  labels?: Prisma.LabelListRelationFilter
  artists?: Prisma.ArtistListRelationFilter
  contributors?: Prisma.ContributorListRelationFilter
  releases?: Prisma.ReleaseListRelationFilter
  tracks?: Prisma.TrackListRelationFilter
  sessions?: Prisma.SessionListRelationFilter
  accounts?: Prisma.AccountListRelationFilter
}

export type UserOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  email?: Prisma.SortOrder
  emailVerified?: Prisma.SortOrder
  image?: Prisma.SortOrderInput | Prisma.SortOrder
  phone?: Prisma.SortOrderInput | Prisma.SortOrder
  role?: Prisma.SortOrderInput | Prisma.SortOrder
  banned?: Prisma.SortOrderInput | Prisma.SortOrder
  banReason?: Prisma.SortOrderInput | Prisma.SortOrder
  banExpires?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  labels?: Prisma.LabelOrderByRelationAggregateInput
  artists?: Prisma.ArtistOrderByRelationAggregateInput
  contributors?: Prisma.ContributorOrderByRelationAggregateInput
  releases?: Prisma.ReleaseOrderByRelationAggregateInput
  tracks?: Prisma.TrackOrderByRelationAggregateInput
  sessions?: Prisma.SessionOrderByRelationAggregateInput
  accounts?: Prisma.AccountOrderByRelationAggregateInput
}

export type UserWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  email?: string
  AND?: Prisma.UserWhereInput | Prisma.UserWhereInput[]
  OR?: Prisma.UserWhereInput[]
  NOT?: Prisma.UserWhereInput | Prisma.UserWhereInput[]
  name?: Prisma.StringFilter<"User"> | string
  emailVerified?: Prisma.BoolFilter<"User"> | boolean
  image?: Prisma.StringNullableFilter<"User"> | string | null
  phone?: Prisma.StringNullableFilter<"User"> | string | null
  role?: Prisma.StringNullableFilter<"User"> | string | null
  banned?: Prisma.BoolNullableFilter<"User"> | boolean | null
  banReason?: Prisma.StringNullableFilter<"User"> | string | null
  banExpires?: Prisma.DateTimeNullableFilter<"User"> | Date | string | null
  createdAt?: Prisma.DateTimeFilter<"User"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"User"> | Date | string
  labels?: Prisma.LabelListRelationFilter
  artists?: Prisma.ArtistListRelationFilter
  contributors?: Prisma.ContributorListRelationFilter
  releases?: Prisma.ReleaseListRelationFilter
  tracks?: Prisma.TrackListRelationFilter
  sessions?: Prisma.SessionListRelationFilter
  accounts?: Prisma.AccountListRelationFilter
}, "id" | "email">

export type UserOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  email?: Prisma.SortOrder
  emailVerified?: Prisma.SortOrder
  image?: Prisma.SortOrderInput | Prisma.SortOrder
  phone?: Prisma.SortOrderInput | Prisma.SortOrder
  role?: Prisma.SortOrderInput | Prisma.SortOrder
  banned?: Prisma.SortOrderInput | Prisma.SortOrder
  banReason?: Prisma.SortOrderInput | Prisma.SortOrder
  banExpires?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.UserCountOrderByAggregateInput
  _max?: Prisma.UserMaxOrderByAggregateInput
  _min?: Prisma.UserMinOrderByAggregateInput
}

export type UserScalarWhereWithAggregatesInput = {
  AND?: Prisma.UserScalarWhereWithAggregatesInput | Prisma.UserScalarWhereWithAggregatesInput[]
  OR?: Prisma.UserScalarWhereWithAggregatesInput[]
  NOT?: Prisma.UserScalarWhereWithAggregatesInput | Prisma.UserScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"User"> | string
  name?: Prisma.StringWithAggregatesFilter<"User"> | string
  email?: Prisma.StringWithAggregatesFilter<"User"> | string
  emailVerified?: Prisma.BoolWithAggregatesFilter<"User"> | boolean
  image?: Prisma.StringNullableWithAggregatesFilter<"User"> | string | null
  phone?: Prisma.StringNullableWithAggregatesFilter<"User"> | string | null
  role?: Prisma.StringNullableWithAggregatesFilter<"User"> | string | null
  banned?: Prisma.BoolNullableWithAggregatesFilter<"User"> | boolean | null
  banReason?: Prisma.StringNullableWithAggregatesFilter<"User"> | string | null
  banExpires?: Prisma.DateTimeNullableWithAggregatesFilter<"User"> | Date | string | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"User"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"User"> | Date | string
}

export type UserCreateInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  phone?: string | null
  role?: string | null
  banned?: boolean | null
  banReason?: string | null
  banExpires?: Date | string | null
  createdAt: Date | string
  updatedAt: Date | string
  labels?: Prisma.LabelCreateNestedManyWithoutUserInput
  artists?: Prisma.ArtistCreateNestedManyWithoutUserInput
  contributors?: Prisma.ContributorCreateNestedManyWithoutUserInput
  releases?: Prisma.ReleaseCreateNestedManyWithoutUserInput
  tracks?: Prisma.TrackCreateNestedManyWithoutUserInput
  sessions?: Prisma.SessionCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  phone?: string | null
  role?: string | null
  banned?: boolean | null
  banReason?: string | null
  banExpires?: Date | string | null
  createdAt: Date | string
  updatedAt: Date | string
  labels?: Prisma.LabelUncheckedCreateNestedManyWithoutUserInput
  artists?: Prisma.ArtistUncheckedCreateNestedManyWithoutUserInput
  contributors?: Prisma.ContributorUncheckedCreateNestedManyWithoutUserInput
  releases?: Prisma.ReleaseUncheckedCreateNestedManyWithoutUserInput
  tracks?: Prisma.TrackUncheckedCreateNestedManyWithoutUserInput
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountUncheckedCreateNestedManyWithoutUserInput
}

export type UserUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  labels?: Prisma.LabelUpdateManyWithoutUserNestedInput
  artists?: Prisma.ArtistUpdateManyWithoutUserNestedInput
  contributors?: Prisma.ContributorUpdateManyWithoutUserNestedInput
  releases?: Prisma.ReleaseUpdateManyWithoutUserNestedInput
  tracks?: Prisma.TrackUpdateManyWithoutUserNestedInput
  sessions?: Prisma.SessionUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  labels?: Prisma.LabelUncheckedUpdateManyWithoutUserNestedInput
  artists?: Prisma.ArtistUncheckedUpdateManyWithoutUserNestedInput
  contributors?: Prisma.ContributorUncheckedUpdateManyWithoutUserNestedInput
  releases?: Prisma.ReleaseUncheckedUpdateManyWithoutUserNestedInput
  tracks?: Prisma.TrackUncheckedUpdateManyWithoutUserNestedInput
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUncheckedUpdateManyWithoutUserNestedInput
}

export type UserCreateManyInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  phone?: string | null
  role?: string | null
  banned?: boolean | null
  banReason?: string | null
  banExpires?: Date | string | null
  createdAt: Date | string
  updatedAt: Date | string
}

export type UserUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type UserUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type UserScalarRelationFilter = {
  is?: Prisma.UserWhereInput
  isNot?: Prisma.UserWhereInput
}

export type UserCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  email?: Prisma.SortOrder
  emailVerified?: Prisma.SortOrder
  image?: Prisma.SortOrder
  phone?: Prisma.SortOrder
  role?: Prisma.SortOrder
  banned?: Prisma.SortOrder
  banReason?: Prisma.SortOrder
  banExpires?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type UserMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  email?: Prisma.SortOrder
  emailVerified?: Prisma.SortOrder
  image?: Prisma.SortOrder
  phone?: Prisma.SortOrder
  role?: Prisma.SortOrder
  banned?: Prisma.SortOrder
  banReason?: Prisma.SortOrder
  banExpires?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type UserMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  email?: Prisma.SortOrder
  emailVerified?: Prisma.SortOrder
  image?: Prisma.SortOrder
  phone?: Prisma.SortOrder
  role?: Prisma.SortOrder
  banned?: Prisma.SortOrder
  banReason?: Prisma.SortOrder
  banExpires?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type UserCreateNestedOneWithoutArtistsInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutArtistsInput, Prisma.UserUncheckedCreateWithoutArtistsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutArtistsInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutArtistsNestedInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutArtistsInput, Prisma.UserUncheckedCreateWithoutArtistsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutArtistsInput
  upsert?: Prisma.UserUpsertWithoutArtistsInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UserUpdateToOneWithWhereWithoutArtistsInput, Prisma.UserUpdateWithoutArtistsInput>, Prisma.UserUncheckedUpdateWithoutArtistsInput>
}

export type BoolFieldUpdateOperationsInput = {
  set?: boolean
}

export type NullableBoolFieldUpdateOperationsInput = {
  set?: boolean | null
}

export type NullableDateTimeFieldUpdateOperationsInput = {
  set?: Date | string | null
}

export type UserCreateNestedOneWithoutSessionsInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutSessionsInput, Prisma.UserUncheckedCreateWithoutSessionsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutSessionsInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutSessionsNestedInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutSessionsInput, Prisma.UserUncheckedCreateWithoutSessionsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutSessionsInput
  upsert?: Prisma.UserUpsertWithoutSessionsInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UserUpdateToOneWithWhereWithoutSessionsInput, Prisma.UserUpdateWithoutSessionsInput>, Prisma.UserUncheckedUpdateWithoutSessionsInput>
}

export type UserCreateNestedOneWithoutAccountsInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutAccountsInput, Prisma.UserUncheckedCreateWithoutAccountsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutAccountsInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutAccountsNestedInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutAccountsInput, Prisma.UserUncheckedCreateWithoutAccountsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutAccountsInput
  upsert?: Prisma.UserUpsertWithoutAccountsInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UserUpdateToOneWithWhereWithoutAccountsInput, Prisma.UserUpdateWithoutAccountsInput>, Prisma.UserUncheckedUpdateWithoutAccountsInput>
}

export type UserCreateNestedOneWithoutContributorsInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutContributorsInput, Prisma.UserUncheckedCreateWithoutContributorsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutContributorsInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutContributorsNestedInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutContributorsInput, Prisma.UserUncheckedCreateWithoutContributorsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutContributorsInput
  upsert?: Prisma.UserUpsertWithoutContributorsInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UserUpdateToOneWithWhereWithoutContributorsInput, Prisma.UserUpdateWithoutContributorsInput>, Prisma.UserUncheckedUpdateWithoutContributorsInput>
}

export type UserCreateNestedOneWithoutLabelsInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutLabelsInput, Prisma.UserUncheckedCreateWithoutLabelsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutLabelsInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutLabelsNestedInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutLabelsInput, Prisma.UserUncheckedCreateWithoutLabelsInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutLabelsInput
  upsert?: Prisma.UserUpsertWithoutLabelsInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UserUpdateToOneWithWhereWithoutLabelsInput, Prisma.UserUpdateWithoutLabelsInput>, Prisma.UserUncheckedUpdateWithoutLabelsInput>
}

export type UserCreateNestedOneWithoutReleasesInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutReleasesInput, Prisma.UserUncheckedCreateWithoutReleasesInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutReleasesInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutReleasesNestedInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutReleasesInput, Prisma.UserUncheckedCreateWithoutReleasesInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutReleasesInput
  upsert?: Prisma.UserUpsertWithoutReleasesInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UserUpdateToOneWithWhereWithoutReleasesInput, Prisma.UserUpdateWithoutReleasesInput>, Prisma.UserUncheckedUpdateWithoutReleasesInput>
}

export type UserCreateNestedOneWithoutTracksInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutTracksInput, Prisma.UserUncheckedCreateWithoutTracksInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutTracksInput
  connect?: Prisma.UserWhereUniqueInput
}

export type UserUpdateOneRequiredWithoutTracksNestedInput = {
  create?: Prisma.XOR<Prisma.UserCreateWithoutTracksInput, Prisma.UserUncheckedCreateWithoutTracksInput>
  connectOrCreate?: Prisma.UserCreateOrConnectWithoutTracksInput
  upsert?: Prisma.UserUpsertWithoutTracksInput
  connect?: Prisma.UserWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.UserUpdateToOneWithWhereWithoutTracksInput, Prisma.UserUpdateWithoutTracksInput>, Prisma.UserUncheckedUpdateWithoutTracksInput>
}

export type UserCreateWithoutArtistsInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  phone?: string | null
  role?: string | null
  banned?: boolean | null
  banReason?: string | null
  banExpires?: Date | string | null
  createdAt: Date | string
  updatedAt: Date | string
  labels?: Prisma.LabelCreateNestedManyWithoutUserInput
  contributors?: Prisma.ContributorCreateNestedManyWithoutUserInput
  releases?: Prisma.ReleaseCreateNestedManyWithoutUserInput
  tracks?: Prisma.TrackCreateNestedManyWithoutUserInput
  sessions?: Prisma.SessionCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateWithoutArtistsInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  phone?: string | null
  role?: string | null
  banned?: boolean | null
  banReason?: string | null
  banExpires?: Date | string | null
  createdAt: Date | string
  updatedAt: Date | string
  labels?: Prisma.LabelUncheckedCreateNestedManyWithoutUserInput
  contributors?: Prisma.ContributorUncheckedCreateNestedManyWithoutUserInput
  releases?: Prisma.ReleaseUncheckedCreateNestedManyWithoutUserInput
  tracks?: Prisma.TrackUncheckedCreateNestedManyWithoutUserInput
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountUncheckedCreateNestedManyWithoutUserInput
}

export type UserCreateOrConnectWithoutArtistsInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<Prisma.UserCreateWithoutArtistsInput, Prisma.UserUncheckedCreateWithoutArtistsInput>
}

export type UserUpsertWithoutArtistsInput = {
  update: Prisma.XOR<Prisma.UserUpdateWithoutArtistsInput, Prisma.UserUncheckedUpdateWithoutArtistsInput>
  create: Prisma.XOR<Prisma.UserCreateWithoutArtistsInput, Prisma.UserUncheckedCreateWithoutArtistsInput>
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutArtistsInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<Prisma.UserUpdateWithoutArtistsInput, Prisma.UserUncheckedUpdateWithoutArtistsInput>
}

export type UserUpdateWithoutArtistsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  labels?: Prisma.LabelUpdateManyWithoutUserNestedInput
  contributors?: Prisma.ContributorUpdateManyWithoutUserNestedInput
  releases?: Prisma.ReleaseUpdateManyWithoutUserNestedInput
  tracks?: Prisma.TrackUpdateManyWithoutUserNestedInput
  sessions?: Prisma.SessionUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateWithoutArtistsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  labels?: Prisma.LabelUncheckedUpdateManyWithoutUserNestedInput
  contributors?: Prisma.ContributorUncheckedUpdateManyWithoutUserNestedInput
  releases?: Prisma.ReleaseUncheckedUpdateManyWithoutUserNestedInput
  tracks?: Prisma.TrackUncheckedUpdateManyWithoutUserNestedInput
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUncheckedUpdateManyWithoutUserNestedInput
}

export type UserCreateWithoutSessionsInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  phone?: string | null
  role?: string | null
  banned?: boolean | null
  banReason?: string | null
  banExpires?: Date | string | null
  createdAt: Date | string
  updatedAt: Date | string
  labels?: Prisma.LabelCreateNestedManyWithoutUserInput
  artists?: Prisma.ArtistCreateNestedManyWithoutUserInput
  contributors?: Prisma.ContributorCreateNestedManyWithoutUserInput
  releases?: Prisma.ReleaseCreateNestedManyWithoutUserInput
  tracks?: Prisma.TrackCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateWithoutSessionsInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  phone?: string | null
  role?: string | null
  banned?: boolean | null
  banReason?: string | null
  banExpires?: Date | string | null
  createdAt: Date | string
  updatedAt: Date | string
  labels?: Prisma.LabelUncheckedCreateNestedManyWithoutUserInput
  artists?: Prisma.ArtistUncheckedCreateNestedManyWithoutUserInput
  contributors?: Prisma.ContributorUncheckedCreateNestedManyWithoutUserInput
  releases?: Prisma.ReleaseUncheckedCreateNestedManyWithoutUserInput
  tracks?: Prisma.TrackUncheckedCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountUncheckedCreateNestedManyWithoutUserInput
}

export type UserCreateOrConnectWithoutSessionsInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<Prisma.UserCreateWithoutSessionsInput, Prisma.UserUncheckedCreateWithoutSessionsInput>
}

export type UserUpsertWithoutSessionsInput = {
  update: Prisma.XOR<Prisma.UserUpdateWithoutSessionsInput, Prisma.UserUncheckedUpdateWithoutSessionsInput>
  create: Prisma.XOR<Prisma.UserCreateWithoutSessionsInput, Prisma.UserUncheckedCreateWithoutSessionsInput>
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutSessionsInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<Prisma.UserUpdateWithoutSessionsInput, Prisma.UserUncheckedUpdateWithoutSessionsInput>
}

export type UserUpdateWithoutSessionsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  labels?: Prisma.LabelUpdateManyWithoutUserNestedInput
  artists?: Prisma.ArtistUpdateManyWithoutUserNestedInput
  contributors?: Prisma.ContributorUpdateManyWithoutUserNestedInput
  releases?: Prisma.ReleaseUpdateManyWithoutUserNestedInput
  tracks?: Prisma.TrackUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateWithoutSessionsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  labels?: Prisma.LabelUncheckedUpdateManyWithoutUserNestedInput
  artists?: Prisma.ArtistUncheckedUpdateManyWithoutUserNestedInput
  contributors?: Prisma.ContributorUncheckedUpdateManyWithoutUserNestedInput
  releases?: Prisma.ReleaseUncheckedUpdateManyWithoutUserNestedInput
  tracks?: Prisma.TrackUncheckedUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUncheckedUpdateManyWithoutUserNestedInput
}

export type UserCreateWithoutAccountsInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  phone?: string | null
  role?: string | null
  banned?: boolean | null
  banReason?: string | null
  banExpires?: Date | string | null
  createdAt: Date | string
  updatedAt: Date | string
  labels?: Prisma.LabelCreateNestedManyWithoutUserInput
  artists?: Prisma.ArtistCreateNestedManyWithoutUserInput
  contributors?: Prisma.ContributorCreateNestedManyWithoutUserInput
  releases?: Prisma.ReleaseCreateNestedManyWithoutUserInput
  tracks?: Prisma.TrackCreateNestedManyWithoutUserInput
  sessions?: Prisma.SessionCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateWithoutAccountsInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  phone?: string | null
  role?: string | null
  banned?: boolean | null
  banReason?: string | null
  banExpires?: Date | string | null
  createdAt: Date | string
  updatedAt: Date | string
  labels?: Prisma.LabelUncheckedCreateNestedManyWithoutUserInput
  artists?: Prisma.ArtistUncheckedCreateNestedManyWithoutUserInput
  contributors?: Prisma.ContributorUncheckedCreateNestedManyWithoutUserInput
  releases?: Prisma.ReleaseUncheckedCreateNestedManyWithoutUserInput
  tracks?: Prisma.TrackUncheckedCreateNestedManyWithoutUserInput
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutUserInput
}

export type UserCreateOrConnectWithoutAccountsInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<Prisma.UserCreateWithoutAccountsInput, Prisma.UserUncheckedCreateWithoutAccountsInput>
}

export type UserUpsertWithoutAccountsInput = {
  update: Prisma.XOR<Prisma.UserUpdateWithoutAccountsInput, Prisma.UserUncheckedUpdateWithoutAccountsInput>
  create: Prisma.XOR<Prisma.UserCreateWithoutAccountsInput, Prisma.UserUncheckedCreateWithoutAccountsInput>
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutAccountsInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<Prisma.UserUpdateWithoutAccountsInput, Prisma.UserUncheckedUpdateWithoutAccountsInput>
}

export type UserUpdateWithoutAccountsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  labels?: Prisma.LabelUpdateManyWithoutUserNestedInput
  artists?: Prisma.ArtistUpdateManyWithoutUserNestedInput
  contributors?: Prisma.ContributorUpdateManyWithoutUserNestedInput
  releases?: Prisma.ReleaseUpdateManyWithoutUserNestedInput
  tracks?: Prisma.TrackUpdateManyWithoutUserNestedInput
  sessions?: Prisma.SessionUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateWithoutAccountsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  labels?: Prisma.LabelUncheckedUpdateManyWithoutUserNestedInput
  artists?: Prisma.ArtistUncheckedUpdateManyWithoutUserNestedInput
  contributors?: Prisma.ContributorUncheckedUpdateManyWithoutUserNestedInput
  releases?: Prisma.ReleaseUncheckedUpdateManyWithoutUserNestedInput
  tracks?: Prisma.TrackUncheckedUpdateManyWithoutUserNestedInput
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutUserNestedInput
}

export type UserCreateWithoutContributorsInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  phone?: string | null
  role?: string | null
  banned?: boolean | null
  banReason?: string | null
  banExpires?: Date | string | null
  createdAt: Date | string
  updatedAt: Date | string
  labels?: Prisma.LabelCreateNestedManyWithoutUserInput
  artists?: Prisma.ArtistCreateNestedManyWithoutUserInput
  releases?: Prisma.ReleaseCreateNestedManyWithoutUserInput
  tracks?: Prisma.TrackCreateNestedManyWithoutUserInput
  sessions?: Prisma.SessionCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateWithoutContributorsInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  phone?: string | null
  role?: string | null
  banned?: boolean | null
  banReason?: string | null
  banExpires?: Date | string | null
  createdAt: Date | string
  updatedAt: Date | string
  labels?: Prisma.LabelUncheckedCreateNestedManyWithoutUserInput
  artists?: Prisma.ArtistUncheckedCreateNestedManyWithoutUserInput
  releases?: Prisma.ReleaseUncheckedCreateNestedManyWithoutUserInput
  tracks?: Prisma.TrackUncheckedCreateNestedManyWithoutUserInput
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountUncheckedCreateNestedManyWithoutUserInput
}

export type UserCreateOrConnectWithoutContributorsInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<Prisma.UserCreateWithoutContributorsInput, Prisma.UserUncheckedCreateWithoutContributorsInput>
}

export type UserUpsertWithoutContributorsInput = {
  update: Prisma.XOR<Prisma.UserUpdateWithoutContributorsInput, Prisma.UserUncheckedUpdateWithoutContributorsInput>
  create: Prisma.XOR<Prisma.UserCreateWithoutContributorsInput, Prisma.UserUncheckedCreateWithoutContributorsInput>
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutContributorsInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<Prisma.UserUpdateWithoutContributorsInput, Prisma.UserUncheckedUpdateWithoutContributorsInput>
}

export type UserUpdateWithoutContributorsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  labels?: Prisma.LabelUpdateManyWithoutUserNestedInput
  artists?: Prisma.ArtistUpdateManyWithoutUserNestedInput
  releases?: Prisma.ReleaseUpdateManyWithoutUserNestedInput
  tracks?: Prisma.TrackUpdateManyWithoutUserNestedInput
  sessions?: Prisma.SessionUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateWithoutContributorsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  labels?: Prisma.LabelUncheckedUpdateManyWithoutUserNestedInput
  artists?: Prisma.ArtistUncheckedUpdateManyWithoutUserNestedInput
  releases?: Prisma.ReleaseUncheckedUpdateManyWithoutUserNestedInput
  tracks?: Prisma.TrackUncheckedUpdateManyWithoutUserNestedInput
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUncheckedUpdateManyWithoutUserNestedInput
}

export type UserCreateWithoutLabelsInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  phone?: string | null
  role?: string | null
  banned?: boolean | null
  banReason?: string | null
  banExpires?: Date | string | null
  createdAt: Date | string
  updatedAt: Date | string
  artists?: Prisma.ArtistCreateNestedManyWithoutUserInput
  contributors?: Prisma.ContributorCreateNestedManyWithoutUserInput
  releases?: Prisma.ReleaseCreateNestedManyWithoutUserInput
  tracks?: Prisma.TrackCreateNestedManyWithoutUserInput
  sessions?: Prisma.SessionCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateWithoutLabelsInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  phone?: string | null
  role?: string | null
  banned?: boolean | null
  banReason?: string | null
  banExpires?: Date | string | null
  createdAt: Date | string
  updatedAt: Date | string
  artists?: Prisma.ArtistUncheckedCreateNestedManyWithoutUserInput
  contributors?: Prisma.ContributorUncheckedCreateNestedManyWithoutUserInput
  releases?: Prisma.ReleaseUncheckedCreateNestedManyWithoutUserInput
  tracks?: Prisma.TrackUncheckedCreateNestedManyWithoutUserInput
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountUncheckedCreateNestedManyWithoutUserInput
}

export type UserCreateOrConnectWithoutLabelsInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<Prisma.UserCreateWithoutLabelsInput, Prisma.UserUncheckedCreateWithoutLabelsInput>
}

export type UserUpsertWithoutLabelsInput = {
  update: Prisma.XOR<Prisma.UserUpdateWithoutLabelsInput, Prisma.UserUncheckedUpdateWithoutLabelsInput>
  create: Prisma.XOR<Prisma.UserCreateWithoutLabelsInput, Prisma.UserUncheckedCreateWithoutLabelsInput>
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutLabelsInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<Prisma.UserUpdateWithoutLabelsInput, Prisma.UserUncheckedUpdateWithoutLabelsInput>
}

export type UserUpdateWithoutLabelsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  artists?: Prisma.ArtistUpdateManyWithoutUserNestedInput
  contributors?: Prisma.ContributorUpdateManyWithoutUserNestedInput
  releases?: Prisma.ReleaseUpdateManyWithoutUserNestedInput
  tracks?: Prisma.TrackUpdateManyWithoutUserNestedInput
  sessions?: Prisma.SessionUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateWithoutLabelsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  artists?: Prisma.ArtistUncheckedUpdateManyWithoutUserNestedInput
  contributors?: Prisma.ContributorUncheckedUpdateManyWithoutUserNestedInput
  releases?: Prisma.ReleaseUncheckedUpdateManyWithoutUserNestedInput
  tracks?: Prisma.TrackUncheckedUpdateManyWithoutUserNestedInput
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUncheckedUpdateManyWithoutUserNestedInput
}

export type UserCreateWithoutReleasesInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  phone?: string | null
  role?: string | null
  banned?: boolean | null
  banReason?: string | null
  banExpires?: Date | string | null
  createdAt: Date | string
  updatedAt: Date | string
  labels?: Prisma.LabelCreateNestedManyWithoutUserInput
  artists?: Prisma.ArtistCreateNestedManyWithoutUserInput
  contributors?: Prisma.ContributorCreateNestedManyWithoutUserInput
  tracks?: Prisma.TrackCreateNestedManyWithoutUserInput
  sessions?: Prisma.SessionCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateWithoutReleasesInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  phone?: string | null
  role?: string | null
  banned?: boolean | null
  banReason?: string | null
  banExpires?: Date | string | null
  createdAt: Date | string
  updatedAt: Date | string
  labels?: Prisma.LabelUncheckedCreateNestedManyWithoutUserInput
  artists?: Prisma.ArtistUncheckedCreateNestedManyWithoutUserInput
  contributors?: Prisma.ContributorUncheckedCreateNestedManyWithoutUserInput
  tracks?: Prisma.TrackUncheckedCreateNestedManyWithoutUserInput
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountUncheckedCreateNestedManyWithoutUserInput
}

export type UserCreateOrConnectWithoutReleasesInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<Prisma.UserCreateWithoutReleasesInput, Prisma.UserUncheckedCreateWithoutReleasesInput>
}

export type UserUpsertWithoutReleasesInput = {
  update: Prisma.XOR<Prisma.UserUpdateWithoutReleasesInput, Prisma.UserUncheckedUpdateWithoutReleasesInput>
  create: Prisma.XOR<Prisma.UserCreateWithoutReleasesInput, Prisma.UserUncheckedCreateWithoutReleasesInput>
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutReleasesInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<Prisma.UserUpdateWithoutReleasesInput, Prisma.UserUncheckedUpdateWithoutReleasesInput>
}

export type UserUpdateWithoutReleasesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  labels?: Prisma.LabelUpdateManyWithoutUserNestedInput
  artists?: Prisma.ArtistUpdateManyWithoutUserNestedInput
  contributors?: Prisma.ContributorUpdateManyWithoutUserNestedInput
  tracks?: Prisma.TrackUpdateManyWithoutUserNestedInput
  sessions?: Prisma.SessionUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateWithoutReleasesInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  labels?: Prisma.LabelUncheckedUpdateManyWithoutUserNestedInput
  artists?: Prisma.ArtistUncheckedUpdateManyWithoutUserNestedInput
  contributors?: Prisma.ContributorUncheckedUpdateManyWithoutUserNestedInput
  tracks?: Prisma.TrackUncheckedUpdateManyWithoutUserNestedInput
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUncheckedUpdateManyWithoutUserNestedInput
}

export type UserCreateWithoutTracksInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  phone?: string | null
  role?: string | null
  banned?: boolean | null
  banReason?: string | null
  banExpires?: Date | string | null
  createdAt: Date | string
  updatedAt: Date | string
  labels?: Prisma.LabelCreateNestedManyWithoutUserInput
  artists?: Prisma.ArtistCreateNestedManyWithoutUserInput
  contributors?: Prisma.ContributorCreateNestedManyWithoutUserInput
  releases?: Prisma.ReleaseCreateNestedManyWithoutUserInput
  sessions?: Prisma.SessionCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountCreateNestedManyWithoutUserInput
}

export type UserUncheckedCreateWithoutTracksInput = {
  id: string
  name: string
  email: string
  emailVerified: boolean
  image?: string | null
  phone?: string | null
  role?: string | null
  banned?: boolean | null
  banReason?: string | null
  banExpires?: Date | string | null
  createdAt: Date | string
  updatedAt: Date | string
  labels?: Prisma.LabelUncheckedCreateNestedManyWithoutUserInput
  artists?: Prisma.ArtistUncheckedCreateNestedManyWithoutUserInput
  contributors?: Prisma.ContributorUncheckedCreateNestedManyWithoutUserInput
  releases?: Prisma.ReleaseUncheckedCreateNestedManyWithoutUserInput
  sessions?: Prisma.SessionUncheckedCreateNestedManyWithoutUserInput
  accounts?: Prisma.AccountUncheckedCreateNestedManyWithoutUserInput
}

export type UserCreateOrConnectWithoutTracksInput = {
  where: Prisma.UserWhereUniqueInput
  create: Prisma.XOR<Prisma.UserCreateWithoutTracksInput, Prisma.UserUncheckedCreateWithoutTracksInput>
}

export type UserUpsertWithoutTracksInput = {
  update: Prisma.XOR<Prisma.UserUpdateWithoutTracksInput, Prisma.UserUncheckedUpdateWithoutTracksInput>
  create: Prisma.XOR<Prisma.UserCreateWithoutTracksInput, Prisma.UserUncheckedCreateWithoutTracksInput>
  where?: Prisma.UserWhereInput
}

export type UserUpdateToOneWithWhereWithoutTracksInput = {
  where?: Prisma.UserWhereInput
  data: Prisma.XOR<Prisma.UserUpdateWithoutTracksInput, Prisma.UserUncheckedUpdateWithoutTracksInput>
}

export type UserUpdateWithoutTracksInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  labels?: Prisma.LabelUpdateManyWithoutUserNestedInput
  artists?: Prisma.ArtistUpdateManyWithoutUserNestedInput
  contributors?: Prisma.ContributorUpdateManyWithoutUserNestedInput
  releases?: Prisma.ReleaseUpdateManyWithoutUserNestedInput
  sessions?: Prisma.SessionUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUpdateManyWithoutUserNestedInput
}

export type UserUncheckedUpdateWithoutTracksInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  email?: Prisma.StringFieldUpdateOperationsInput | string
  emailVerified?: Prisma.BoolFieldUpdateOperationsInput | boolean
  image?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  phone?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  role?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banned?: Prisma.NullableBoolFieldUpdateOperationsInput | boolean | null
  banReason?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  banExpires?: Prisma.NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  labels?: Prisma.LabelUncheckedUpdateManyWithoutUserNestedInput
  artists?: Prisma.ArtistUncheckedUpdateManyWithoutUserNestedInput
  contributors?: Prisma.ContributorUncheckedUpdateManyWithoutUserNestedInput
  releases?: Prisma.ReleaseUncheckedUpdateManyWithoutUserNestedInput
  sessions?: Prisma.SessionUncheckedUpdateManyWithoutUserNestedInput
  accounts?: Prisma.AccountUncheckedUpdateManyWithoutUserNestedInput
}


/**
 * Count Type UserCountOutputType
 */

export type UserCountOutputType = {
  labels: number
  artists: number
  contributors: number
  releases: number
  tracks: number
  sessions: number
  accounts: number
}

export type UserCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  labels?: boolean | UserCountOutputTypeCountLabelsArgs
  artists?: boolean | UserCountOutputTypeCountArtistsArgs
  contributors?: boolean | UserCountOutputTypeCountContributorsArgs
  releases?: boolean | UserCountOutputTypeCountReleasesArgs
  tracks?: boolean | UserCountOutputTypeCountTracksArgs
  sessions?: boolean | UserCountOutputTypeCountSessionsArgs
  accounts?: boolean | UserCountOutputTypeCountAccountsArgs
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the UserCountOutputType
   */
  select?: Prisma.UserCountOutputTypeSelect<ExtArgs> | null
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountLabelsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.LabelWhereInput
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountArtistsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ArtistWhereInput
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountContributorsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ContributorWhereInput
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountReleasesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ReleaseWhereInput
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountTracksArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.TrackWhereInput
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountSessionsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.SessionWhereInput
}

/**
 * UserCountOutputType without action
 */
export type UserCountOutputTypeCountAccountsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.AccountWhereInput
}


export type UserSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  email?: boolean
  emailVerified?: boolean
  image?: boolean
  phone?: boolean
  role?: boolean
  banned?: boolean
  banReason?: boolean
  banExpires?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  labels?: boolean | Prisma.User$labelsArgs<ExtArgs>
  artists?: boolean | Prisma.User$artistsArgs<ExtArgs>
  contributors?: boolean | Prisma.User$contributorsArgs<ExtArgs>
  releases?: boolean | Prisma.User$releasesArgs<ExtArgs>
  tracks?: boolean | Prisma.User$tracksArgs<ExtArgs>
  sessions?: boolean | Prisma.User$sessionsArgs<ExtArgs>
  accounts?: boolean | Prisma.User$accountsArgs<ExtArgs>
  _count?: boolean | Prisma.UserCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["user"]>

export type UserSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  email?: boolean
  emailVerified?: boolean
  image?: boolean
  phone?: boolean
  role?: boolean
  banned?: boolean
  banReason?: boolean
  banExpires?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}, ExtArgs["result"]["user"]>

export type UserSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  email?: boolean
  emailVerified?: boolean
  image?: boolean
  phone?: boolean
  role?: boolean
  banned?: boolean
  banReason?: boolean
  banExpires?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}, ExtArgs["result"]["user"]>

export type UserSelectScalar = {
  id?: boolean
  name?: boolean
  email?: boolean
  emailVerified?: boolean
  image?: boolean
  phone?: boolean
  role?: boolean
  banned?: boolean
  banReason?: boolean
  banExpires?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type UserOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "name" | "email" | "emailVerified" | "image" | "phone" | "role" | "banned" | "banReason" | "banExpires" | "createdAt" | "updatedAt", ExtArgs["result"]["user"]>
export type UserInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  labels?: boolean | Prisma.User$labelsArgs<ExtArgs>
  artists?: boolean | Prisma.User$artistsArgs<ExtArgs>
  contributors?: boolean | Prisma.User$contributorsArgs<ExtArgs>
  releases?: boolean | Prisma.User$releasesArgs<ExtArgs>
  tracks?: boolean | Prisma.User$tracksArgs<ExtArgs>
  sessions?: boolean | Prisma.User$sessionsArgs<ExtArgs>
  accounts?: boolean | Prisma.User$accountsArgs<ExtArgs>
  _count?: boolean | Prisma.UserCountOutputTypeDefaultArgs<ExtArgs>
}
export type UserIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {}
export type UserIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {}

export type $UserPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "User"
  objects: {
    labels: Prisma.$LabelPayload<ExtArgs>[]
    artists: Prisma.$ArtistPayload<ExtArgs>[]
    contributors: Prisma.$ContributorPayload<ExtArgs>[]
    releases: Prisma.$ReleasePayload<ExtArgs>[]
    tracks: Prisma.$TrackPayload<ExtArgs>[]
    sessions: Prisma.$SessionPayload<ExtArgs>[]
    accounts: Prisma.$AccountPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    name: string
    email: string
    emailVerified: boolean
    image: string | null
    phone: string | null
    role: string | null
    banned: boolean | null
    banReason: string | null
    banExpires: Date | null
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["user"]>
  composites: {}
}

export type UserGetPayload<S extends boolean | null | undefined | UserDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$UserPayload, S>

export type UserCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<UserFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: UserCountAggregateInputType | true
  }

export interface UserDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['User'], meta: { name: 'User' } }
  /**
   * Find zero or one User that matches the filter.
   * @param {UserFindUniqueArgs} args - Arguments to find a User
   * @example
   * // Get one User
   * const user = await prisma.user.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends UserFindUniqueArgs>(args: Prisma.SelectSubset<T, UserFindUniqueArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one User that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {UserFindUniqueOrThrowArgs} args - Arguments to find a User
   * @example
   * // Get one User
   * const user = await prisma.user.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends UserFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, UserFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first User that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserFindFirstArgs} args - Arguments to find a User
   * @example
   * // Get one User
   * const user = await prisma.user.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends UserFindFirstArgs>(args?: Prisma.SelectSubset<T, UserFindFirstArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first User that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserFindFirstOrThrowArgs} args - Arguments to find a User
   * @example
   * // Get one User
   * const user = await prisma.user.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends UserFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, UserFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Users that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Users
   * const users = await prisma.user.findMany()
   * 
   * // Get first 10 Users
   * const users = await prisma.user.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const userWithIdOnly = await prisma.user.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends UserFindManyArgs>(args?: Prisma.SelectSubset<T, UserFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a User.
   * @param {UserCreateArgs} args - Arguments to create a User.
   * @example
   * // Create one User
   * const User = await prisma.user.create({
   *   data: {
   *     // ... data to create a User
   *   }
   * })
   * 
   */
  create<T extends UserCreateArgs>(args: Prisma.SelectSubset<T, UserCreateArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Users.
   * @param {UserCreateManyArgs} args - Arguments to create many Users.
   * @example
   * // Create many Users
   * const user = await prisma.user.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends UserCreateManyArgs>(args?: Prisma.SelectSubset<T, UserCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Users and returns the data saved in the database.
   * @param {UserCreateManyAndReturnArgs} args - Arguments to create many Users.
   * @example
   * // Create many Users
   * const user = await prisma.user.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Users and only return the `id`
   * const userWithIdOnly = await prisma.user.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends UserCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, UserCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a User.
   * @param {UserDeleteArgs} args - Arguments to delete one User.
   * @example
   * // Delete one User
   * const User = await prisma.user.delete({
   *   where: {
   *     // ... filter to delete one User
   *   }
   * })
   * 
   */
  delete<T extends UserDeleteArgs>(args: Prisma.SelectSubset<T, UserDeleteArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one User.
   * @param {UserUpdateArgs} args - Arguments to update one User.
   * @example
   * // Update one User
   * const user = await prisma.user.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends UserUpdateArgs>(args: Prisma.SelectSubset<T, UserUpdateArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Users.
   * @param {UserDeleteManyArgs} args - Arguments to filter Users to delete.
   * @example
   * // Delete a few Users
   * const { count } = await prisma.user.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends UserDeleteManyArgs>(args?: Prisma.SelectSubset<T, UserDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Users.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Users
   * const user = await prisma.user.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends UserUpdateManyArgs>(args: Prisma.SelectSubset<T, UserUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Users and returns the data updated in the database.
   * @param {UserUpdateManyAndReturnArgs} args - Arguments to update many Users.
   * @example
   * // Update many Users
   * const user = await prisma.user.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Users and only return the `id`
   * const userWithIdOnly = await prisma.user.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends UserUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, UserUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one User.
   * @param {UserUpsertArgs} args - Arguments to update or create a User.
   * @example
   * // Update or create a User
   * const user = await prisma.user.upsert({
   *   create: {
   *     // ... data to create a User
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the User we want to update
   *   }
   * })
   */
  upsert<T extends UserUpsertArgs>(args: Prisma.SelectSubset<T, UserUpsertArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Users.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserCountArgs} args - Arguments to filter Users to count.
   * @example
   * // Count the number of Users
   * const count = await prisma.user.count({
   *   where: {
   *     // ... the filter for the Users we want to count
   *   }
   * })
  **/
  count<T extends UserCountArgs>(
    args?: Prisma.Subset<T, UserCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], UserCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a User.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends UserAggregateArgs>(args: Prisma.Subset<T, UserAggregateArgs>): Prisma.PrismaPromise<GetUserAggregateType<T>>

  /**
   * Group by User.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {UserGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends UserGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: UserGroupByArgs['orderBy'] }
      : { orderBy?: UserGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, UserGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUserGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the User model
 */
readonly fields: UserFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for User.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__UserClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  labels<T extends Prisma.User$labelsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.User$labelsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$LabelPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  artists<T extends Prisma.User$artistsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.User$artistsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ArtistPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  contributors<T extends Prisma.User$contributorsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.User$contributorsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ContributorPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  releases<T extends Prisma.User$releasesArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.User$releasesArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ReleasePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  tracks<T extends Prisma.User$tracksArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.User$tracksArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TrackPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  sessions<T extends Prisma.User$sessionsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.User$sessionsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$SessionPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  accounts<T extends Prisma.User$accountsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.User$accountsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$AccountPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the User model
 */
export interface UserFieldRefs {
  readonly id: Prisma.FieldRef<"User", 'String'>
  readonly name: Prisma.FieldRef<"User", 'String'>
  readonly email: Prisma.FieldRef<"User", 'String'>
  readonly emailVerified: Prisma.FieldRef<"User", 'Boolean'>
  readonly image: Prisma.FieldRef<"User", 'String'>
  readonly phone: Prisma.FieldRef<"User", 'String'>
  readonly role: Prisma.FieldRef<"User", 'String'>
  readonly banned: Prisma.FieldRef<"User", 'Boolean'>
  readonly banReason: Prisma.FieldRef<"User", 'String'>
  readonly banExpires: Prisma.FieldRef<"User", 'DateTime'>
  readonly createdAt: Prisma.FieldRef<"User", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"User", 'DateTime'>
}
    

// Custom InputTypes
/**
 * User findUnique
 */
export type UserFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * Filter, which User to fetch.
   */
  where: Prisma.UserWhereUniqueInput
}

/**
 * User findUniqueOrThrow
 */
export type UserFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * Filter, which User to fetch.
   */
  where: Prisma.UserWhereUniqueInput
}

/**
 * User findFirst
 */
export type UserFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * Filter, which User to fetch.
   */
  where?: Prisma.UserWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Users to fetch.
   */
  orderBy?: Prisma.UserOrderByWithRelationInput | Prisma.UserOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Users.
   */
  cursor?: Prisma.UserWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Users from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Users.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Users.
   */
  distinct?: Prisma.UserScalarFieldEnum | Prisma.UserScalarFieldEnum[]
}

/**
 * User findFirstOrThrow
 */
export type UserFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * Filter, which User to fetch.
   */
  where?: Prisma.UserWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Users to fetch.
   */
  orderBy?: Prisma.UserOrderByWithRelationInput | Prisma.UserOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Users.
   */
  cursor?: Prisma.UserWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Users from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Users.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Users.
   */
  distinct?: Prisma.UserScalarFieldEnum | Prisma.UserScalarFieldEnum[]
}

/**
 * User findMany
 */
export type UserFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * Filter, which Users to fetch.
   */
  where?: Prisma.UserWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Users to fetch.
   */
  orderBy?: Prisma.UserOrderByWithRelationInput | Prisma.UserOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Users.
   */
  cursor?: Prisma.UserWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Users from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Users.
   */
  skip?: number
  distinct?: Prisma.UserScalarFieldEnum | Prisma.UserScalarFieldEnum[]
}

/**
 * User create
 */
export type UserCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * The data needed to create a User.
   */
  data: Prisma.XOR<Prisma.UserCreateInput, Prisma.UserUncheckedCreateInput>
}

/**
 * User createMany
 */
export type UserCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Users.
   */
  data: Prisma.UserCreateManyInput | Prisma.UserCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * User createManyAndReturn
 */
export type UserCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * The data used to create many Users.
   */
  data: Prisma.UserCreateManyInput | Prisma.UserCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * User update
 */
export type UserUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * The data needed to update a User.
   */
  data: Prisma.XOR<Prisma.UserUpdateInput, Prisma.UserUncheckedUpdateInput>
  /**
   * Choose, which User to update.
   */
  where: Prisma.UserWhereUniqueInput
}

/**
 * User updateMany
 */
export type UserUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Users.
   */
  data: Prisma.XOR<Prisma.UserUpdateManyMutationInput, Prisma.UserUncheckedUpdateManyInput>
  /**
   * Filter which Users to update
   */
  where?: Prisma.UserWhereInput
  /**
   * Limit how many Users to update.
   */
  limit?: number
}

/**
 * User updateManyAndReturn
 */
export type UserUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * The data used to update Users.
   */
  data: Prisma.XOR<Prisma.UserUpdateManyMutationInput, Prisma.UserUncheckedUpdateManyInput>
  /**
   * Filter which Users to update
   */
  where?: Prisma.UserWhereInput
  /**
   * Limit how many Users to update.
   */
  limit?: number
}

/**
 * User upsert
 */
export type UserUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * The filter to search for the User to update in case it exists.
   */
  where: Prisma.UserWhereUniqueInput
  /**
   * In case the User found by the `where` argument doesn't exist, create a new User with this data.
   */
  create: Prisma.XOR<Prisma.UserCreateInput, Prisma.UserUncheckedCreateInput>
  /**
   * In case the User was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.UserUpdateInput, Prisma.UserUncheckedUpdateInput>
}

/**
 * User delete
 */
export type UserDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
  /**
   * Filter which User to delete.
   */
  where: Prisma.UserWhereUniqueInput
}

/**
 * User deleteMany
 */
export type UserDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Users to delete
   */
  where?: Prisma.UserWhereInput
  /**
   * Limit how many Users to delete.
   */
  limit?: number
}

/**
 * User.labels
 */
export type User$labelsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Label
   */
  select?: Prisma.LabelSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Label
   */
  omit?: Prisma.LabelOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LabelInclude<ExtArgs> | null
  where?: Prisma.LabelWhereInput
  orderBy?: Prisma.LabelOrderByWithRelationInput | Prisma.LabelOrderByWithRelationInput[]
  cursor?: Prisma.LabelWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.LabelScalarFieldEnum | Prisma.LabelScalarFieldEnum[]
}

/**
 * User.artists
 */
export type User$artistsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Artist
   */
  select?: Prisma.ArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Artist
   */
  omit?: Prisma.ArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ArtistInclude<ExtArgs> | null
  where?: Prisma.ArtistWhereInput
  orderBy?: Prisma.ArtistOrderByWithRelationInput | Prisma.ArtistOrderByWithRelationInput[]
  cursor?: Prisma.ArtistWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.ArtistScalarFieldEnum | Prisma.ArtistScalarFieldEnum[]
}

/**
 * User.contributors
 */
export type User$contributorsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Contributor
   */
  select?: Prisma.ContributorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Contributor
   */
  omit?: Prisma.ContributorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ContributorInclude<ExtArgs> | null
  where?: Prisma.ContributorWhereInput
  orderBy?: Prisma.ContributorOrderByWithRelationInput | Prisma.ContributorOrderByWithRelationInput[]
  cursor?: Prisma.ContributorWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.ContributorScalarFieldEnum | Prisma.ContributorScalarFieldEnum[]
}

/**
 * User.releases
 */
export type User$releasesArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Release
   */
  select?: Prisma.ReleaseSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Release
   */
  omit?: Prisma.ReleaseOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseInclude<ExtArgs> | null
  where?: Prisma.ReleaseWhereInput
  orderBy?: Prisma.ReleaseOrderByWithRelationInput | Prisma.ReleaseOrderByWithRelationInput[]
  cursor?: Prisma.ReleaseWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.ReleaseScalarFieldEnum | Prisma.ReleaseScalarFieldEnum[]
}

/**
 * User.tracks
 */
export type User$tracksArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Track
   */
  select?: Prisma.TrackSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Track
   */
  omit?: Prisma.TrackOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackInclude<ExtArgs> | null
  where?: Prisma.TrackWhereInput
  orderBy?: Prisma.TrackOrderByWithRelationInput | Prisma.TrackOrderByWithRelationInput[]
  cursor?: Prisma.TrackWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.TrackScalarFieldEnum | Prisma.TrackScalarFieldEnum[]
}

/**
 * User.sessions
 */
export type User$sessionsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Session
   */
  select?: Prisma.SessionSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Session
   */
  omit?: Prisma.SessionOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.SessionInclude<ExtArgs> | null
  where?: Prisma.SessionWhereInput
  orderBy?: Prisma.SessionOrderByWithRelationInput | Prisma.SessionOrderByWithRelationInput[]
  cursor?: Prisma.SessionWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.SessionScalarFieldEnum | Prisma.SessionScalarFieldEnum[]
}

/**
 * User.accounts
 */
export type User$accountsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Account
   */
  select?: Prisma.AccountSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Account
   */
  omit?: Prisma.AccountOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.AccountInclude<ExtArgs> | null
  where?: Prisma.AccountWhereInput
  orderBy?: Prisma.AccountOrderByWithRelationInput | Prisma.AccountOrderByWithRelationInput[]
  cursor?: Prisma.AccountWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.AccountScalarFieldEnum | Prisma.AccountScalarFieldEnum[]
}

/**
 * User without action
 */
export type UserDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the User
   */
  select?: Prisma.UserSelect<ExtArgs> | null
  /**
   * Omit specific fields from the User
   */
  omit?: Prisma.UserOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.UserInclude<ExtArgs> | null
}
