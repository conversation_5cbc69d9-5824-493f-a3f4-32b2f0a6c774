import { useState } from "react";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";
import { useMutation } from "@tanstack/react-query";

import { useTRPCClient } from "@/utils/trpc";
import { authClient } from "@/lib/auth-client";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface CreateLabelDialogProps {
  children: React.ReactNode;
  onLabelCreated: () => void;
}

export function CreateLabelDialog({
  children,
  onLabelCreated,
}: CreateLabelDialogProps) {
  const [open, setOpen] = useState(false);
  const trpcClient = useTRPCClient();
  const { data: session } = authClient.useSession();
  const isAdmin = session?.user?.role === "admin";
  const [formData, setFormData] = useState({
    name: "",
  });

  const createLabelMutation = useMutation({
    mutationFn: async (data: typeof formData) => {
      return trpcClient.label.create.mutate({
        name: data.name,
      });
    },
    onSuccess: () => {
      toast.success("Label created successfully");
      setOpen(false);
      setFormData({
        name: "",
      });
      onLabelCreated();
    },
    onError: (error: any) => {
      console.error("Failed to create label:", error);
      toast.error(
        "Failed to create label: " + (error.message || "Unknown error")
      );
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast.error("Label name is required");
      return;
    }

    try {
      await createLabelMutation.mutateAsync({
        name: formData.name.trim(),
      });
    } catch (error) {
      // Error handling is done in the mutation
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Create New Label</DialogTitle>
          <DialogDescription>
            Add a new label to organize your artists and releases.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Basic Information */}
          <div className="space-y-4">
            {isAdmin && (
              <div className="space-y-2">
                <Label htmlFor="createdBy">Created By</Label>
                <Input
                  id="createdBy"
                  value={`${session?.user?.email} | ${session?.user?.name}`}
                  disabled
                />
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="name" className="block">
                Label Name <span className="text-red-500">*</span>
              </Label>
              <p
                className="text-muted-foreground text-xs"
                role="region"
                aria-live="polite"
              >
                This is the name that will be displayed for the label. Please
                make sure to use the correct name.
              </p>
              <Input
                id="name"
                placeholder="Enter label name"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                required
                disabled={createLabelMutation.isPending}
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={createLabelMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={createLabelMutation.isPending || !formData.name.trim()}
            >
              {createLabelMutation.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                "Create Label"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
