import { protectedProcedure, router } from "@/lib/trpc";
import { z } from "zod";
import prisma from "../../prisma/index";
import { TRPCError } from "@trpc/server";

// Validation schemas
const ServiceEnum = z.enum([
  "YOUTUBE",
  "SPOTIFY",
  "APPLE_MUSIC",
  "SOUNDCLOUD",
  "AUDIOMACK",
]);

const ArtistIdentifierSchema = z.object({
  service: ServiceEnum,
  identifier: z.string().min(1, "Identifier is required"),
});

const CreateArtistSchema = z.object({
  name: z.string().min(1, "Artist name is required").max(100, "Name too long"),
  instagram: z.string().optional(),
  biography: z.string().max(1000, "Biography too long").optional(),
  country: z.string().optional(),
  genre: z.string().max(50, "Genre too long").optional(),
  labelId: z.string().uuid().optional(),
  identifiers: z.array(ArtistIdentifierSchema).optional(),
});

const UpdateArtistSchema = z.object({
  id: z.string().uuid(),
  name: z
    .string()
    .min(1, "Artist name is required")
    .max(100, "Name too long")
    .optional(),
  instagram: z.string().optional(),
  biography: z.string().max(1000, "Biography too long").optional(),
  country: z.string().optional(),
  genre: z.string().max(50, "Genre too long").optional(),
  labelId: z.string().uuid().optional(),
  identifiers: z.array(ArtistIdentifierSchema).optional(),
});

const GetArtistSchema = z.object({
  id: z.string().uuid(),
});

const GetArtistsSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  search: z.string().optional(),
  genre: z.string().optional(),
});

const DeleteArtistSchema = z.object({
  id: z.string().uuid(),
});

export const artistRouter = router({
  // Create a new artist
  create: protectedProcedure
    .input(CreateArtistSchema)
    .mutation(async ({ ctx, input }) => {
      const { session } = ctx;
      const { identifiers, ...artistData } = input;
      const isAdmin = session.user.role === "admin";

      try {
        // Check if artist name already exists for this user
        const existingArtist = await prisma.artist.findFirst({
          where: {
            userId: session.user.id,
            name: input.name,
          },
        });

        if (existingArtist) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "Artist with this name already exists",
          });
        }

        // If labelId is provided, verify the label belongs to the user
        if (input.labelId) {
          const label = await prisma.label.findFirst({
            where: {
              id: input.labelId,
              ...(isAdmin ? {} : { userId: session.user.id }),
            },
          });

          if (!label) {
            throw new TRPCError({
              code: "NOT_FOUND",
              message: "Label not found or not owned by user",
            });
          }
        }

        // Create artist with identifiers in a transaction
        const artist = await prisma.$transaction(async (tx) => {
          const newArtist = await tx.artist.create({
            data: {
              ...artistData,
              userId: session.user.id,
            },
            include: {
              user: true,
              label: true,
              identifiers: true,
            },
          });

          // Create identifiers if provided
          if (identifiers && identifiers.length > 0) {
            await tx.artistIdentifier.createMany({
              data: identifiers.map((identifier) => ({
                artistId: newArtist.id,
                service: identifier.service,
                identifier: identifier.identifier,
              })),
            });

            // Return artist with identifiers
            return await tx.artist.findUnique({
              where: { id: newArtist.id },
              include: {
                user: true,
                label: true,
                identifiers: true,
              },
            });
          }

          return newArtist;
        });

        return artist;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create artist",
          cause: error,
        });
      }
    }),

  // Get a single artist by ID
  getById: protectedProcedure
    .input(GetArtistSchema)
    .query(async ({ ctx, input }) => {
      const { session } = ctx;
      const isAdmin = session.user.role === "admin";

      try {
        const artist = await prisma.artist.findUnique({
          where: {
            id: input.id,
          },
          include: {
            user: true,
            label: true,
            identifiers: true,
          },
        });

        if (!artist) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Artist not found",
          });
        }

        // Check if user owns this artist
        if (artist.userId !== session.user.id && !isAdmin) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "Not authorized to view this artist",
          });
        }

        return artist;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to get artist",
          cause: error,
        });
      }
    }),

  // Get all artists for the current user with pagination and filtering
  getAll: protectedProcedure
    .input(GetArtistsSchema)
    .query(async ({ ctx, input }) => {
      const { session } = ctx;
      const { page, limit, search, genre } = input;
      const skip = (page - 1) * limit;
      const isAdmin = session.user.role === "admin";

      try {
        const where = {
          ...(isAdmin ? {} : { userId: session.user.id }),
          ...(search && {
            name: {
              contains: search,
              mode: "insensitive" as const,
            },
          }),
          ...(genre && { genre }),
        };

        const [artists, total] = await Promise.all([
          prisma.artist.findMany({
            where,
            include: {
              user: true,
              label: true,
              identifiers: true,
            },
            orderBy: {
              createdAt: "desc",
            },
            skip,
            take: limit,
          }),
          prisma.artist.count({ where }),
        ]);

        return {
          artists,
          pagination: {
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
          },
        };
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to get artists",
          cause: error,
        });
      }
    }),

  // Update an artist
  update: protectedProcedure
    .input(UpdateArtistSchema)
    .mutation(async ({ ctx, input }) => {
      const { session } = ctx;
      const { id, identifiers, ...updateData } = input;
      const isAdmin = session.user.role === "admin";

      try {
        // Check if artist exists and belongs to user
        const existingArtist = await prisma.artist.findUnique({
          where: { id },
          include: { identifiers: true },
        });

        if (!existingArtist) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Artist not found",
          });
        }

        if (existingArtist.userId !== session.user.id && !isAdmin) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "Not authorized to update this artist",
          });
        }

        // Check for name conflicts if name is being updated
        if (updateData.name && updateData.name !== existingArtist.name) {
          const nameConflict = await prisma.artist.findFirst({
            where: {
              ...(isAdmin ? {} : { userId: session.user.id }),
              name: updateData.name,
              id: { not: id },
            },
          });

          if (nameConflict) {
            throw new TRPCError({
              code: "CONFLICT",
              message: "Artist with this name already exists",
            });
          }
        }

        // If labelId is provided, verify the label belongs to the user
        if (updateData.labelId) {
          const label = await prisma.label.findFirst({
            where: {
              id: updateData.labelId,
              ...(isAdmin ? {} : { userId: session.user.id }),
            },
          });

          if (!label) {
            throw new TRPCError({
              code: "NOT_FOUND",
              message: "Label not found or not owned by user",
            });
          }
        }

        // Update artist with identifiers in a transaction
        const updatedArtist = await prisma.$transaction(async (tx) => {
          // Update the artist
          const artist = await tx.artist.update({
            where: { id },
            data: updateData,
            include: {
              user: true,
              label: true,
              identifiers: true,
            },
          });

          // Update identifiers if provided
          if (identifiers !== undefined) {
            // Delete existing identifiers
            await tx.artistIdentifier.deleteMany({
              where: { artistId: id },
            });

            // Create new identifiers
            if (identifiers.length > 0) {
              await tx.artistIdentifier.createMany({
                data: identifiers.map((identifier) => ({
                  artistId: id,
                  service: identifier.service,
                  identifier: identifier.identifier,
                })),
              });
            }

            // Return updated artist with new identifiers
            return await tx.artist.findUnique({
              where: { id },
              include: {
                user: true,
                label: true,
                identifiers: true,
              },
            });
          }

          return artist;
        });

        return updatedArtist;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update artist",
          cause: error,
        });
      }
    }),

  // Delete an artist
  delete: protectedProcedure
    .input(DeleteArtistSchema)
    .mutation(async ({ ctx, input }) => {
      const { session } = ctx;
      const isAdmin = session.user.role === "admin";

      try {
        // Check if artist exists and belongs to user
        const existingArtist = await prisma.artist.findUnique({
          where: { id: input.id },
        });

        if (!existingArtist) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Artist not found",
          });
        }

        if (existingArtist.userId !== session.user.id && !isAdmin) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "Not authorized to delete this artist",
          });
        }

        // Delete artist (cascade will handle identifiers)
        await prisma.artist.delete({
          where: { id: input.id },
        });

        return { success: true, message: "Artist deleted successfully" };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete artist",
          cause: error,
        });
      }
    }),
});
