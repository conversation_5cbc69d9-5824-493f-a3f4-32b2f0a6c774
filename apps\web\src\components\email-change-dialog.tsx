import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import { Mail, Send } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useTRPCClient } from "@/utils/trpc";
import { useState } from "react";
import { useForm } from "@tanstack/react-form";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface EmailChangeDialogProps {
  currentEmail: string;
  trigger?: React.ReactNode;
}

export default function EmailChangeDialog({
  currentEmail,
  trigger,
}: EmailChangeDialogProps) {
  const [open, setOpen] = useState(false);
  const trpcClient = useTRPCClient();

  const requestEmailChangeMutation = useMutation({
    mutationFn: (newEmail: string) =>
      trpcClient.email.requestEmailChange.mutate({ newEmail }),
    onSuccess: () => {
      toast.success(
        "Email change verification sent! Please check your current email inbox."
      );
      setOpen(false);
      form.reset();
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to send email change verification");
    },
  });

  const form = useForm({
    defaultValues: {
      newEmail: "",
      confirmEmail: "",
    },
    onSubmit: async ({ value }) => {
      requestEmailChangeMutation.mutate(value.newEmail);
    },
    validators: {
      onSubmit: z
        .object({
          newEmail: z
            .string()
            .email("Please enter a valid email address")
            .refine(
              (email) => email !== currentEmail,
              "New email must be different from current email"
            ),
          confirmEmail: z.string().email("Please enter a valid email address"),
        })
        .refine((data) => data.newEmail === data.confirmEmail, {
          message: "Email addresses don't match",
          path: ["confirmEmail"],
        }),
    },
  });

  const defaultTrigger = (
    <Button
      type="button"
      variant="default"
      className="w-full sm:min-w-[180px] h-10"
    >
      <Send className="h-4 w-4" />
      Send Email Change Link
    </Button>
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{trigger || defaultTrigger}</DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader className="gap-4">
          <DialogTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Change Email Address
          </DialogTitle>
          <DialogDescription>
            Enter your new email address. A verification link will be sent to
            your <strong>current email</strong> ({currentEmail}) to approve this
            change.
          </DialogDescription>
        </DialogHeader>

        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            void form.handleSubmit();
          }}
          className="space-y-6"
        >
          <div>
            <form.Field name="newEmail">
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>New Email Address</Label>
                  <Input
                    id={field.name}
                    name={field.name}
                    type="email"
                    placeholder="Enter your new email address"
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                  />
                  {field.state.meta.errors.map((error) => (
                    <p
                      key={error?.message}
                      className="text-destructive text-sm"
                    >
                      {error?.message}
                    </p>
                  ))}
                </div>
              )}
            </form.Field>
          </div>

          <div>
            <form.Field name="confirmEmail">
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>Confirm New Email Address</Label>
                  <Input
                    id={field.name}
                    name={field.name}
                    type="email"
                    placeholder="Confirm your new email address"
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                  />
                  {field.state.meta.errors.map((error) => (
                    <p
                      key={error?.message}
                      className="text-destructive text-sm"
                    >
                      {error?.message}
                    </p>
                  ))}
                </div>
              )}
            </form.Field>
          </div>

          <div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 space-y-2 space-y-reverse sm:space-y-0">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={requestEmailChangeMutation.isPending}
            >
              Cancel
            </Button>
            <form.Subscribe>
              {(state) => (
                <Button
                  type="submit"
                  disabled={
                    !state.canSubmit ||
                    state.isSubmitting ||
                    requestEmailChangeMutation.isPending
                  }
                >
                  <Send className="h-4 w-4" />
                  {requestEmailChangeMutation.isPending
                    ? "Sending..."
                    : "Send Verification"}
                </Button>
              )}
            </form.Subscribe>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
