# Sidebar Custom Renderers System

This system provides a reusable way to create custom behaviors for sidebar menu items, such as opening dialogs, executing actions, or custom navigation.

## Features

- **Dialog Type**: Open dialogs with automatic navigation on success
- **Action Type**: Execute custom functions when clicked
- **Navigation Type**: Custom navigation behavior
- **Automatic Success Handling**: Automatically maps success callbacks to component-specific prop names

## Usage

### 1. Define Your Custom Renderers

```tsx
import {
  useSidebarRenderers,
  type SidebarCustomRenderers,
} from "@/lib/sidebar-renderers";
import { CreateArtistDialog } from "@/routes/dashboard/artist/components/create-artist-dialog";
import { CreateReleaseDialog } from "@/components/create-release-dialog";

const rendererConfigs: SidebarCustomRenderers = {
  "New Artist": {
    type: "dialog",
    component: CreateArtistDialog,
    navigationPath: "/dashboard/artist",
    successPropName: "onArtistCreated", // The prop name your dialog expects
  },
  "New Release": {
    type: "dialog",
    component: CreateReleaseDialog,
    navigationPath: "/dashboard/releases",
    successPropName: "onReleaseCreated",
  },
  "Custom Action": {
    type: "action",
    action: () => console.log("Custom action executed"),
  },
  "Custom Nav": {
    type: "navigation",
    navigationPath: "/custom-path",
  },
};
```

### 2. Generate Renderers Using the Hook

```tsx
const customRenderers = useSidebarRenderers(rendererConfigs);
```

### 3. Pass to NavMain Component

```tsx
<NavMain items={filteredNavMain} customRenderers={customRenderers} />
```

## Renderer Types

### Dialog Type

- **Purpose**: Opens a dialog component when clicked
- **Required**: `component`, `successPropName`
- **Optional**: `navigationPath` (auto-navigation on success), `props` (additional props)

```tsx
{
  type: "dialog",
  component: YourDialogComponent,
  navigationPath: "/redirect-after-success",
  successPropName: "onYourActionCompleted",
  props: { additionalProp: "value" }
}
```

### Action Type

- **Purpose**: Executes a custom function when clicked
- **Required**: `action`

```tsx
{
  type: "action",
  action: () => {
    // Your custom logic here
    console.log("Action executed");
  }
}
```

### Navigation Type

- **Purpose**: Custom navigation behavior
- **Required**: `navigationPath`

```tsx
{
  type: "navigation",
  navigationPath: "/your-custom-path"
}
```

## Dialog Component Requirements

Your dialog components should:

1. Accept a `children` prop for the trigger element
2. Have a success callback prop (e.g., `onArtistCreated`, `onReleaseCreated`)
3. Follow this pattern:

```tsx
interface YourDialogProps {
  children: React.ReactNode;
  onYourActionCompleted: () => void; // This prop name goes in successPropName
}

export function YourDialog({
  children,
  onYourActionCompleted,
}: YourDialogProps) {
  // Your dialog implementation

  const handleSuccess = () => {
    // Your success logic
    onYourActionCompleted(); // Call this on success
  };

  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      {/* Your dialog content */}
    </Dialog>
  );
}
```

## Benefits

- **Reusable**: Add new custom menu items easily
- **Type Safe**: Full TypeScript support
- **Automatic Navigation**: Handle post-success navigation automatically
- **Flexible**: Support for different interaction patterns
- **Maintainable**: Centralized configuration
