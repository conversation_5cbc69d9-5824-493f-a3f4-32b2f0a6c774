# Email System Setup with Loops.so

This guide will help you set up email verification and password reset for your Soundmera-tan application using Loops.so.

## Prerequisites

1. A Loops.so account
2. An email verification template created in Loops.so

## Setup Steps

### 1. Create Email Templates in Loops.so

#### Email Verification Template

1. Log in to your Loops.so dashboard
2. Go to "Transactional" → "Create transactional email"
3. Create an email verification template with the following data variables:
   - `url` (required) - The URL users will click to verify their email
   - `name` (optional) - The user's name for personalization

Example email verification template content:

```html
Hi {{name}}, Please verify your email address by clicking the link below:

<a href="{{url}}">Verify Email Address</a>

This link will expire in 24 hours. Thanks, The Soundmera-tan Team
```

4. Save the template and copy the Template ID

#### Password Reset Template

1. Create another transactional email template for password reset
2. Use the same data variables:
   - `url` (required) - The URL users will click to reset their password
   - `name` (optional) - The user's name for personalization

Example password reset template content:

```html
Hi {{name}}, We received a request to reset your password. Click the link below
to set a new password:

<a href="{{url}}">Reset Your Password</a>

This link will expire in 1 hour. If you didn't request this, you can safely
ignore this email. Thanks, The Soundmera-tan Team
```

3. Save the template and copy the Template ID

### 2. Configure Environment Variables

Add the following environment variables to your `apps/server/.env` file:

```env
# Loops.so Configuration
LOOPS_API_KEY="your_loops_api_key_here"
LOOPS_EMAIL_VERIFICATION_TEMPLATE_ID="your_template_id_here"

# Optional: For other email types
LOOPS_PASSWORD_RESET_TEMPLATE_ID="your_password_reset_template_id"
LOOPS_NOTIFICATION_TEMPLATE_ID="your_notification_template_id"
```

### 3. Get Your Loops.so API Key

1. In your Loops.so dashboard, go to "Settings" → "API Keys"
2. Copy your API key
3. Add it to your `.env` file as `LOOPS_API_KEY`

## Features Implemented

### 1. Reusable Email Service (`apps/server/src/lib/email.ts`)

- `EmailService.sendVerificationEmail()` - Send email verification emails
- `EmailService.sendPasswordResetEmail()` - Send password reset emails (template needed)
- `EmailService.sendNotificationEmail()` - Send notification emails (template needed)
- `EmailService.sendTransactionalEmail()` - Generic method for any transactional email
- `EmailService.validateConfiguration()` - Check if service is properly configured

### 2. Better Auth Integration (`apps/server/src/lib/auth.ts`)

- Automatic email verification on signup
- Custom email sending via Loops.so
- Email verification required for account access

### 3. Manual Email Verification (`apps/server/src/routers/index.ts`)

- tRPC endpoint: `sendVerificationEmail` - Manually trigger verification email
- Protected route (requires authentication)
- Prevents sending to already verified emails

### 4. Frontend Integration

#### Email Verification

- **Profile Component**: "Verify Email" button in the alert banner
- **Verification Page**: `/verify-email` route to handle email verification tokens
- **User Feedback**: Toast notifications for success/error states

#### Password Reset

- **Login Page**: "Forgot your password?" link with expandable form
- **Reset Page**: `/reset-password` route to handle password reset tokens
- **User Experience**: Step-by-step flow with clear feedback

## Usage

### Email Verification Flow

#### Manual Verification

Users can manually trigger email verification by:

1. Going to their profile page (`/dashboard/profile`)
2. Clicking the "Verify Email" button in the alert banner (only shown if email is not verified)

#### Verification Process

1. User clicks "Verify Email" button
2. System sends verification email via Loops.so
3. User receives email with verification link
4. User clicks verification link
5. System verifies the token and marks email as verified
6. User is redirected to dashboard

### Password Reset Flow

#### Requesting Password Reset

1. User goes to login page (`/auth`)
2. Clicks "Forgot your password?" link
3. Enters their email address
4. Clicks "Send Reset Email"
5. System sends password reset email via Loops.so

#### Resetting Password

1. User receives email with reset link
2. User clicks reset link → goes to `/reset-password?token=...`
3. User enters new password and confirms it
4. User clicks "Reset Password"
5. System validates token and updates password
6. User is redirected to login page

## Testing

To test the email verification:

1. Create a new user account
2. Check that the email verification alert appears in the profile
3. Click "Verify Email" button
4. Check your email for the verification message
5. Click the verification link
6. Verify that the email is marked as verified

## Troubleshooting

### Common Issues

1. **"LOOPS_API_KEY is not configured"**

   - Make sure you've added your Loops.so API key to the `.env` file

2. **"LOOPS_EMAIL_VERIFICATION_TEMPLATE_ID is not configured"**

   - Make sure you've created an email template in Loops.so and added the template ID to your `.env` file

3. **Email not sending**

   - Check your Loops.so dashboard for any API errors
   - Verify your API key is correct
   - Check the server logs for detailed error messages

4. **Verification link not working**
   - Make sure the `CORS_ORIGIN` environment variable is set correctly
   - Check that the verification URL format matches your frontend routing

### Environment Variables Checklist

- [ ] `LOOPS_API_KEY` - Your Loops.so API key
- [ ] `LOOPS_EMAIL_VERIFICATION_TEMPLATE_ID` - Your email verification template ID
- [ ] `CORS_ORIGIN` - Your frontend URL (e.g., "http://localhost:3001")

## Next Steps

1. Create additional email templates for password reset and notifications
2. Add the corresponding template IDs to your environment variables
3. Test the complete email verification flow
4. Customize the email templates to match your brand

## Support

If you encounter any issues:

1. Check the server logs for detailed error messages
2. Verify your Loops.so dashboard for API usage and errors
3. Ensure all environment variables are properly configured
