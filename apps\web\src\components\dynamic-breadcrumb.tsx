import { useRouterState } from "@tanstack/react-router";
import { useQuery } from "@tanstack/react-query";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { useTRPC } from "@/utils/trpc";
import React from "react";

// Route title mapping
const routeTitleMap: Record<string, string> = {
  "/dashboard": "Dashboard",
  "/dashboard/": "Dashboard",
  "/dashboard/profile": "Profile Settings",
  "/dashboard/profile/": "Profile Settings",
  "/dashboard/user-management": "User Management",
  "/dashboard/user-management/": "User Management",
  "/dashboard/artist": "Artist Management",
  "/dashboard/artist/": "Artist Management",
  "/dashboard/contributor": "Contributor Management",
  "/dashboard/contributor/": "Contributor Management",
  "/dashboard/label": "Label Management",
  "/dashboard/label/": "Label Management",
  "/dashboard/release": "Release Management",
  "/dashboard/release/": "Release Management",
  "/dashboard/release/create": "Create Release",
  "/dashboard/release/create/": "Create Release",
};

// Generate breadcrumb title from route path
function getRouteTitle(pathname: string): string {
  // Check exact match first
  if (routeTitleMap[pathname]) {
    return routeTitleMap[pathname];
  }

  // Fallback: convert pathname to title
  const segments = pathname.split("/").filter(Boolean);
  const lastSegment = segments[segments.length - 1];

  if (lastSegment) {
    // Convert kebab-case to Title Case
    return lastSegment
      .split("-")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  }

  return "Dashboard";
}

interface BreadcrumbItem {
  title: string;
  path: string;
  isLast: boolean;
}

export function DynamicBreadcrumb() {
  const matches = useRouterState({ select: (s) => s.matches });
  const trpc = useTRPC();

  // Check if we're on an artist details page and get the artist ID
  const currentPath = matches[matches.length - 1]?.pathname || "";
  const artistIdMatch = currentPath.match(/^\/dashboard\/artist\/([^\/]+)$/);
  const artistId = artistIdMatch ? artistIdMatch[1] : null;

  // Fetch artist data if we're on an artist details page
  const { data: artist } = useQuery(
    trpc.artist.getById.queryOptions({ id: artistId! }, { enabled: !!artistId })
  );

  // Special handling for artist details page
  if (artistId && artist) {
    return (
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard/artist">
              Artist Management
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{artist.name}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    );
  }

  // Generate breadcrumb items from matches for other pages
  const breadcrumbItems: BreadcrumbItem[] = matches
    .filter((match) => {
      // Only include dashboard routes
      return match.pathname.startsWith("/dashboard");
    })
    .map((match, index, filteredMatches) => {
      const isLast = index === filteredMatches.length - 1;
      const title = getRouteTitle(match.pathname);

      return {
        title,
        path: match.pathname,
        isLast,
      };
    })
    .filter((item, index, items) => {
      // Remove duplicates (e.g., /dashboard and /dashboard/)
      return index === 0 || item.title !== items[index - 1].title;
    });

  // If no breadcrumbs or only one item, show a default breadcrumb
  if (breadcrumbItems.length === 0) {
    return (
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbPage>Dashboard</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    );
  }

  return (
    <Breadcrumb>
      <BreadcrumbList>
        {breadcrumbItems.map((item, index) => (
          <React.Fragment key={item.path}>
            <BreadcrumbItem>
              {item.isLast ? (
                <BreadcrumbPage>{item.title}</BreadcrumbPage>
              ) : (
                <BreadcrumbLink href={item.path}>{item.title}</BreadcrumbLink>
              )}
            </BreadcrumbItem>
            {!item.isLast && <BreadcrumbSeparator />}
          </React.Fragment>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  );
}
