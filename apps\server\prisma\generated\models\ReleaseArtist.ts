
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `ReleaseArtist` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model ReleaseArtist
 * 
 */
export type ReleaseArtistModel = runtime.Types.Result.DefaultSelection<Prisma.$ReleaseArtistPayload>

export type AggregateReleaseArtist = {
  _count: ReleaseArtistCountAggregateOutputType | null
  _min: ReleaseArtistMinAggregateOutputType | null
  _max: ReleaseArtistMaxAggregateOutputType | null
}

export type ReleaseArtistMinAggregateOutputType = {
  releaseId: string | null
  artistId: string | null
  role: $Enums.ArtistRole | null
}

export type ReleaseArtistMaxAggregateOutputType = {
  releaseId: string | null
  artistId: string | null
  role: $Enums.ArtistRole | null
}

export type ReleaseArtistCountAggregateOutputType = {
  releaseId: number
  artistId: number
  role: number
  _all: number
}


export type ReleaseArtistMinAggregateInputType = {
  releaseId?: true
  artistId?: true
  role?: true
}

export type ReleaseArtistMaxAggregateInputType = {
  releaseId?: true
  artistId?: true
  role?: true
}

export type ReleaseArtistCountAggregateInputType = {
  releaseId?: true
  artistId?: true
  role?: true
  _all?: true
}

export type ReleaseArtistAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which ReleaseArtist to aggregate.
   */
  where?: Prisma.ReleaseArtistWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ReleaseArtists to fetch.
   */
  orderBy?: Prisma.ReleaseArtistOrderByWithRelationInput | Prisma.ReleaseArtistOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.ReleaseArtistWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ReleaseArtists from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ReleaseArtists.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned ReleaseArtists
  **/
  _count?: true | ReleaseArtistCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: ReleaseArtistMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: ReleaseArtistMaxAggregateInputType
}

export type GetReleaseArtistAggregateType<T extends ReleaseArtistAggregateArgs> = {
      [P in keyof T & keyof AggregateReleaseArtist]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateReleaseArtist[P]>
    : Prisma.GetScalarType<T[P], AggregateReleaseArtist[P]>
}




export type ReleaseArtistGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ReleaseArtistWhereInput
  orderBy?: Prisma.ReleaseArtistOrderByWithAggregationInput | Prisma.ReleaseArtistOrderByWithAggregationInput[]
  by: Prisma.ReleaseArtistScalarFieldEnum[] | Prisma.ReleaseArtistScalarFieldEnum
  having?: Prisma.ReleaseArtistScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: ReleaseArtistCountAggregateInputType | true
  _min?: ReleaseArtistMinAggregateInputType
  _max?: ReleaseArtistMaxAggregateInputType
}

export type ReleaseArtistGroupByOutputType = {
  releaseId: string
  artistId: string
  role: $Enums.ArtistRole
  _count: ReleaseArtistCountAggregateOutputType | null
  _min: ReleaseArtistMinAggregateOutputType | null
  _max: ReleaseArtistMaxAggregateOutputType | null
}

type GetReleaseArtistGroupByPayload<T extends ReleaseArtistGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<ReleaseArtistGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof ReleaseArtistGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], ReleaseArtistGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], ReleaseArtistGroupByOutputType[P]>
      }
    >
  > 



export type ReleaseArtistWhereInput = {
  AND?: Prisma.ReleaseArtistWhereInput | Prisma.ReleaseArtistWhereInput[]
  OR?: Prisma.ReleaseArtistWhereInput[]
  NOT?: Prisma.ReleaseArtistWhereInput | Prisma.ReleaseArtistWhereInput[]
  releaseId?: Prisma.StringFilter<"ReleaseArtist"> | string
  artistId?: Prisma.StringFilter<"ReleaseArtist"> | string
  role?: Prisma.EnumArtistRoleFilter<"ReleaseArtist"> | $Enums.ArtistRole
  release?: Prisma.XOR<Prisma.ReleaseScalarRelationFilter, Prisma.ReleaseWhereInput>
  artist?: Prisma.XOR<Prisma.ArtistScalarRelationFilter, Prisma.ArtistWhereInput>
}

export type ReleaseArtistOrderByWithRelationInput = {
  releaseId?: Prisma.SortOrder
  artistId?: Prisma.SortOrder
  role?: Prisma.SortOrder
  release?: Prisma.ReleaseOrderByWithRelationInput
  artist?: Prisma.ArtistOrderByWithRelationInput
}

export type ReleaseArtistWhereUniqueInput = Prisma.AtLeast<{
  releaseId_artistId?: Prisma.ReleaseArtistReleaseIdArtistIdCompoundUniqueInput
  AND?: Prisma.ReleaseArtistWhereInput | Prisma.ReleaseArtistWhereInput[]
  OR?: Prisma.ReleaseArtistWhereInput[]
  NOT?: Prisma.ReleaseArtistWhereInput | Prisma.ReleaseArtistWhereInput[]
  releaseId?: Prisma.StringFilter<"ReleaseArtist"> | string
  artistId?: Prisma.StringFilter<"ReleaseArtist"> | string
  role?: Prisma.EnumArtistRoleFilter<"ReleaseArtist"> | $Enums.ArtistRole
  release?: Prisma.XOR<Prisma.ReleaseScalarRelationFilter, Prisma.ReleaseWhereInput>
  artist?: Prisma.XOR<Prisma.ArtistScalarRelationFilter, Prisma.ArtistWhereInput>
}, "releaseId_artistId">

export type ReleaseArtistOrderByWithAggregationInput = {
  releaseId?: Prisma.SortOrder
  artistId?: Prisma.SortOrder
  role?: Prisma.SortOrder
  _count?: Prisma.ReleaseArtistCountOrderByAggregateInput
  _max?: Prisma.ReleaseArtistMaxOrderByAggregateInput
  _min?: Prisma.ReleaseArtistMinOrderByAggregateInput
}

export type ReleaseArtistScalarWhereWithAggregatesInput = {
  AND?: Prisma.ReleaseArtistScalarWhereWithAggregatesInput | Prisma.ReleaseArtistScalarWhereWithAggregatesInput[]
  OR?: Prisma.ReleaseArtistScalarWhereWithAggregatesInput[]
  NOT?: Prisma.ReleaseArtistScalarWhereWithAggregatesInput | Prisma.ReleaseArtistScalarWhereWithAggregatesInput[]
  releaseId?: Prisma.StringWithAggregatesFilter<"ReleaseArtist"> | string
  artistId?: Prisma.StringWithAggregatesFilter<"ReleaseArtist"> | string
  role?: Prisma.EnumArtistRoleWithAggregatesFilter<"ReleaseArtist"> | $Enums.ArtistRole
}

export type ReleaseArtistCreateInput = {
  role: $Enums.ArtistRole
  release: Prisma.ReleaseCreateNestedOneWithoutArtistsInput
  artist: Prisma.ArtistCreateNestedOneWithoutReleasesInput
}

export type ReleaseArtistUncheckedCreateInput = {
  releaseId: string
  artistId: string
  role: $Enums.ArtistRole
}

export type ReleaseArtistUpdateInput = {
  role?: Prisma.EnumArtistRoleFieldUpdateOperationsInput | $Enums.ArtistRole
  release?: Prisma.ReleaseUpdateOneRequiredWithoutArtistsNestedInput
  artist?: Prisma.ArtistUpdateOneRequiredWithoutReleasesNestedInput
}

export type ReleaseArtistUncheckedUpdateInput = {
  releaseId?: Prisma.StringFieldUpdateOperationsInput | string
  artistId?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.EnumArtistRoleFieldUpdateOperationsInput | $Enums.ArtistRole
}

export type ReleaseArtistCreateManyInput = {
  releaseId: string
  artistId: string
  role: $Enums.ArtistRole
}

export type ReleaseArtistUpdateManyMutationInput = {
  role?: Prisma.EnumArtistRoleFieldUpdateOperationsInput | $Enums.ArtistRole
}

export type ReleaseArtistUncheckedUpdateManyInput = {
  releaseId?: Prisma.StringFieldUpdateOperationsInput | string
  artistId?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.EnumArtistRoleFieldUpdateOperationsInput | $Enums.ArtistRole
}

export type ReleaseArtistListRelationFilter = {
  every?: Prisma.ReleaseArtistWhereInput
  some?: Prisma.ReleaseArtistWhereInput
  none?: Prisma.ReleaseArtistWhereInput
}

export type ReleaseArtistOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type ReleaseArtistReleaseIdArtistIdCompoundUniqueInput = {
  releaseId: string
  artistId: string
}

export type ReleaseArtistCountOrderByAggregateInput = {
  releaseId?: Prisma.SortOrder
  artistId?: Prisma.SortOrder
  role?: Prisma.SortOrder
}

export type ReleaseArtistMaxOrderByAggregateInput = {
  releaseId?: Prisma.SortOrder
  artistId?: Prisma.SortOrder
  role?: Prisma.SortOrder
}

export type ReleaseArtistMinOrderByAggregateInput = {
  releaseId?: Prisma.SortOrder
  artistId?: Prisma.SortOrder
  role?: Prisma.SortOrder
}

export type ReleaseArtistCreateNestedManyWithoutArtistInput = {
  create?: Prisma.XOR<Prisma.ReleaseArtistCreateWithoutArtistInput, Prisma.ReleaseArtistUncheckedCreateWithoutArtistInput> | Prisma.ReleaseArtistCreateWithoutArtistInput[] | Prisma.ReleaseArtistUncheckedCreateWithoutArtistInput[]
  connectOrCreate?: Prisma.ReleaseArtistCreateOrConnectWithoutArtistInput | Prisma.ReleaseArtistCreateOrConnectWithoutArtistInput[]
  createMany?: Prisma.ReleaseArtistCreateManyArtistInputEnvelope
  connect?: Prisma.ReleaseArtistWhereUniqueInput | Prisma.ReleaseArtistWhereUniqueInput[]
}

export type ReleaseArtistUncheckedCreateNestedManyWithoutArtistInput = {
  create?: Prisma.XOR<Prisma.ReleaseArtistCreateWithoutArtistInput, Prisma.ReleaseArtistUncheckedCreateWithoutArtistInput> | Prisma.ReleaseArtistCreateWithoutArtistInput[] | Prisma.ReleaseArtistUncheckedCreateWithoutArtistInput[]
  connectOrCreate?: Prisma.ReleaseArtistCreateOrConnectWithoutArtistInput | Prisma.ReleaseArtistCreateOrConnectWithoutArtistInput[]
  createMany?: Prisma.ReleaseArtistCreateManyArtistInputEnvelope
  connect?: Prisma.ReleaseArtistWhereUniqueInput | Prisma.ReleaseArtistWhereUniqueInput[]
}

export type ReleaseArtistUpdateManyWithoutArtistNestedInput = {
  create?: Prisma.XOR<Prisma.ReleaseArtistCreateWithoutArtistInput, Prisma.ReleaseArtistUncheckedCreateWithoutArtistInput> | Prisma.ReleaseArtistCreateWithoutArtistInput[] | Prisma.ReleaseArtistUncheckedCreateWithoutArtistInput[]
  connectOrCreate?: Prisma.ReleaseArtistCreateOrConnectWithoutArtistInput | Prisma.ReleaseArtistCreateOrConnectWithoutArtistInput[]
  upsert?: Prisma.ReleaseArtistUpsertWithWhereUniqueWithoutArtistInput | Prisma.ReleaseArtistUpsertWithWhereUniqueWithoutArtistInput[]
  createMany?: Prisma.ReleaseArtistCreateManyArtistInputEnvelope
  set?: Prisma.ReleaseArtistWhereUniqueInput | Prisma.ReleaseArtistWhereUniqueInput[]
  disconnect?: Prisma.ReleaseArtistWhereUniqueInput | Prisma.ReleaseArtistWhereUniqueInput[]
  delete?: Prisma.ReleaseArtistWhereUniqueInput | Prisma.ReleaseArtistWhereUniqueInput[]
  connect?: Prisma.ReleaseArtistWhereUniqueInput | Prisma.ReleaseArtistWhereUniqueInput[]
  update?: Prisma.ReleaseArtistUpdateWithWhereUniqueWithoutArtistInput | Prisma.ReleaseArtistUpdateWithWhereUniqueWithoutArtistInput[]
  updateMany?: Prisma.ReleaseArtistUpdateManyWithWhereWithoutArtistInput | Prisma.ReleaseArtistUpdateManyWithWhereWithoutArtistInput[]
  deleteMany?: Prisma.ReleaseArtistScalarWhereInput | Prisma.ReleaseArtistScalarWhereInput[]
}

export type ReleaseArtistUncheckedUpdateManyWithoutArtistNestedInput = {
  create?: Prisma.XOR<Prisma.ReleaseArtistCreateWithoutArtistInput, Prisma.ReleaseArtistUncheckedCreateWithoutArtistInput> | Prisma.ReleaseArtistCreateWithoutArtistInput[] | Prisma.ReleaseArtistUncheckedCreateWithoutArtistInput[]
  connectOrCreate?: Prisma.ReleaseArtistCreateOrConnectWithoutArtistInput | Prisma.ReleaseArtistCreateOrConnectWithoutArtistInput[]
  upsert?: Prisma.ReleaseArtistUpsertWithWhereUniqueWithoutArtistInput | Prisma.ReleaseArtistUpsertWithWhereUniqueWithoutArtistInput[]
  createMany?: Prisma.ReleaseArtistCreateManyArtistInputEnvelope
  set?: Prisma.ReleaseArtistWhereUniqueInput | Prisma.ReleaseArtistWhereUniqueInput[]
  disconnect?: Prisma.ReleaseArtistWhereUniqueInput | Prisma.ReleaseArtistWhereUniqueInput[]
  delete?: Prisma.ReleaseArtistWhereUniqueInput | Prisma.ReleaseArtistWhereUniqueInput[]
  connect?: Prisma.ReleaseArtistWhereUniqueInput | Prisma.ReleaseArtistWhereUniqueInput[]
  update?: Prisma.ReleaseArtistUpdateWithWhereUniqueWithoutArtistInput | Prisma.ReleaseArtistUpdateWithWhereUniqueWithoutArtistInput[]
  updateMany?: Prisma.ReleaseArtistUpdateManyWithWhereWithoutArtistInput | Prisma.ReleaseArtistUpdateManyWithWhereWithoutArtistInput[]
  deleteMany?: Prisma.ReleaseArtistScalarWhereInput | Prisma.ReleaseArtistScalarWhereInput[]
}

export type ReleaseArtistCreateNestedManyWithoutReleaseInput = {
  create?: Prisma.XOR<Prisma.ReleaseArtistCreateWithoutReleaseInput, Prisma.ReleaseArtistUncheckedCreateWithoutReleaseInput> | Prisma.ReleaseArtistCreateWithoutReleaseInput[] | Prisma.ReleaseArtistUncheckedCreateWithoutReleaseInput[]
  connectOrCreate?: Prisma.ReleaseArtistCreateOrConnectWithoutReleaseInput | Prisma.ReleaseArtistCreateOrConnectWithoutReleaseInput[]
  createMany?: Prisma.ReleaseArtistCreateManyReleaseInputEnvelope
  connect?: Prisma.ReleaseArtistWhereUniqueInput | Prisma.ReleaseArtistWhereUniqueInput[]
}

export type ReleaseArtistUncheckedCreateNestedManyWithoutReleaseInput = {
  create?: Prisma.XOR<Prisma.ReleaseArtistCreateWithoutReleaseInput, Prisma.ReleaseArtistUncheckedCreateWithoutReleaseInput> | Prisma.ReleaseArtistCreateWithoutReleaseInput[] | Prisma.ReleaseArtistUncheckedCreateWithoutReleaseInput[]
  connectOrCreate?: Prisma.ReleaseArtistCreateOrConnectWithoutReleaseInput | Prisma.ReleaseArtistCreateOrConnectWithoutReleaseInput[]
  createMany?: Prisma.ReleaseArtistCreateManyReleaseInputEnvelope
  connect?: Prisma.ReleaseArtistWhereUniqueInput | Prisma.ReleaseArtistWhereUniqueInput[]
}

export type ReleaseArtistUpdateManyWithoutReleaseNestedInput = {
  create?: Prisma.XOR<Prisma.ReleaseArtistCreateWithoutReleaseInput, Prisma.ReleaseArtistUncheckedCreateWithoutReleaseInput> | Prisma.ReleaseArtistCreateWithoutReleaseInput[] | Prisma.ReleaseArtistUncheckedCreateWithoutReleaseInput[]
  connectOrCreate?: Prisma.ReleaseArtistCreateOrConnectWithoutReleaseInput | Prisma.ReleaseArtistCreateOrConnectWithoutReleaseInput[]
  upsert?: Prisma.ReleaseArtistUpsertWithWhereUniqueWithoutReleaseInput | Prisma.ReleaseArtistUpsertWithWhereUniqueWithoutReleaseInput[]
  createMany?: Prisma.ReleaseArtistCreateManyReleaseInputEnvelope
  set?: Prisma.ReleaseArtistWhereUniqueInput | Prisma.ReleaseArtistWhereUniqueInput[]
  disconnect?: Prisma.ReleaseArtistWhereUniqueInput | Prisma.ReleaseArtistWhereUniqueInput[]
  delete?: Prisma.ReleaseArtistWhereUniqueInput | Prisma.ReleaseArtistWhereUniqueInput[]
  connect?: Prisma.ReleaseArtistWhereUniqueInput | Prisma.ReleaseArtistWhereUniqueInput[]
  update?: Prisma.ReleaseArtistUpdateWithWhereUniqueWithoutReleaseInput | Prisma.ReleaseArtistUpdateWithWhereUniqueWithoutReleaseInput[]
  updateMany?: Prisma.ReleaseArtistUpdateManyWithWhereWithoutReleaseInput | Prisma.ReleaseArtistUpdateManyWithWhereWithoutReleaseInput[]
  deleteMany?: Prisma.ReleaseArtistScalarWhereInput | Prisma.ReleaseArtistScalarWhereInput[]
}

export type ReleaseArtistUncheckedUpdateManyWithoutReleaseNestedInput = {
  create?: Prisma.XOR<Prisma.ReleaseArtistCreateWithoutReleaseInput, Prisma.ReleaseArtistUncheckedCreateWithoutReleaseInput> | Prisma.ReleaseArtistCreateWithoutReleaseInput[] | Prisma.ReleaseArtistUncheckedCreateWithoutReleaseInput[]
  connectOrCreate?: Prisma.ReleaseArtistCreateOrConnectWithoutReleaseInput | Prisma.ReleaseArtistCreateOrConnectWithoutReleaseInput[]
  upsert?: Prisma.ReleaseArtistUpsertWithWhereUniqueWithoutReleaseInput | Prisma.ReleaseArtistUpsertWithWhereUniqueWithoutReleaseInput[]
  createMany?: Prisma.ReleaseArtistCreateManyReleaseInputEnvelope
  set?: Prisma.ReleaseArtistWhereUniqueInput | Prisma.ReleaseArtistWhereUniqueInput[]
  disconnect?: Prisma.ReleaseArtistWhereUniqueInput | Prisma.ReleaseArtistWhereUniqueInput[]
  delete?: Prisma.ReleaseArtistWhereUniqueInput | Prisma.ReleaseArtistWhereUniqueInput[]
  connect?: Prisma.ReleaseArtistWhereUniqueInput | Prisma.ReleaseArtistWhereUniqueInput[]
  update?: Prisma.ReleaseArtistUpdateWithWhereUniqueWithoutReleaseInput | Prisma.ReleaseArtistUpdateWithWhereUniqueWithoutReleaseInput[]
  updateMany?: Prisma.ReleaseArtistUpdateManyWithWhereWithoutReleaseInput | Prisma.ReleaseArtistUpdateManyWithWhereWithoutReleaseInput[]
  deleteMany?: Prisma.ReleaseArtistScalarWhereInput | Prisma.ReleaseArtistScalarWhereInput[]
}

export type EnumArtistRoleFieldUpdateOperationsInput = {
  set?: $Enums.ArtistRole
}

export type ReleaseArtistCreateWithoutArtistInput = {
  role: $Enums.ArtistRole
  release: Prisma.ReleaseCreateNestedOneWithoutArtistsInput
}

export type ReleaseArtistUncheckedCreateWithoutArtistInput = {
  releaseId: string
  role: $Enums.ArtistRole
}

export type ReleaseArtistCreateOrConnectWithoutArtistInput = {
  where: Prisma.ReleaseArtistWhereUniqueInput
  create: Prisma.XOR<Prisma.ReleaseArtistCreateWithoutArtistInput, Prisma.ReleaseArtistUncheckedCreateWithoutArtistInput>
}

export type ReleaseArtistCreateManyArtistInputEnvelope = {
  data: Prisma.ReleaseArtistCreateManyArtistInput | Prisma.ReleaseArtistCreateManyArtistInput[]
  skipDuplicates?: boolean
}

export type ReleaseArtistUpsertWithWhereUniqueWithoutArtistInput = {
  where: Prisma.ReleaseArtistWhereUniqueInput
  update: Prisma.XOR<Prisma.ReleaseArtistUpdateWithoutArtistInput, Prisma.ReleaseArtistUncheckedUpdateWithoutArtistInput>
  create: Prisma.XOR<Prisma.ReleaseArtistCreateWithoutArtistInput, Prisma.ReleaseArtistUncheckedCreateWithoutArtistInput>
}

export type ReleaseArtistUpdateWithWhereUniqueWithoutArtistInput = {
  where: Prisma.ReleaseArtistWhereUniqueInput
  data: Prisma.XOR<Prisma.ReleaseArtistUpdateWithoutArtistInput, Prisma.ReleaseArtistUncheckedUpdateWithoutArtistInput>
}

export type ReleaseArtistUpdateManyWithWhereWithoutArtistInput = {
  where: Prisma.ReleaseArtistScalarWhereInput
  data: Prisma.XOR<Prisma.ReleaseArtistUpdateManyMutationInput, Prisma.ReleaseArtistUncheckedUpdateManyWithoutArtistInput>
}

export type ReleaseArtistScalarWhereInput = {
  AND?: Prisma.ReleaseArtistScalarWhereInput | Prisma.ReleaseArtistScalarWhereInput[]
  OR?: Prisma.ReleaseArtistScalarWhereInput[]
  NOT?: Prisma.ReleaseArtistScalarWhereInput | Prisma.ReleaseArtistScalarWhereInput[]
  releaseId?: Prisma.StringFilter<"ReleaseArtist"> | string
  artistId?: Prisma.StringFilter<"ReleaseArtist"> | string
  role?: Prisma.EnumArtistRoleFilter<"ReleaseArtist"> | $Enums.ArtistRole
}

export type ReleaseArtistCreateWithoutReleaseInput = {
  role: $Enums.ArtistRole
  artist: Prisma.ArtistCreateNestedOneWithoutReleasesInput
}

export type ReleaseArtistUncheckedCreateWithoutReleaseInput = {
  artistId: string
  role: $Enums.ArtistRole
}

export type ReleaseArtistCreateOrConnectWithoutReleaseInput = {
  where: Prisma.ReleaseArtistWhereUniqueInput
  create: Prisma.XOR<Prisma.ReleaseArtistCreateWithoutReleaseInput, Prisma.ReleaseArtistUncheckedCreateWithoutReleaseInput>
}

export type ReleaseArtistCreateManyReleaseInputEnvelope = {
  data: Prisma.ReleaseArtistCreateManyReleaseInput | Prisma.ReleaseArtistCreateManyReleaseInput[]
  skipDuplicates?: boolean
}

export type ReleaseArtistUpsertWithWhereUniqueWithoutReleaseInput = {
  where: Prisma.ReleaseArtistWhereUniqueInput
  update: Prisma.XOR<Prisma.ReleaseArtistUpdateWithoutReleaseInput, Prisma.ReleaseArtistUncheckedUpdateWithoutReleaseInput>
  create: Prisma.XOR<Prisma.ReleaseArtistCreateWithoutReleaseInput, Prisma.ReleaseArtistUncheckedCreateWithoutReleaseInput>
}

export type ReleaseArtistUpdateWithWhereUniqueWithoutReleaseInput = {
  where: Prisma.ReleaseArtistWhereUniqueInput
  data: Prisma.XOR<Prisma.ReleaseArtistUpdateWithoutReleaseInput, Prisma.ReleaseArtistUncheckedUpdateWithoutReleaseInput>
}

export type ReleaseArtistUpdateManyWithWhereWithoutReleaseInput = {
  where: Prisma.ReleaseArtistScalarWhereInput
  data: Prisma.XOR<Prisma.ReleaseArtistUpdateManyMutationInput, Prisma.ReleaseArtistUncheckedUpdateManyWithoutReleaseInput>
}

export type ReleaseArtistCreateManyArtistInput = {
  releaseId: string
  role: $Enums.ArtistRole
}

export type ReleaseArtistUpdateWithoutArtistInput = {
  role?: Prisma.EnumArtistRoleFieldUpdateOperationsInput | $Enums.ArtistRole
  release?: Prisma.ReleaseUpdateOneRequiredWithoutArtistsNestedInput
}

export type ReleaseArtistUncheckedUpdateWithoutArtistInput = {
  releaseId?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.EnumArtistRoleFieldUpdateOperationsInput | $Enums.ArtistRole
}

export type ReleaseArtistUncheckedUpdateManyWithoutArtistInput = {
  releaseId?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.EnumArtistRoleFieldUpdateOperationsInput | $Enums.ArtistRole
}

export type ReleaseArtistCreateManyReleaseInput = {
  artistId: string
  role: $Enums.ArtistRole
}

export type ReleaseArtistUpdateWithoutReleaseInput = {
  role?: Prisma.EnumArtistRoleFieldUpdateOperationsInput | $Enums.ArtistRole
  artist?: Prisma.ArtistUpdateOneRequiredWithoutReleasesNestedInput
}

export type ReleaseArtistUncheckedUpdateWithoutReleaseInput = {
  artistId?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.EnumArtistRoleFieldUpdateOperationsInput | $Enums.ArtistRole
}

export type ReleaseArtistUncheckedUpdateManyWithoutReleaseInput = {
  artistId?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.EnumArtistRoleFieldUpdateOperationsInput | $Enums.ArtistRole
}



export type ReleaseArtistSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  releaseId?: boolean
  artistId?: boolean
  role?: boolean
  release?: boolean | Prisma.ReleaseDefaultArgs<ExtArgs>
  artist?: boolean | Prisma.ArtistDefaultArgs<ExtArgs>
}, ExtArgs["result"]["releaseArtist"]>

export type ReleaseArtistSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  releaseId?: boolean
  artistId?: boolean
  role?: boolean
  release?: boolean | Prisma.ReleaseDefaultArgs<ExtArgs>
  artist?: boolean | Prisma.ArtistDefaultArgs<ExtArgs>
}, ExtArgs["result"]["releaseArtist"]>

export type ReleaseArtistSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  releaseId?: boolean
  artistId?: boolean
  role?: boolean
  release?: boolean | Prisma.ReleaseDefaultArgs<ExtArgs>
  artist?: boolean | Prisma.ArtistDefaultArgs<ExtArgs>
}, ExtArgs["result"]["releaseArtist"]>

export type ReleaseArtistSelectScalar = {
  releaseId?: boolean
  artistId?: boolean
  role?: boolean
}

export type ReleaseArtistOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"releaseId" | "artistId" | "role", ExtArgs["result"]["releaseArtist"]>
export type ReleaseArtistInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  release?: boolean | Prisma.ReleaseDefaultArgs<ExtArgs>
  artist?: boolean | Prisma.ArtistDefaultArgs<ExtArgs>
}
export type ReleaseArtistIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  release?: boolean | Prisma.ReleaseDefaultArgs<ExtArgs>
  artist?: boolean | Prisma.ArtistDefaultArgs<ExtArgs>
}
export type ReleaseArtistIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  release?: boolean | Prisma.ReleaseDefaultArgs<ExtArgs>
  artist?: boolean | Prisma.ArtistDefaultArgs<ExtArgs>
}

export type $ReleaseArtistPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "ReleaseArtist"
  objects: {
    release: Prisma.$ReleasePayload<ExtArgs>
    artist: Prisma.$ArtistPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    releaseId: string
    artistId: string
    role: $Enums.ArtistRole
  }, ExtArgs["result"]["releaseArtist"]>
  composites: {}
}

export type ReleaseArtistGetPayload<S extends boolean | null | undefined | ReleaseArtistDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$ReleaseArtistPayload, S>

export type ReleaseArtistCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<ReleaseArtistFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: ReleaseArtistCountAggregateInputType | true
  }

export interface ReleaseArtistDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['ReleaseArtist'], meta: { name: 'ReleaseArtist' } }
  /**
   * Find zero or one ReleaseArtist that matches the filter.
   * @param {ReleaseArtistFindUniqueArgs} args - Arguments to find a ReleaseArtist
   * @example
   * // Get one ReleaseArtist
   * const releaseArtist = await prisma.releaseArtist.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends ReleaseArtistFindUniqueArgs>(args: Prisma.SelectSubset<T, ReleaseArtistFindUniqueArgs<ExtArgs>>): Prisma.Prisma__ReleaseArtistClient<runtime.Types.Result.GetResult<Prisma.$ReleaseArtistPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one ReleaseArtist that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {ReleaseArtistFindUniqueOrThrowArgs} args - Arguments to find a ReleaseArtist
   * @example
   * // Get one ReleaseArtist
   * const releaseArtist = await prisma.releaseArtist.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends ReleaseArtistFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, ReleaseArtistFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__ReleaseArtistClient<runtime.Types.Result.GetResult<Prisma.$ReleaseArtistPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first ReleaseArtist that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReleaseArtistFindFirstArgs} args - Arguments to find a ReleaseArtist
   * @example
   * // Get one ReleaseArtist
   * const releaseArtist = await prisma.releaseArtist.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends ReleaseArtistFindFirstArgs>(args?: Prisma.SelectSubset<T, ReleaseArtistFindFirstArgs<ExtArgs>>): Prisma.Prisma__ReleaseArtistClient<runtime.Types.Result.GetResult<Prisma.$ReleaseArtistPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first ReleaseArtist that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReleaseArtistFindFirstOrThrowArgs} args - Arguments to find a ReleaseArtist
   * @example
   * // Get one ReleaseArtist
   * const releaseArtist = await prisma.releaseArtist.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends ReleaseArtistFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, ReleaseArtistFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__ReleaseArtistClient<runtime.Types.Result.GetResult<Prisma.$ReleaseArtistPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more ReleaseArtists that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReleaseArtistFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all ReleaseArtists
   * const releaseArtists = await prisma.releaseArtist.findMany()
   * 
   * // Get first 10 ReleaseArtists
   * const releaseArtists = await prisma.releaseArtist.findMany({ take: 10 })
   * 
   * // Only select the `releaseId`
   * const releaseArtistWithReleaseIdOnly = await prisma.releaseArtist.findMany({ select: { releaseId: true } })
   * 
   */
  findMany<T extends ReleaseArtistFindManyArgs>(args?: Prisma.SelectSubset<T, ReleaseArtistFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ReleaseArtistPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a ReleaseArtist.
   * @param {ReleaseArtistCreateArgs} args - Arguments to create a ReleaseArtist.
   * @example
   * // Create one ReleaseArtist
   * const ReleaseArtist = await prisma.releaseArtist.create({
   *   data: {
   *     // ... data to create a ReleaseArtist
   *   }
   * })
   * 
   */
  create<T extends ReleaseArtistCreateArgs>(args: Prisma.SelectSubset<T, ReleaseArtistCreateArgs<ExtArgs>>): Prisma.Prisma__ReleaseArtistClient<runtime.Types.Result.GetResult<Prisma.$ReleaseArtistPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many ReleaseArtists.
   * @param {ReleaseArtistCreateManyArgs} args - Arguments to create many ReleaseArtists.
   * @example
   * // Create many ReleaseArtists
   * const releaseArtist = await prisma.releaseArtist.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends ReleaseArtistCreateManyArgs>(args?: Prisma.SelectSubset<T, ReleaseArtistCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many ReleaseArtists and returns the data saved in the database.
   * @param {ReleaseArtistCreateManyAndReturnArgs} args - Arguments to create many ReleaseArtists.
   * @example
   * // Create many ReleaseArtists
   * const releaseArtist = await prisma.releaseArtist.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many ReleaseArtists and only return the `releaseId`
   * const releaseArtistWithReleaseIdOnly = await prisma.releaseArtist.createManyAndReturn({
   *   select: { releaseId: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends ReleaseArtistCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, ReleaseArtistCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ReleaseArtistPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a ReleaseArtist.
   * @param {ReleaseArtistDeleteArgs} args - Arguments to delete one ReleaseArtist.
   * @example
   * // Delete one ReleaseArtist
   * const ReleaseArtist = await prisma.releaseArtist.delete({
   *   where: {
   *     // ... filter to delete one ReleaseArtist
   *   }
   * })
   * 
   */
  delete<T extends ReleaseArtistDeleteArgs>(args: Prisma.SelectSubset<T, ReleaseArtistDeleteArgs<ExtArgs>>): Prisma.Prisma__ReleaseArtistClient<runtime.Types.Result.GetResult<Prisma.$ReleaseArtistPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one ReleaseArtist.
   * @param {ReleaseArtistUpdateArgs} args - Arguments to update one ReleaseArtist.
   * @example
   * // Update one ReleaseArtist
   * const releaseArtist = await prisma.releaseArtist.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends ReleaseArtistUpdateArgs>(args: Prisma.SelectSubset<T, ReleaseArtistUpdateArgs<ExtArgs>>): Prisma.Prisma__ReleaseArtistClient<runtime.Types.Result.GetResult<Prisma.$ReleaseArtistPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more ReleaseArtists.
   * @param {ReleaseArtistDeleteManyArgs} args - Arguments to filter ReleaseArtists to delete.
   * @example
   * // Delete a few ReleaseArtists
   * const { count } = await prisma.releaseArtist.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends ReleaseArtistDeleteManyArgs>(args?: Prisma.SelectSubset<T, ReleaseArtistDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more ReleaseArtists.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReleaseArtistUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many ReleaseArtists
   * const releaseArtist = await prisma.releaseArtist.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends ReleaseArtistUpdateManyArgs>(args: Prisma.SelectSubset<T, ReleaseArtistUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more ReleaseArtists and returns the data updated in the database.
   * @param {ReleaseArtistUpdateManyAndReturnArgs} args - Arguments to update many ReleaseArtists.
   * @example
   * // Update many ReleaseArtists
   * const releaseArtist = await prisma.releaseArtist.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more ReleaseArtists and only return the `releaseId`
   * const releaseArtistWithReleaseIdOnly = await prisma.releaseArtist.updateManyAndReturn({
   *   select: { releaseId: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends ReleaseArtistUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, ReleaseArtistUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ReleaseArtistPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one ReleaseArtist.
   * @param {ReleaseArtistUpsertArgs} args - Arguments to update or create a ReleaseArtist.
   * @example
   * // Update or create a ReleaseArtist
   * const releaseArtist = await prisma.releaseArtist.upsert({
   *   create: {
   *     // ... data to create a ReleaseArtist
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the ReleaseArtist we want to update
   *   }
   * })
   */
  upsert<T extends ReleaseArtistUpsertArgs>(args: Prisma.SelectSubset<T, ReleaseArtistUpsertArgs<ExtArgs>>): Prisma.Prisma__ReleaseArtistClient<runtime.Types.Result.GetResult<Prisma.$ReleaseArtistPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of ReleaseArtists.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReleaseArtistCountArgs} args - Arguments to filter ReleaseArtists to count.
   * @example
   * // Count the number of ReleaseArtists
   * const count = await prisma.releaseArtist.count({
   *   where: {
   *     // ... the filter for the ReleaseArtists we want to count
   *   }
   * })
  **/
  count<T extends ReleaseArtistCountArgs>(
    args?: Prisma.Subset<T, ReleaseArtistCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], ReleaseArtistCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a ReleaseArtist.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReleaseArtistAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends ReleaseArtistAggregateArgs>(args: Prisma.Subset<T, ReleaseArtistAggregateArgs>): Prisma.PrismaPromise<GetReleaseArtistAggregateType<T>>

  /**
   * Group by ReleaseArtist.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReleaseArtistGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends ReleaseArtistGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: ReleaseArtistGroupByArgs['orderBy'] }
      : { orderBy?: ReleaseArtistGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, ReleaseArtistGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetReleaseArtistGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the ReleaseArtist model
 */
readonly fields: ReleaseArtistFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for ReleaseArtist.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__ReleaseArtistClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  release<T extends Prisma.ReleaseDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.ReleaseDefaultArgs<ExtArgs>>): Prisma.Prisma__ReleaseClient<runtime.Types.Result.GetResult<Prisma.$ReleasePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  artist<T extends Prisma.ArtistDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.ArtistDefaultArgs<ExtArgs>>): Prisma.Prisma__ArtistClient<runtime.Types.Result.GetResult<Prisma.$ArtistPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the ReleaseArtist model
 */
export interface ReleaseArtistFieldRefs {
  readonly releaseId: Prisma.FieldRef<"ReleaseArtist", 'String'>
  readonly artistId: Prisma.FieldRef<"ReleaseArtist", 'String'>
  readonly role: Prisma.FieldRef<"ReleaseArtist", 'ArtistRole'>
}
    

// Custom InputTypes
/**
 * ReleaseArtist findUnique
 */
export type ReleaseArtistFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseArtist
   */
  select?: Prisma.ReleaseArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseArtist
   */
  omit?: Prisma.ReleaseArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseArtistInclude<ExtArgs> | null
  /**
   * Filter, which ReleaseArtist to fetch.
   */
  where: Prisma.ReleaseArtistWhereUniqueInput
}

/**
 * ReleaseArtist findUniqueOrThrow
 */
export type ReleaseArtistFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseArtist
   */
  select?: Prisma.ReleaseArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseArtist
   */
  omit?: Prisma.ReleaseArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseArtistInclude<ExtArgs> | null
  /**
   * Filter, which ReleaseArtist to fetch.
   */
  where: Prisma.ReleaseArtistWhereUniqueInput
}

/**
 * ReleaseArtist findFirst
 */
export type ReleaseArtistFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseArtist
   */
  select?: Prisma.ReleaseArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseArtist
   */
  omit?: Prisma.ReleaseArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseArtistInclude<ExtArgs> | null
  /**
   * Filter, which ReleaseArtist to fetch.
   */
  where?: Prisma.ReleaseArtistWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ReleaseArtists to fetch.
   */
  orderBy?: Prisma.ReleaseArtistOrderByWithRelationInput | Prisma.ReleaseArtistOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for ReleaseArtists.
   */
  cursor?: Prisma.ReleaseArtistWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ReleaseArtists from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ReleaseArtists.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of ReleaseArtists.
   */
  distinct?: Prisma.ReleaseArtistScalarFieldEnum | Prisma.ReleaseArtistScalarFieldEnum[]
}

/**
 * ReleaseArtist findFirstOrThrow
 */
export type ReleaseArtistFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseArtist
   */
  select?: Prisma.ReleaseArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseArtist
   */
  omit?: Prisma.ReleaseArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseArtistInclude<ExtArgs> | null
  /**
   * Filter, which ReleaseArtist to fetch.
   */
  where?: Prisma.ReleaseArtistWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ReleaseArtists to fetch.
   */
  orderBy?: Prisma.ReleaseArtistOrderByWithRelationInput | Prisma.ReleaseArtistOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for ReleaseArtists.
   */
  cursor?: Prisma.ReleaseArtistWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ReleaseArtists from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ReleaseArtists.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of ReleaseArtists.
   */
  distinct?: Prisma.ReleaseArtistScalarFieldEnum | Prisma.ReleaseArtistScalarFieldEnum[]
}

/**
 * ReleaseArtist findMany
 */
export type ReleaseArtistFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseArtist
   */
  select?: Prisma.ReleaseArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseArtist
   */
  omit?: Prisma.ReleaseArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseArtistInclude<ExtArgs> | null
  /**
   * Filter, which ReleaseArtists to fetch.
   */
  where?: Prisma.ReleaseArtistWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ReleaseArtists to fetch.
   */
  orderBy?: Prisma.ReleaseArtistOrderByWithRelationInput | Prisma.ReleaseArtistOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing ReleaseArtists.
   */
  cursor?: Prisma.ReleaseArtistWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ReleaseArtists from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ReleaseArtists.
   */
  skip?: number
  distinct?: Prisma.ReleaseArtistScalarFieldEnum | Prisma.ReleaseArtistScalarFieldEnum[]
}

/**
 * ReleaseArtist create
 */
export type ReleaseArtistCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseArtist
   */
  select?: Prisma.ReleaseArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseArtist
   */
  omit?: Prisma.ReleaseArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseArtistInclude<ExtArgs> | null
  /**
   * The data needed to create a ReleaseArtist.
   */
  data: Prisma.XOR<Prisma.ReleaseArtistCreateInput, Prisma.ReleaseArtistUncheckedCreateInput>
}

/**
 * ReleaseArtist createMany
 */
export type ReleaseArtistCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many ReleaseArtists.
   */
  data: Prisma.ReleaseArtistCreateManyInput | Prisma.ReleaseArtistCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * ReleaseArtist createManyAndReturn
 */
export type ReleaseArtistCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseArtist
   */
  select?: Prisma.ReleaseArtistSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseArtist
   */
  omit?: Prisma.ReleaseArtistOmit<ExtArgs> | null
  /**
   * The data used to create many ReleaseArtists.
   */
  data: Prisma.ReleaseArtistCreateManyInput | Prisma.ReleaseArtistCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseArtistIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * ReleaseArtist update
 */
export type ReleaseArtistUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseArtist
   */
  select?: Prisma.ReleaseArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseArtist
   */
  omit?: Prisma.ReleaseArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseArtistInclude<ExtArgs> | null
  /**
   * The data needed to update a ReleaseArtist.
   */
  data: Prisma.XOR<Prisma.ReleaseArtistUpdateInput, Prisma.ReleaseArtistUncheckedUpdateInput>
  /**
   * Choose, which ReleaseArtist to update.
   */
  where: Prisma.ReleaseArtistWhereUniqueInput
}

/**
 * ReleaseArtist updateMany
 */
export type ReleaseArtistUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update ReleaseArtists.
   */
  data: Prisma.XOR<Prisma.ReleaseArtistUpdateManyMutationInput, Prisma.ReleaseArtistUncheckedUpdateManyInput>
  /**
   * Filter which ReleaseArtists to update
   */
  where?: Prisma.ReleaseArtistWhereInput
  /**
   * Limit how many ReleaseArtists to update.
   */
  limit?: number
}

/**
 * ReleaseArtist updateManyAndReturn
 */
export type ReleaseArtistUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseArtist
   */
  select?: Prisma.ReleaseArtistSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseArtist
   */
  omit?: Prisma.ReleaseArtistOmit<ExtArgs> | null
  /**
   * The data used to update ReleaseArtists.
   */
  data: Prisma.XOR<Prisma.ReleaseArtistUpdateManyMutationInput, Prisma.ReleaseArtistUncheckedUpdateManyInput>
  /**
   * Filter which ReleaseArtists to update
   */
  where?: Prisma.ReleaseArtistWhereInput
  /**
   * Limit how many ReleaseArtists to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseArtistIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * ReleaseArtist upsert
 */
export type ReleaseArtistUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseArtist
   */
  select?: Prisma.ReleaseArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseArtist
   */
  omit?: Prisma.ReleaseArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseArtistInclude<ExtArgs> | null
  /**
   * The filter to search for the ReleaseArtist to update in case it exists.
   */
  where: Prisma.ReleaseArtistWhereUniqueInput
  /**
   * In case the ReleaseArtist found by the `where` argument doesn't exist, create a new ReleaseArtist with this data.
   */
  create: Prisma.XOR<Prisma.ReleaseArtistCreateInput, Prisma.ReleaseArtistUncheckedCreateInput>
  /**
   * In case the ReleaseArtist was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.ReleaseArtistUpdateInput, Prisma.ReleaseArtistUncheckedUpdateInput>
}

/**
 * ReleaseArtist delete
 */
export type ReleaseArtistDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseArtist
   */
  select?: Prisma.ReleaseArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseArtist
   */
  omit?: Prisma.ReleaseArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseArtistInclude<ExtArgs> | null
  /**
   * Filter which ReleaseArtist to delete.
   */
  where: Prisma.ReleaseArtistWhereUniqueInput
}

/**
 * ReleaseArtist deleteMany
 */
export type ReleaseArtistDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which ReleaseArtists to delete
   */
  where?: Prisma.ReleaseArtistWhereInput
  /**
   * Limit how many ReleaseArtists to delete.
   */
  limit?: number
}

/**
 * ReleaseArtist without action
 */
export type ReleaseArtistDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseArtist
   */
  select?: Prisma.ReleaseArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseArtist
   */
  omit?: Prisma.ReleaseArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseArtistInclude<ExtArgs> | null
}
