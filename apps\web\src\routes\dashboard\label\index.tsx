import { createFileRoute } from "@tanstack/react-router";
import { authClient } from "@/lib/auth-client";
import { useEffect } from "react";
import Loader from "@/components/loader";
import { LabelTable } from "@/routes/dashboard/label/components";

export const Route = createFileRoute("/dashboard/label/")({
  component: RouteComponent,
  head: () => ({
    meta: [
      {
        title: "Label Management - Soundmera App",
      },
    ],
  }),
});

function RouteComponent() {
  const navigate = Route.useNavigate();
  const { data: session, isPending } = authClient.useSession();

  useEffect(() => {
    if (!session && !isPending) {
      navigate({
        to: "/auth",
      });
      return;
    }
  }, [session, isPending, navigate]);

  if (isPending) {
    return <Loader />;
  }

  if (!session || !session.user) {
    return <Loader />;
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="pl-2 text-xl font-bold">Label Management</div>
      <div className="p-2">
        <LabelTable />
      </div>
    </div>
  );
}
