import { useState, useEffect } from "react";
import { Loader2, PlusIcon } from "lucide-react";
import { toast } from "sonner";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";

import { useTRPCClient } from "@/utils/trpc";
import { authClient } from "@/lib/auth-client";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { GenreSelect } from "@/components/ui/genre-select";

interface CreateTrackDialogProps {
  children: React.ReactNode;
  onTrackCreated: () => void;
}

const EXPLICIT_OPTIONS = [
  { value: "NOT_EXPLICIT", label: "Not Explicit" },
  { value: "EXPLICIT", label: "Explicit" },
  { value: "CLEAN", label: "Clean" },
] as const;

const RIGHTS_CLAIM_OPTIONS = [
  { value: "NO_CLAIM", label: "No Claim" },
  { value: "REPORT", label: "Report" },
  { value: "MONETIZE", label: "Monetize" },
  { value: "BLOCK", label: "Block" },
] as const;

const STATUS_OPTIONS = [
  { value: "DRAFT", label: "Draft" },
  { value: "READY", label: "Ready" },
] as const;

const LANGUAGE_OPTIONS = [
  { value: "en", label: "English" },
  { value: "es", label: "Spanish" },
  { value: "fr", label: "French" },
  { value: "de", label: "German" },
  { value: "it", label: "Italian" },
  { value: "pt", label: "Portuguese" },
  { value: "ja", label: "Japanese" },
  { value: "ko", label: "Korean" },
  { value: "zh", label: "Chinese" },
  { value: "ar", label: "Arabic" },
  { value: "hi", label: "Hindi" },
  { value: "ru", label: "Russian" },
] as const;

export function CreateTrackDialog({
  children,
  onTrackCreated,
}: CreateTrackDialogProps) {
  const [open, setOpen] = useState(false);
  const navigate = useNavigate();
  const trpcClient = useTRPCClient();
  const { data: session } = authClient.useSession();
  const isAdmin = session?.user?.role === "admin";
  
  const currentYear = new Date().getFullYear();
  
  const [formData, setFormData] = useState({
    title: "",
    isrc: "",
    trackVersion: "",
    recordingYear: currentYear,
    publishingYear: currentYear,
    publishingHolder: "",
    genre: "",
    subGenre: "",
    lyrics: "",
    previewStart: "",
    previewLength: "",
    metadataLanguage: "en",
    explicit: "NOT_EXPLICIT" as const,
    audioLanguage: "en",
    rightsClaim: "NO_CLAIM" as const,
    status: "DRAFT" as const,
    // These will be handled separately
    trackFiles: [] as any[],
    artists: [] as any[],
    contributors: [] as any[],
  });

  // Fetch artists for selection
  const {
    data: artists,
    isLoading: artistsLoading,
  } = useQuery({
    queryKey: ["artistsForSelect"],
    queryFn: () => trpcClient.artist.getAll.query({ page: 1, limit: 100 }),
    enabled: open,
  });

  // Fetch contributors for selection
  const {
    data: contributors,
    isLoading: contributorsLoading,
  } = useQuery({
    queryKey: ["contributorsForSelect"],
    queryFn: () => trpcClient.contributor.getAll.query({ page: 1, limit: 100 }),
    enabled: open,
  });

  const createTrackMutation = useMutation({
    mutationFn: async (data: typeof formData) => {
      // Basic validation
      if (!data.title.trim()) {
        throw new Error("Track title is required");
      }
      if (!data.publishingHolder.trim()) {
        throw new Error("Publishing holder is required");
      }
      if (!data.genre.trim()) {
        throw new Error("Genre is required");
      }
      if (data.artists.length === 0) {
        throw new Error("At least one artist is required");
      }
      if (data.contributors.length === 0) {
        throw new Error("At least one contributor is required");
      }
      if (data.trackFiles.length === 0) {
        throw new Error("At least one track file is required");
      }

      return trpcClient.track.create.mutate({
        title: data.title.trim(),
        isrc: data.isrc.trim() || undefined,
        trackVersion: data.trackVersion.trim() || undefined,
        recordingYear: data.recordingYear,
        publishingYear: data.publishingYear,
        publishingHolder: data.publishingHolder.trim(),
        genre: data.genre.trim(),
        subGenre: data.subGenre.trim() || undefined,
        lyrics: data.lyrics.trim() || undefined,
        previewStart: data.previewStart.trim() || undefined,
        previewLength: data.previewLength.trim() || undefined,
        metadataLanguage: data.metadataLanguage,
        explicit: data.explicit,
        audioLanguage: data.audioLanguage,
        rightsClaim: data.rightsClaim,
        status: data.status,
        trackFiles: data.trackFiles,
        artists: data.artists,
        contributors: data.contributors,
      });
    },
    onSuccess: (data) => {
      toast.success("Track created successfully");
      setOpen(false);
      resetForm();
      onTrackCreated();

      // Redirect to the track details page when implemented
      // if (data?.id) {
      //   navigate({
      //     to: "/dashboard/track/$id",
      //     params: { id: data.id },
      //   });
      // }
    },
    onError: (error: any) => {
      console.error("Failed to create track:", error);
      toast.error(
        "Failed to create track: " + (error.message || "Unknown error")
      );
    },
  });

  const resetForm = () => {
    setFormData({
      title: "",
      isrc: "",
      trackVersion: "",
      recordingYear: currentYear,
      publishingYear: currentYear,
      publishingHolder: "",
      genre: "",
      subGenre: "",
      lyrics: "",
      previewStart: "",
      previewLength: "",
      metadataLanguage: "en",
      explicit: "NOT_EXPLICIT",
      audioLanguage: "en",
      rightsClaim: "NO_CLAIM",
      status: "DRAFT",
      trackFiles: [],
      artists: [],
      contributors: [],
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      await createTrackMutation.mutateAsync(formData);
    } catch (error) {
      // Error handling is done in the mutation
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Track</DialogTitle>
          <DialogDescription>
            Add a new track to your catalog. All required fields must be filled.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="title" className="block">
                Track Title <span className="text-red-500">*</span>
              </Label>
              <Input
                id="title"
                placeholder="Enter track title"
                value={formData.title}
                onChange={(e) => handleInputChange("title", e.target.value)}
                required
                disabled={createTrackMutation.isPending}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="isrc">ISRC</Label>
              <Input
                id="isrc"
                placeholder="CC-XXX-YY-NNNNN"
                value={formData.isrc}
                onChange={(e) => handleInputChange("isrc", e.target.value)}
                disabled={createTrackMutation.isPending}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="genre">
                Genre <span className="text-red-500">*</span>
              </Label>
              <GenreSelect
                value={formData.genre}
                onValueChange={(value) => handleInputChange("genre", value)}
                disabled={createTrackMutation.isPending}
                placeholder="Select a genre"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="subGenre">Sub Genre</Label>
              <Input
                id="subGenre"
                placeholder="Enter sub genre"
                value={formData.subGenre}
                onChange={(e) => handleInputChange("subGenre", e.target.value)}
                disabled={createTrackMutation.isPending}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="trackVersion">Track Version</Label>
              <Input
                id="trackVersion"
                placeholder="e.g., Radio Edit, Extended Mix"
                value={formData.trackVersion}
                onChange={(e) => handleInputChange("trackVersion", e.target.value)}
                disabled={createTrackMutation.isPending}
              />
            </div>
          </div>

          {/* Note: This is a simplified version. The full implementation would include:
              - File upload component for track files
              - Artist selection/management
              - Contributor selection/management
              - More detailed form fields
              - Better validation
          */}
          
          <div className="text-center py-4 text-muted-foreground">
            <p>This is a simplified create dialog.</p>
            <p>Full implementation would include file upload, artist/contributor management, and more fields.</p>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={createTrackMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={createTrackMutation.isPending || !formData.title.trim()}
            >
              {createTrackMutation.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                "Create Track"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
