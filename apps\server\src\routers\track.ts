import { protectedProcedure, router } from "@/lib/trpc";
import { z } from "zod";
import prisma from "../../prisma/index";
import { TRPCError } from "@trpc/server";

const RightsClaimEnum = z.enum(["NO_CLAIM", "REPORT", "MONETIZE", "BLOCK"]);
const TrackStatusEnum = z.enum(["DRAFT", "READY"]);
const ExplicitContentEnum = z.enum(["EXPLICIT", "NOT_EXPLICIT", "CLEAN"]);
const ArtistRoleEnum = z.enum(["PRIMARY", "FEATURING"]);

// ISRC validation (format: CC-XXX-YY-NNNNN)
const isrcSchema = z.string().optional();

// Year validation
const yearSchema = z
  .number()
  .min(1900, "Year must be greater than 1900")
  .max(
    new Date().getFullYear() + 1,
    "Year cannot be more than 1 year in the future"
  );

// Preview timing validation (format: MM:SS)
const timeFormatSchema = z
  .string()
  .regex(/^\d{1,2}:\d{2}$/, "Time must be in MM:SS format")
  .optional();

// Language code validation (ISO 639-1)
const languageSchema = z
  .string()
  .length(2, "Language must be a 2-letter ISO code");

const TrackArtistSchema = z.object({
  artistId: z.string().uuid("Invalid artist ID"),
  role: ArtistRoleEnum,
});

const TrackContributorSchema = z.object({
  contributorId: z.string().uuid("Invalid contributor ID"),
  role: z.string().min(1, "Contributor role is required"),
});

const TrackFileSchema = z.object({
  fileUrl: z.string().url("Invalid file URL"),
  fileKey: z.string().min(1, "File key is required"),
  fileName: z.string().optional(),
  fileSize: z.number().positive("File size must be positive").optional(),
  mimeType: z.string().optional(),
  duration: z.number().positive("Duration must be positive").optional(),
});

const CreateTrackSchema = z.object({
  title: z.string().min(1, "Title is required").max(200, "Title too long"),
  isrc: isrcSchema,
  trackVersion: z.string().max(100, "Track version too long").optional(),
  recordingYear: yearSchema,
  publishingYear: yearSchema,
  publishingHolder: z
    .string()
    .min(1, "Publishing holder is required")
    .max(200, "Publishing holder too long"),
  genre: z.string().min(1, "Genre is required").max(50, "Genre too long"),
  subGenre: z.string().max(50, "Sub-genre too long").optional(),
  lyrics: z
    .string()
    .max(
      10000,
      "Lyrics too long, contact support if you need to upload a longer lyrics"
    )
    .optional(),
  previewStart: timeFormatSchema,
  previewLength: timeFormatSchema,
  metadataLanguage: languageSchema,
  explicit: ExplicitContentEnum,
  audioLanguage: languageSchema,
  rightsClaim: RightsClaimEnum,
  status: TrackStatusEnum.default("DRAFT"),
  trackFiles: z
    .array(TrackFileSchema)
    .min(1, "At least one track file is required"),
  artists: z.array(TrackArtistSchema).min(1, "At least one artist is required"),
  contributors: z
    .array(TrackContributorSchema)
    .min(1, "At least one contributor is required"),
});

const UpdateTrackSchema = CreateTrackSchema.partial().extend({
  id: z.string().uuid("Invalid track ID"),
});

const GetTrackSchema = z.object({
  id: z.string().uuid("Invalid track ID"),
});

const GetTracksSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  search: z.string().optional(),
  status: TrackStatusEnum.optional(),
  artistId: z.string().uuid().optional(),
  contributorId: z.string().uuid().optional(),
});

const DeleteTrackSchema = z.object({
  id: z.string().uuid("Invalid track ID"),
});

const UpdateTrackStatusSchema = z.object({
  id: z.string().uuid("Invalid track ID"),
  status: TrackStatusEnum,
});

export const trackRouter = router({
  create: protectedProcedure
    .input(CreateTrackSchema)
    .mutation(async ({ ctx, input }) => {
      const { session } = ctx;
      const { artists, contributors, trackFiles, status, ...trackData } = input;

      try {
        // Validate that user owns the artists
        const userArtists = await prisma.artist.findMany({
          where: {
            id: { in: artists.map((a) => a.artistId) },
            userId: session.user.id,
          },
          select: { id: true },
        });

        if (userArtists.length !== artists.length) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "You can only assign your own artists to tracks",
          });
        }

        // Validate that user owns the contributors
        const userContributors = await prisma.contributor.findMany({
          where: {
            id: { in: contributors.map((c) => c.contributorId) },
            userId: session.user.id,
          },
          select: { id: true },
        });

        if (userContributors.length !== contributors.length) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "You can only assign your own contributors to tracks",
          });
        }

        // Validate ISRC uniqueness if provided
        if (trackData.isrc) {
          const existingIsrc = await prisma.track.findFirst({
            where: { isrc: trackData.isrc },
          });

          if (existingIsrc) {
            throw new TRPCError({
              code: "CONFLICT",
              message: "ISRC already exists",
            });
          }
        }

        const track = await prisma.track.create({
          data: {
            ...trackData,
            userId: session.user.id,
            submittedAt: status === "READY" ? new Date() : null,
            readyAt: status === "READY" ? new Date() : null,
            trackFiles: {
              create: trackFiles,
            },
            artists: {
              create: artists,
            },
            contributors: {
              create: contributors,
            },
          },
          include: {
            trackFiles: true,
            artists: {
              include: {
                artist: true,
              },
            },
            contributors: {
              include: {
                contributor: true,
              },
            },
          },
        });

        return track;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create track",
          cause: error,
        });
      }
    }),

  update: protectedProcedure
    .input(UpdateTrackSchema)
    .mutation(async ({ ctx, input }) => {
      const { session } = ctx;
      const { id, artists, contributors, trackFiles, status, ...trackData } =
        input;

      try {
        // Check ownership
        const existingTrack = await prisma.track.findFirst({
          where: { id, userId: session.user.id },
        });

        if (!existingTrack) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message:
              "Track not found or you don't have permission to update it",
          });
        }

        // Validate ISRC uniqueness if provided and changed
        if (trackData.isrc && trackData.isrc !== existingTrack.isrc) {
          const existingIsrc = await prisma.track.findFirst({
            where: {
              isrc: trackData.isrc,
              id: { not: id },
            },
          });

          if (existingIsrc) {
            throw new TRPCError({
              code: "CONFLICT",
              message: "ISRC already exists",
            });
          }
        }

        const updateData: any = { ...trackData };

        // Update status timestamps
        if (status === "READY" && existingTrack.status === "DRAFT") {
          updateData.submittedAt = new Date();
          updateData.readyAt = new Date();
        }

        const track = await prisma.track.update({
          where: { id },
          data: updateData,
          include: {
            trackFiles: true,
            artists: {
              include: {
                artist: true,
              },
            },
            contributors: {
              include: {
                contributor: true,
              },
            },
          },
        });

        // Update relationships if provided
        if (artists) {
          await prisma.trackArtist.deleteMany({ where: { trackId: id } });
          await prisma.trackArtist.createMany({
            data: artists.map((artist) => ({
              trackId: id,
              ...artist,
            })),
          });
        }

        if (contributors) {
          await prisma.trackContributor.deleteMany({ where: { trackId: id } });
          await prisma.trackContributor.createMany({
            data: contributors.map((contributor) => ({
              trackId: id,
              ...contributor,
            })),
          });
        }

        if (trackFiles) {
          await prisma.trackFile.deleteMany({ where: { trackId: id } });
          await prisma.trackFile.createMany({
            data: trackFiles.map((file) => ({
              trackId: id,
              ...file,
            })),
          });
        }

        return track;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update track",
          cause: error,
        });
      }
    }),

  get: protectedProcedure
    .input(GetTrackSchema)
    .query(async ({ ctx, input }) => {
      const { session } = ctx;
      const { id } = input;

      try {
        const track = await prisma.track.findFirst({
          where: {
            id,
            userId: session.user.role === "admin" ? undefined : session.user.id,
          },
          include: {
            trackFiles: true,
            artists: {
              include: {
                artist: true,
              },
            },
            contributors: {
              include: {
                contributor: true,
              },
            },
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        });

        if (!track) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Track not found",
          });
        }

        return track;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to get track",
          cause: error,
        });
      }
    }),

  list: protectedProcedure
    .input(GetTracksSchema)
    .query(async ({ ctx, input }) => {
      const { session } = ctx;
      const { page, limit, search, status, artistId, contributorId } = input;

      try {
        const where: any = {
          userId: session.user.role === "admin" ? undefined : session.user.id,
        };

        if (search) {
          where.OR = [
            { title: { contains: search, mode: "insensitive" } },
            { isrc: { contains: search, mode: "insensitive" } },
            { genre: { contains: search, mode: "insensitive" } },
          ];
        }

        if (status) {
          where.status = status;
        }

        if (artistId) {
          where.artists = {
            some: { artistId },
          };
        }

        if (contributorId) {
          where.contributors = {
            some: { contributorId },
          };
        }

        const [tracks, total] = await Promise.all([
          prisma.track.findMany({
            where,
            include: {
              trackFiles: true,
              artists: {
                include: {
                  artist: true,
                },
              },
              contributors: {
                include: {
                  contributor: true,
                },
              },
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
            orderBy: { createdAt: "desc" },
            skip: (page - 1) * limit,
            take: limit,
          }),
          prisma.track.count({ where }),
        ]);

        return {
          tracks,
          pagination: {
            page,
            limit,
            total,
            pages: Math.ceil(total / limit),
          },
        };
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to get tracks",
          cause: error,
        });
      }
    }),

  updateStatus: protectedProcedure
    .input(UpdateTrackStatusSchema)
    .mutation(async ({ ctx, input }) => {
      const { session } = ctx;
      const { id, status } = input;

      try {
        // Check ownership or admin permission
        const track = await prisma.track.findFirst({
          where: {
            id,
            userId: session.user.role === "admin" ? undefined : session.user.id,
          },
        });

        if (!track) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message:
              "Track not found or you don't have permission to update it",
          });
        }

        const updateData: any = { status };

        if (status === "READY" && track.status === "DRAFT") {
          updateData.submittedAt = new Date();
          updateData.readyAt = new Date();
        }

        return await prisma.track.update({
          where: { id },
          data: updateData,
        });
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update track status",
          cause: error,
        });
      }
    }),

  delete: protectedProcedure
    .input(DeleteTrackSchema)
    .mutation(async ({ ctx, input }) => {
      const { session } = ctx;
      const { id } = input;

      try {
        // Check ownership
        const track = await prisma.track.findFirst({
          where: { id, userId: session.user.id },
        });

        if (!track) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message:
              "Track not found or you don't have permission to delete it",
          });
        }

        // Check if track is used in any releases
        const releaseCount = await prisma.releaseTrack.count({
          where: { trackId: id },
        });

        if (releaseCount > 0) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "Cannot delete track that is part of a release",
          });
        }

        // Delete all related data
        await prisma.$transaction([
          prisma.trackFile.deleteMany({ where: { trackId: id } }),
          prisma.trackArtist.deleteMany({ where: { trackId: id } }),
          prisma.trackContributor.deleteMany({ where: { trackId: id } }),
          prisma.track.delete({ where: { id } }),
        ]);

        return { success: true };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete track",
          cause: error,
        });
      }
    }),
});
