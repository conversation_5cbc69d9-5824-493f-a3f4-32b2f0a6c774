
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `TrackContributor` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model TrackContributor
 * 
 */
export type TrackContributorModel = runtime.Types.Result.DefaultSelection<Prisma.$TrackContributorPayload>

export type AggregateTrackContributor = {
  _count: TrackContributorCountAggregateOutputType | null
  _min: TrackContributorMinAggregateOutputType | null
  _max: TrackContributorMaxAggregateOutputType | null
}

export type TrackContributorMinAggregateOutputType = {
  trackId: string | null
  contributorId: string | null
  role: string | null
}

export type TrackContributorMaxAggregateOutputType = {
  trackId: string | null
  contributorId: string | null
  role: string | null
}

export type TrackContributorCountAggregateOutputType = {
  trackId: number
  contributorId: number
  role: number
  _all: number
}


export type TrackContributorMinAggregateInputType = {
  trackId?: true
  contributorId?: true
  role?: true
}

export type TrackContributorMaxAggregateInputType = {
  trackId?: true
  contributorId?: true
  role?: true
}

export type TrackContributorCountAggregateInputType = {
  trackId?: true
  contributorId?: true
  role?: true
  _all?: true
}

export type TrackContributorAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which TrackContributor to aggregate.
   */
  where?: Prisma.TrackContributorWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of TrackContributors to fetch.
   */
  orderBy?: Prisma.TrackContributorOrderByWithRelationInput | Prisma.TrackContributorOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.TrackContributorWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` TrackContributors from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` TrackContributors.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned TrackContributors
  **/
  _count?: true | TrackContributorCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: TrackContributorMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: TrackContributorMaxAggregateInputType
}

export type GetTrackContributorAggregateType<T extends TrackContributorAggregateArgs> = {
      [P in keyof T & keyof AggregateTrackContributor]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateTrackContributor[P]>
    : Prisma.GetScalarType<T[P], AggregateTrackContributor[P]>
}




export type TrackContributorGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.TrackContributorWhereInput
  orderBy?: Prisma.TrackContributorOrderByWithAggregationInput | Prisma.TrackContributorOrderByWithAggregationInput[]
  by: Prisma.TrackContributorScalarFieldEnum[] | Prisma.TrackContributorScalarFieldEnum
  having?: Prisma.TrackContributorScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: TrackContributorCountAggregateInputType | true
  _min?: TrackContributorMinAggregateInputType
  _max?: TrackContributorMaxAggregateInputType
}

export type TrackContributorGroupByOutputType = {
  trackId: string
  contributorId: string
  role: string
  _count: TrackContributorCountAggregateOutputType | null
  _min: TrackContributorMinAggregateOutputType | null
  _max: TrackContributorMaxAggregateOutputType | null
}

type GetTrackContributorGroupByPayload<T extends TrackContributorGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<TrackContributorGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof TrackContributorGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], TrackContributorGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], TrackContributorGroupByOutputType[P]>
      }
    >
  > 



export type TrackContributorWhereInput = {
  AND?: Prisma.TrackContributorWhereInput | Prisma.TrackContributorWhereInput[]
  OR?: Prisma.TrackContributorWhereInput[]
  NOT?: Prisma.TrackContributorWhereInput | Prisma.TrackContributorWhereInput[]
  trackId?: Prisma.StringFilter<"TrackContributor"> | string
  contributorId?: Prisma.StringFilter<"TrackContributor"> | string
  role?: Prisma.StringFilter<"TrackContributor"> | string
  track?: Prisma.XOR<Prisma.TrackScalarRelationFilter, Prisma.TrackWhereInput>
  contributor?: Prisma.XOR<Prisma.ContributorScalarRelationFilter, Prisma.ContributorWhereInput>
}

export type TrackContributorOrderByWithRelationInput = {
  trackId?: Prisma.SortOrder
  contributorId?: Prisma.SortOrder
  role?: Prisma.SortOrder
  track?: Prisma.TrackOrderByWithRelationInput
  contributor?: Prisma.ContributorOrderByWithRelationInput
}

export type TrackContributorWhereUniqueInput = Prisma.AtLeast<{
  trackId_contributorId?: Prisma.TrackContributorTrackIdContributorIdCompoundUniqueInput
  AND?: Prisma.TrackContributorWhereInput | Prisma.TrackContributorWhereInput[]
  OR?: Prisma.TrackContributorWhereInput[]
  NOT?: Prisma.TrackContributorWhereInput | Prisma.TrackContributorWhereInput[]
  trackId?: Prisma.StringFilter<"TrackContributor"> | string
  contributorId?: Prisma.StringFilter<"TrackContributor"> | string
  role?: Prisma.StringFilter<"TrackContributor"> | string
  track?: Prisma.XOR<Prisma.TrackScalarRelationFilter, Prisma.TrackWhereInput>
  contributor?: Prisma.XOR<Prisma.ContributorScalarRelationFilter, Prisma.ContributorWhereInput>
}, "trackId_contributorId">

export type TrackContributorOrderByWithAggregationInput = {
  trackId?: Prisma.SortOrder
  contributorId?: Prisma.SortOrder
  role?: Prisma.SortOrder
  _count?: Prisma.TrackContributorCountOrderByAggregateInput
  _max?: Prisma.TrackContributorMaxOrderByAggregateInput
  _min?: Prisma.TrackContributorMinOrderByAggregateInput
}

export type TrackContributorScalarWhereWithAggregatesInput = {
  AND?: Prisma.TrackContributorScalarWhereWithAggregatesInput | Prisma.TrackContributorScalarWhereWithAggregatesInput[]
  OR?: Prisma.TrackContributorScalarWhereWithAggregatesInput[]
  NOT?: Prisma.TrackContributorScalarWhereWithAggregatesInput | Prisma.TrackContributorScalarWhereWithAggregatesInput[]
  trackId?: Prisma.StringWithAggregatesFilter<"TrackContributor"> | string
  contributorId?: Prisma.StringWithAggregatesFilter<"TrackContributor"> | string
  role?: Prisma.StringWithAggregatesFilter<"TrackContributor"> | string
}

export type TrackContributorCreateInput = {
  role: string
  track: Prisma.TrackCreateNestedOneWithoutContributorsInput
  contributor: Prisma.ContributorCreateNestedOneWithoutTracksInput
}

export type TrackContributorUncheckedCreateInput = {
  trackId: string
  contributorId: string
  role: string
}

export type TrackContributorUpdateInput = {
  role?: Prisma.StringFieldUpdateOperationsInput | string
  track?: Prisma.TrackUpdateOneRequiredWithoutContributorsNestedInput
  contributor?: Prisma.ContributorUpdateOneRequiredWithoutTracksNestedInput
}

export type TrackContributorUncheckedUpdateInput = {
  trackId?: Prisma.StringFieldUpdateOperationsInput | string
  contributorId?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
}

export type TrackContributorCreateManyInput = {
  trackId: string
  contributorId: string
  role: string
}

export type TrackContributorUpdateManyMutationInput = {
  role?: Prisma.StringFieldUpdateOperationsInput | string
}

export type TrackContributorUncheckedUpdateManyInput = {
  trackId?: Prisma.StringFieldUpdateOperationsInput | string
  contributorId?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
}

export type TrackContributorListRelationFilter = {
  every?: Prisma.TrackContributorWhereInput
  some?: Prisma.TrackContributorWhereInput
  none?: Prisma.TrackContributorWhereInput
}

export type TrackContributorOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type TrackContributorTrackIdContributorIdCompoundUniqueInput = {
  trackId: string
  contributorId: string
}

export type TrackContributorCountOrderByAggregateInput = {
  trackId?: Prisma.SortOrder
  contributorId?: Prisma.SortOrder
  role?: Prisma.SortOrder
}

export type TrackContributorMaxOrderByAggregateInput = {
  trackId?: Prisma.SortOrder
  contributorId?: Prisma.SortOrder
  role?: Prisma.SortOrder
}

export type TrackContributorMinOrderByAggregateInput = {
  trackId?: Prisma.SortOrder
  contributorId?: Prisma.SortOrder
  role?: Prisma.SortOrder
}

export type TrackContributorCreateNestedManyWithoutContributorInput = {
  create?: Prisma.XOR<Prisma.TrackContributorCreateWithoutContributorInput, Prisma.TrackContributorUncheckedCreateWithoutContributorInput> | Prisma.TrackContributorCreateWithoutContributorInput[] | Prisma.TrackContributorUncheckedCreateWithoutContributorInput[]
  connectOrCreate?: Prisma.TrackContributorCreateOrConnectWithoutContributorInput | Prisma.TrackContributorCreateOrConnectWithoutContributorInput[]
  createMany?: Prisma.TrackContributorCreateManyContributorInputEnvelope
  connect?: Prisma.TrackContributorWhereUniqueInput | Prisma.TrackContributorWhereUniqueInput[]
}

export type TrackContributorUncheckedCreateNestedManyWithoutContributorInput = {
  create?: Prisma.XOR<Prisma.TrackContributorCreateWithoutContributorInput, Prisma.TrackContributorUncheckedCreateWithoutContributorInput> | Prisma.TrackContributorCreateWithoutContributorInput[] | Prisma.TrackContributorUncheckedCreateWithoutContributorInput[]
  connectOrCreate?: Prisma.TrackContributorCreateOrConnectWithoutContributorInput | Prisma.TrackContributorCreateOrConnectWithoutContributorInput[]
  createMany?: Prisma.TrackContributorCreateManyContributorInputEnvelope
  connect?: Prisma.TrackContributorWhereUniqueInput | Prisma.TrackContributorWhereUniqueInput[]
}

export type TrackContributorUpdateManyWithoutContributorNestedInput = {
  create?: Prisma.XOR<Prisma.TrackContributorCreateWithoutContributorInput, Prisma.TrackContributorUncheckedCreateWithoutContributorInput> | Prisma.TrackContributorCreateWithoutContributorInput[] | Prisma.TrackContributorUncheckedCreateWithoutContributorInput[]
  connectOrCreate?: Prisma.TrackContributorCreateOrConnectWithoutContributorInput | Prisma.TrackContributorCreateOrConnectWithoutContributorInput[]
  upsert?: Prisma.TrackContributorUpsertWithWhereUniqueWithoutContributorInput | Prisma.TrackContributorUpsertWithWhereUniqueWithoutContributorInput[]
  createMany?: Prisma.TrackContributorCreateManyContributorInputEnvelope
  set?: Prisma.TrackContributorWhereUniqueInput | Prisma.TrackContributorWhereUniqueInput[]
  disconnect?: Prisma.TrackContributorWhereUniqueInput | Prisma.TrackContributorWhereUniqueInput[]
  delete?: Prisma.TrackContributorWhereUniqueInput | Prisma.TrackContributorWhereUniqueInput[]
  connect?: Prisma.TrackContributorWhereUniqueInput | Prisma.TrackContributorWhereUniqueInput[]
  update?: Prisma.TrackContributorUpdateWithWhereUniqueWithoutContributorInput | Prisma.TrackContributorUpdateWithWhereUniqueWithoutContributorInput[]
  updateMany?: Prisma.TrackContributorUpdateManyWithWhereWithoutContributorInput | Prisma.TrackContributorUpdateManyWithWhereWithoutContributorInput[]
  deleteMany?: Prisma.TrackContributorScalarWhereInput | Prisma.TrackContributorScalarWhereInput[]
}

export type TrackContributorUncheckedUpdateManyWithoutContributorNestedInput = {
  create?: Prisma.XOR<Prisma.TrackContributorCreateWithoutContributorInput, Prisma.TrackContributorUncheckedCreateWithoutContributorInput> | Prisma.TrackContributorCreateWithoutContributorInput[] | Prisma.TrackContributorUncheckedCreateWithoutContributorInput[]
  connectOrCreate?: Prisma.TrackContributorCreateOrConnectWithoutContributorInput | Prisma.TrackContributorCreateOrConnectWithoutContributorInput[]
  upsert?: Prisma.TrackContributorUpsertWithWhereUniqueWithoutContributorInput | Prisma.TrackContributorUpsertWithWhereUniqueWithoutContributorInput[]
  createMany?: Prisma.TrackContributorCreateManyContributorInputEnvelope
  set?: Prisma.TrackContributorWhereUniqueInput | Prisma.TrackContributorWhereUniqueInput[]
  disconnect?: Prisma.TrackContributorWhereUniqueInput | Prisma.TrackContributorWhereUniqueInput[]
  delete?: Prisma.TrackContributorWhereUniqueInput | Prisma.TrackContributorWhereUniqueInput[]
  connect?: Prisma.TrackContributorWhereUniqueInput | Prisma.TrackContributorWhereUniqueInput[]
  update?: Prisma.TrackContributorUpdateWithWhereUniqueWithoutContributorInput | Prisma.TrackContributorUpdateWithWhereUniqueWithoutContributorInput[]
  updateMany?: Prisma.TrackContributorUpdateManyWithWhereWithoutContributorInput | Prisma.TrackContributorUpdateManyWithWhereWithoutContributorInput[]
  deleteMany?: Prisma.TrackContributorScalarWhereInput | Prisma.TrackContributorScalarWhereInput[]
}

export type TrackContributorCreateNestedManyWithoutTrackInput = {
  create?: Prisma.XOR<Prisma.TrackContributorCreateWithoutTrackInput, Prisma.TrackContributorUncheckedCreateWithoutTrackInput> | Prisma.TrackContributorCreateWithoutTrackInput[] | Prisma.TrackContributorUncheckedCreateWithoutTrackInput[]
  connectOrCreate?: Prisma.TrackContributorCreateOrConnectWithoutTrackInput | Prisma.TrackContributorCreateOrConnectWithoutTrackInput[]
  createMany?: Prisma.TrackContributorCreateManyTrackInputEnvelope
  connect?: Prisma.TrackContributorWhereUniqueInput | Prisma.TrackContributorWhereUniqueInput[]
}

export type TrackContributorUncheckedCreateNestedManyWithoutTrackInput = {
  create?: Prisma.XOR<Prisma.TrackContributorCreateWithoutTrackInput, Prisma.TrackContributorUncheckedCreateWithoutTrackInput> | Prisma.TrackContributorCreateWithoutTrackInput[] | Prisma.TrackContributorUncheckedCreateWithoutTrackInput[]
  connectOrCreate?: Prisma.TrackContributorCreateOrConnectWithoutTrackInput | Prisma.TrackContributorCreateOrConnectWithoutTrackInput[]
  createMany?: Prisma.TrackContributorCreateManyTrackInputEnvelope
  connect?: Prisma.TrackContributorWhereUniqueInput | Prisma.TrackContributorWhereUniqueInput[]
}

export type TrackContributorUpdateManyWithoutTrackNestedInput = {
  create?: Prisma.XOR<Prisma.TrackContributorCreateWithoutTrackInput, Prisma.TrackContributorUncheckedCreateWithoutTrackInput> | Prisma.TrackContributorCreateWithoutTrackInput[] | Prisma.TrackContributorUncheckedCreateWithoutTrackInput[]
  connectOrCreate?: Prisma.TrackContributorCreateOrConnectWithoutTrackInput | Prisma.TrackContributorCreateOrConnectWithoutTrackInput[]
  upsert?: Prisma.TrackContributorUpsertWithWhereUniqueWithoutTrackInput | Prisma.TrackContributorUpsertWithWhereUniqueWithoutTrackInput[]
  createMany?: Prisma.TrackContributorCreateManyTrackInputEnvelope
  set?: Prisma.TrackContributorWhereUniqueInput | Prisma.TrackContributorWhereUniqueInput[]
  disconnect?: Prisma.TrackContributorWhereUniqueInput | Prisma.TrackContributorWhereUniqueInput[]
  delete?: Prisma.TrackContributorWhereUniqueInput | Prisma.TrackContributorWhereUniqueInput[]
  connect?: Prisma.TrackContributorWhereUniqueInput | Prisma.TrackContributorWhereUniqueInput[]
  update?: Prisma.TrackContributorUpdateWithWhereUniqueWithoutTrackInput | Prisma.TrackContributorUpdateWithWhereUniqueWithoutTrackInput[]
  updateMany?: Prisma.TrackContributorUpdateManyWithWhereWithoutTrackInput | Prisma.TrackContributorUpdateManyWithWhereWithoutTrackInput[]
  deleteMany?: Prisma.TrackContributorScalarWhereInput | Prisma.TrackContributorScalarWhereInput[]
}

export type TrackContributorUncheckedUpdateManyWithoutTrackNestedInput = {
  create?: Prisma.XOR<Prisma.TrackContributorCreateWithoutTrackInput, Prisma.TrackContributorUncheckedCreateWithoutTrackInput> | Prisma.TrackContributorCreateWithoutTrackInput[] | Prisma.TrackContributorUncheckedCreateWithoutTrackInput[]
  connectOrCreate?: Prisma.TrackContributorCreateOrConnectWithoutTrackInput | Prisma.TrackContributorCreateOrConnectWithoutTrackInput[]
  upsert?: Prisma.TrackContributorUpsertWithWhereUniqueWithoutTrackInput | Prisma.TrackContributorUpsertWithWhereUniqueWithoutTrackInput[]
  createMany?: Prisma.TrackContributorCreateManyTrackInputEnvelope
  set?: Prisma.TrackContributorWhereUniqueInput | Prisma.TrackContributorWhereUniqueInput[]
  disconnect?: Prisma.TrackContributorWhereUniqueInput | Prisma.TrackContributorWhereUniqueInput[]
  delete?: Prisma.TrackContributorWhereUniqueInput | Prisma.TrackContributorWhereUniqueInput[]
  connect?: Prisma.TrackContributorWhereUniqueInput | Prisma.TrackContributorWhereUniqueInput[]
  update?: Prisma.TrackContributorUpdateWithWhereUniqueWithoutTrackInput | Prisma.TrackContributorUpdateWithWhereUniqueWithoutTrackInput[]
  updateMany?: Prisma.TrackContributorUpdateManyWithWhereWithoutTrackInput | Prisma.TrackContributorUpdateManyWithWhereWithoutTrackInput[]
  deleteMany?: Prisma.TrackContributorScalarWhereInput | Prisma.TrackContributorScalarWhereInput[]
}

export type TrackContributorCreateWithoutContributorInput = {
  role: string
  track: Prisma.TrackCreateNestedOneWithoutContributorsInput
}

export type TrackContributorUncheckedCreateWithoutContributorInput = {
  trackId: string
  role: string
}

export type TrackContributorCreateOrConnectWithoutContributorInput = {
  where: Prisma.TrackContributorWhereUniqueInput
  create: Prisma.XOR<Prisma.TrackContributorCreateWithoutContributorInput, Prisma.TrackContributorUncheckedCreateWithoutContributorInput>
}

export type TrackContributorCreateManyContributorInputEnvelope = {
  data: Prisma.TrackContributorCreateManyContributorInput | Prisma.TrackContributorCreateManyContributorInput[]
  skipDuplicates?: boolean
}

export type TrackContributorUpsertWithWhereUniqueWithoutContributorInput = {
  where: Prisma.TrackContributorWhereUniqueInput
  update: Prisma.XOR<Prisma.TrackContributorUpdateWithoutContributorInput, Prisma.TrackContributorUncheckedUpdateWithoutContributorInput>
  create: Prisma.XOR<Prisma.TrackContributorCreateWithoutContributorInput, Prisma.TrackContributorUncheckedCreateWithoutContributorInput>
}

export type TrackContributorUpdateWithWhereUniqueWithoutContributorInput = {
  where: Prisma.TrackContributorWhereUniqueInput
  data: Prisma.XOR<Prisma.TrackContributorUpdateWithoutContributorInput, Prisma.TrackContributorUncheckedUpdateWithoutContributorInput>
}

export type TrackContributorUpdateManyWithWhereWithoutContributorInput = {
  where: Prisma.TrackContributorScalarWhereInput
  data: Prisma.XOR<Prisma.TrackContributorUpdateManyMutationInput, Prisma.TrackContributorUncheckedUpdateManyWithoutContributorInput>
}

export type TrackContributorScalarWhereInput = {
  AND?: Prisma.TrackContributorScalarWhereInput | Prisma.TrackContributorScalarWhereInput[]
  OR?: Prisma.TrackContributorScalarWhereInput[]
  NOT?: Prisma.TrackContributorScalarWhereInput | Prisma.TrackContributorScalarWhereInput[]
  trackId?: Prisma.StringFilter<"TrackContributor"> | string
  contributorId?: Prisma.StringFilter<"TrackContributor"> | string
  role?: Prisma.StringFilter<"TrackContributor"> | string
}

export type TrackContributorCreateWithoutTrackInput = {
  role: string
  contributor: Prisma.ContributorCreateNestedOneWithoutTracksInput
}

export type TrackContributorUncheckedCreateWithoutTrackInput = {
  contributorId: string
  role: string
}

export type TrackContributorCreateOrConnectWithoutTrackInput = {
  where: Prisma.TrackContributorWhereUniqueInput
  create: Prisma.XOR<Prisma.TrackContributorCreateWithoutTrackInput, Prisma.TrackContributorUncheckedCreateWithoutTrackInput>
}

export type TrackContributorCreateManyTrackInputEnvelope = {
  data: Prisma.TrackContributorCreateManyTrackInput | Prisma.TrackContributorCreateManyTrackInput[]
  skipDuplicates?: boolean
}

export type TrackContributorUpsertWithWhereUniqueWithoutTrackInput = {
  where: Prisma.TrackContributorWhereUniqueInput
  update: Prisma.XOR<Prisma.TrackContributorUpdateWithoutTrackInput, Prisma.TrackContributorUncheckedUpdateWithoutTrackInput>
  create: Prisma.XOR<Prisma.TrackContributorCreateWithoutTrackInput, Prisma.TrackContributorUncheckedCreateWithoutTrackInput>
}

export type TrackContributorUpdateWithWhereUniqueWithoutTrackInput = {
  where: Prisma.TrackContributorWhereUniqueInput
  data: Prisma.XOR<Prisma.TrackContributorUpdateWithoutTrackInput, Prisma.TrackContributorUncheckedUpdateWithoutTrackInput>
}

export type TrackContributorUpdateManyWithWhereWithoutTrackInput = {
  where: Prisma.TrackContributorScalarWhereInput
  data: Prisma.XOR<Prisma.TrackContributorUpdateManyMutationInput, Prisma.TrackContributorUncheckedUpdateManyWithoutTrackInput>
}

export type TrackContributorCreateManyContributorInput = {
  trackId: string
  role: string
}

export type TrackContributorUpdateWithoutContributorInput = {
  role?: Prisma.StringFieldUpdateOperationsInput | string
  track?: Prisma.TrackUpdateOneRequiredWithoutContributorsNestedInput
}

export type TrackContributorUncheckedUpdateWithoutContributorInput = {
  trackId?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
}

export type TrackContributorUncheckedUpdateManyWithoutContributorInput = {
  trackId?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
}

export type TrackContributorCreateManyTrackInput = {
  contributorId: string
  role: string
}

export type TrackContributorUpdateWithoutTrackInput = {
  role?: Prisma.StringFieldUpdateOperationsInput | string
  contributor?: Prisma.ContributorUpdateOneRequiredWithoutTracksNestedInput
}

export type TrackContributorUncheckedUpdateWithoutTrackInput = {
  contributorId?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
}

export type TrackContributorUncheckedUpdateManyWithoutTrackInput = {
  contributorId?: Prisma.StringFieldUpdateOperationsInput | string
  role?: Prisma.StringFieldUpdateOperationsInput | string
}



export type TrackContributorSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  trackId?: boolean
  contributorId?: boolean
  role?: boolean
  track?: boolean | Prisma.TrackDefaultArgs<ExtArgs>
  contributor?: boolean | Prisma.ContributorDefaultArgs<ExtArgs>
}, ExtArgs["result"]["trackContributor"]>

export type TrackContributorSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  trackId?: boolean
  contributorId?: boolean
  role?: boolean
  track?: boolean | Prisma.TrackDefaultArgs<ExtArgs>
  contributor?: boolean | Prisma.ContributorDefaultArgs<ExtArgs>
}, ExtArgs["result"]["trackContributor"]>

export type TrackContributorSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  trackId?: boolean
  contributorId?: boolean
  role?: boolean
  track?: boolean | Prisma.TrackDefaultArgs<ExtArgs>
  contributor?: boolean | Prisma.ContributorDefaultArgs<ExtArgs>
}, ExtArgs["result"]["trackContributor"]>

export type TrackContributorSelectScalar = {
  trackId?: boolean
  contributorId?: boolean
  role?: boolean
}

export type TrackContributorOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"trackId" | "contributorId" | "role", ExtArgs["result"]["trackContributor"]>
export type TrackContributorInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  track?: boolean | Prisma.TrackDefaultArgs<ExtArgs>
  contributor?: boolean | Prisma.ContributorDefaultArgs<ExtArgs>
}
export type TrackContributorIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  track?: boolean | Prisma.TrackDefaultArgs<ExtArgs>
  contributor?: boolean | Prisma.ContributorDefaultArgs<ExtArgs>
}
export type TrackContributorIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  track?: boolean | Prisma.TrackDefaultArgs<ExtArgs>
  contributor?: boolean | Prisma.ContributorDefaultArgs<ExtArgs>
}

export type $TrackContributorPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "TrackContributor"
  objects: {
    track: Prisma.$TrackPayload<ExtArgs>
    contributor: Prisma.$ContributorPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    trackId: string
    contributorId: string
    role: string
  }, ExtArgs["result"]["trackContributor"]>
  composites: {}
}

export type TrackContributorGetPayload<S extends boolean | null | undefined | TrackContributorDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$TrackContributorPayload, S>

export type TrackContributorCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<TrackContributorFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: TrackContributorCountAggregateInputType | true
  }

export interface TrackContributorDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['TrackContributor'], meta: { name: 'TrackContributor' } }
  /**
   * Find zero or one TrackContributor that matches the filter.
   * @param {TrackContributorFindUniqueArgs} args - Arguments to find a TrackContributor
   * @example
   * // Get one TrackContributor
   * const trackContributor = await prisma.trackContributor.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends TrackContributorFindUniqueArgs>(args: Prisma.SelectSubset<T, TrackContributorFindUniqueArgs<ExtArgs>>): Prisma.Prisma__TrackContributorClient<runtime.Types.Result.GetResult<Prisma.$TrackContributorPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one TrackContributor that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {TrackContributorFindUniqueOrThrowArgs} args - Arguments to find a TrackContributor
   * @example
   * // Get one TrackContributor
   * const trackContributor = await prisma.trackContributor.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends TrackContributorFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, TrackContributorFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__TrackContributorClient<runtime.Types.Result.GetResult<Prisma.$TrackContributorPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first TrackContributor that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackContributorFindFirstArgs} args - Arguments to find a TrackContributor
   * @example
   * // Get one TrackContributor
   * const trackContributor = await prisma.trackContributor.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends TrackContributorFindFirstArgs>(args?: Prisma.SelectSubset<T, TrackContributorFindFirstArgs<ExtArgs>>): Prisma.Prisma__TrackContributorClient<runtime.Types.Result.GetResult<Prisma.$TrackContributorPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first TrackContributor that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackContributorFindFirstOrThrowArgs} args - Arguments to find a TrackContributor
   * @example
   * // Get one TrackContributor
   * const trackContributor = await prisma.trackContributor.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends TrackContributorFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, TrackContributorFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__TrackContributorClient<runtime.Types.Result.GetResult<Prisma.$TrackContributorPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more TrackContributors that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackContributorFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all TrackContributors
   * const trackContributors = await prisma.trackContributor.findMany()
   * 
   * // Get first 10 TrackContributors
   * const trackContributors = await prisma.trackContributor.findMany({ take: 10 })
   * 
   * // Only select the `trackId`
   * const trackContributorWithTrackIdOnly = await prisma.trackContributor.findMany({ select: { trackId: true } })
   * 
   */
  findMany<T extends TrackContributorFindManyArgs>(args?: Prisma.SelectSubset<T, TrackContributorFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TrackContributorPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a TrackContributor.
   * @param {TrackContributorCreateArgs} args - Arguments to create a TrackContributor.
   * @example
   * // Create one TrackContributor
   * const TrackContributor = await prisma.trackContributor.create({
   *   data: {
   *     // ... data to create a TrackContributor
   *   }
   * })
   * 
   */
  create<T extends TrackContributorCreateArgs>(args: Prisma.SelectSubset<T, TrackContributorCreateArgs<ExtArgs>>): Prisma.Prisma__TrackContributorClient<runtime.Types.Result.GetResult<Prisma.$TrackContributorPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many TrackContributors.
   * @param {TrackContributorCreateManyArgs} args - Arguments to create many TrackContributors.
   * @example
   * // Create many TrackContributors
   * const trackContributor = await prisma.trackContributor.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends TrackContributorCreateManyArgs>(args?: Prisma.SelectSubset<T, TrackContributorCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many TrackContributors and returns the data saved in the database.
   * @param {TrackContributorCreateManyAndReturnArgs} args - Arguments to create many TrackContributors.
   * @example
   * // Create many TrackContributors
   * const trackContributor = await prisma.trackContributor.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many TrackContributors and only return the `trackId`
   * const trackContributorWithTrackIdOnly = await prisma.trackContributor.createManyAndReturn({
   *   select: { trackId: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends TrackContributorCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, TrackContributorCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TrackContributorPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a TrackContributor.
   * @param {TrackContributorDeleteArgs} args - Arguments to delete one TrackContributor.
   * @example
   * // Delete one TrackContributor
   * const TrackContributor = await prisma.trackContributor.delete({
   *   where: {
   *     // ... filter to delete one TrackContributor
   *   }
   * })
   * 
   */
  delete<T extends TrackContributorDeleteArgs>(args: Prisma.SelectSubset<T, TrackContributorDeleteArgs<ExtArgs>>): Prisma.Prisma__TrackContributorClient<runtime.Types.Result.GetResult<Prisma.$TrackContributorPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one TrackContributor.
   * @param {TrackContributorUpdateArgs} args - Arguments to update one TrackContributor.
   * @example
   * // Update one TrackContributor
   * const trackContributor = await prisma.trackContributor.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends TrackContributorUpdateArgs>(args: Prisma.SelectSubset<T, TrackContributorUpdateArgs<ExtArgs>>): Prisma.Prisma__TrackContributorClient<runtime.Types.Result.GetResult<Prisma.$TrackContributorPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more TrackContributors.
   * @param {TrackContributorDeleteManyArgs} args - Arguments to filter TrackContributors to delete.
   * @example
   * // Delete a few TrackContributors
   * const { count } = await prisma.trackContributor.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends TrackContributorDeleteManyArgs>(args?: Prisma.SelectSubset<T, TrackContributorDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more TrackContributors.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackContributorUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many TrackContributors
   * const trackContributor = await prisma.trackContributor.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends TrackContributorUpdateManyArgs>(args: Prisma.SelectSubset<T, TrackContributorUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more TrackContributors and returns the data updated in the database.
   * @param {TrackContributorUpdateManyAndReturnArgs} args - Arguments to update many TrackContributors.
   * @example
   * // Update many TrackContributors
   * const trackContributor = await prisma.trackContributor.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more TrackContributors and only return the `trackId`
   * const trackContributorWithTrackIdOnly = await prisma.trackContributor.updateManyAndReturn({
   *   select: { trackId: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends TrackContributorUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, TrackContributorUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$TrackContributorPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one TrackContributor.
   * @param {TrackContributorUpsertArgs} args - Arguments to update or create a TrackContributor.
   * @example
   * // Update or create a TrackContributor
   * const trackContributor = await prisma.trackContributor.upsert({
   *   create: {
   *     // ... data to create a TrackContributor
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the TrackContributor we want to update
   *   }
   * })
   */
  upsert<T extends TrackContributorUpsertArgs>(args: Prisma.SelectSubset<T, TrackContributorUpsertArgs<ExtArgs>>): Prisma.Prisma__TrackContributorClient<runtime.Types.Result.GetResult<Prisma.$TrackContributorPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of TrackContributors.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackContributorCountArgs} args - Arguments to filter TrackContributors to count.
   * @example
   * // Count the number of TrackContributors
   * const count = await prisma.trackContributor.count({
   *   where: {
   *     // ... the filter for the TrackContributors we want to count
   *   }
   * })
  **/
  count<T extends TrackContributorCountArgs>(
    args?: Prisma.Subset<T, TrackContributorCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], TrackContributorCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a TrackContributor.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackContributorAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends TrackContributorAggregateArgs>(args: Prisma.Subset<T, TrackContributorAggregateArgs>): Prisma.PrismaPromise<GetTrackContributorAggregateType<T>>

  /**
   * Group by TrackContributor.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {TrackContributorGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends TrackContributorGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: TrackContributorGroupByArgs['orderBy'] }
      : { orderBy?: TrackContributorGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, TrackContributorGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetTrackContributorGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the TrackContributor model
 */
readonly fields: TrackContributorFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for TrackContributor.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__TrackContributorClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  track<T extends Prisma.TrackDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.TrackDefaultArgs<ExtArgs>>): Prisma.Prisma__TrackClient<runtime.Types.Result.GetResult<Prisma.$TrackPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  contributor<T extends Prisma.ContributorDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.ContributorDefaultArgs<ExtArgs>>): Prisma.Prisma__ContributorClient<runtime.Types.Result.GetResult<Prisma.$ContributorPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the TrackContributor model
 */
export interface TrackContributorFieldRefs {
  readonly trackId: Prisma.FieldRef<"TrackContributor", 'String'>
  readonly contributorId: Prisma.FieldRef<"TrackContributor", 'String'>
  readonly role: Prisma.FieldRef<"TrackContributor", 'String'>
}
    

// Custom InputTypes
/**
 * TrackContributor findUnique
 */
export type TrackContributorFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackContributor
   */
  select?: Prisma.TrackContributorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackContributor
   */
  omit?: Prisma.TrackContributorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackContributorInclude<ExtArgs> | null
  /**
   * Filter, which TrackContributor to fetch.
   */
  where: Prisma.TrackContributorWhereUniqueInput
}

/**
 * TrackContributor findUniqueOrThrow
 */
export type TrackContributorFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackContributor
   */
  select?: Prisma.TrackContributorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackContributor
   */
  omit?: Prisma.TrackContributorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackContributorInclude<ExtArgs> | null
  /**
   * Filter, which TrackContributor to fetch.
   */
  where: Prisma.TrackContributorWhereUniqueInput
}

/**
 * TrackContributor findFirst
 */
export type TrackContributorFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackContributor
   */
  select?: Prisma.TrackContributorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackContributor
   */
  omit?: Prisma.TrackContributorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackContributorInclude<ExtArgs> | null
  /**
   * Filter, which TrackContributor to fetch.
   */
  where?: Prisma.TrackContributorWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of TrackContributors to fetch.
   */
  orderBy?: Prisma.TrackContributorOrderByWithRelationInput | Prisma.TrackContributorOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for TrackContributors.
   */
  cursor?: Prisma.TrackContributorWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` TrackContributors from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` TrackContributors.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of TrackContributors.
   */
  distinct?: Prisma.TrackContributorScalarFieldEnum | Prisma.TrackContributorScalarFieldEnum[]
}

/**
 * TrackContributor findFirstOrThrow
 */
export type TrackContributorFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackContributor
   */
  select?: Prisma.TrackContributorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackContributor
   */
  omit?: Prisma.TrackContributorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackContributorInclude<ExtArgs> | null
  /**
   * Filter, which TrackContributor to fetch.
   */
  where?: Prisma.TrackContributorWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of TrackContributors to fetch.
   */
  orderBy?: Prisma.TrackContributorOrderByWithRelationInput | Prisma.TrackContributorOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for TrackContributors.
   */
  cursor?: Prisma.TrackContributorWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` TrackContributors from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` TrackContributors.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of TrackContributors.
   */
  distinct?: Prisma.TrackContributorScalarFieldEnum | Prisma.TrackContributorScalarFieldEnum[]
}

/**
 * TrackContributor findMany
 */
export type TrackContributorFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackContributor
   */
  select?: Prisma.TrackContributorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackContributor
   */
  omit?: Prisma.TrackContributorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackContributorInclude<ExtArgs> | null
  /**
   * Filter, which TrackContributors to fetch.
   */
  where?: Prisma.TrackContributorWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of TrackContributors to fetch.
   */
  orderBy?: Prisma.TrackContributorOrderByWithRelationInput | Prisma.TrackContributorOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing TrackContributors.
   */
  cursor?: Prisma.TrackContributorWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` TrackContributors from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` TrackContributors.
   */
  skip?: number
  distinct?: Prisma.TrackContributorScalarFieldEnum | Prisma.TrackContributorScalarFieldEnum[]
}

/**
 * TrackContributor create
 */
export type TrackContributorCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackContributor
   */
  select?: Prisma.TrackContributorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackContributor
   */
  omit?: Prisma.TrackContributorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackContributorInclude<ExtArgs> | null
  /**
   * The data needed to create a TrackContributor.
   */
  data: Prisma.XOR<Prisma.TrackContributorCreateInput, Prisma.TrackContributorUncheckedCreateInput>
}

/**
 * TrackContributor createMany
 */
export type TrackContributorCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many TrackContributors.
   */
  data: Prisma.TrackContributorCreateManyInput | Prisma.TrackContributorCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * TrackContributor createManyAndReturn
 */
export type TrackContributorCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackContributor
   */
  select?: Prisma.TrackContributorSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the TrackContributor
   */
  omit?: Prisma.TrackContributorOmit<ExtArgs> | null
  /**
   * The data used to create many TrackContributors.
   */
  data: Prisma.TrackContributorCreateManyInput | Prisma.TrackContributorCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackContributorIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * TrackContributor update
 */
export type TrackContributorUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackContributor
   */
  select?: Prisma.TrackContributorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackContributor
   */
  omit?: Prisma.TrackContributorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackContributorInclude<ExtArgs> | null
  /**
   * The data needed to update a TrackContributor.
   */
  data: Prisma.XOR<Prisma.TrackContributorUpdateInput, Prisma.TrackContributorUncheckedUpdateInput>
  /**
   * Choose, which TrackContributor to update.
   */
  where: Prisma.TrackContributorWhereUniqueInput
}

/**
 * TrackContributor updateMany
 */
export type TrackContributorUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update TrackContributors.
   */
  data: Prisma.XOR<Prisma.TrackContributorUpdateManyMutationInput, Prisma.TrackContributorUncheckedUpdateManyInput>
  /**
   * Filter which TrackContributors to update
   */
  where?: Prisma.TrackContributorWhereInput
  /**
   * Limit how many TrackContributors to update.
   */
  limit?: number
}

/**
 * TrackContributor updateManyAndReturn
 */
export type TrackContributorUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackContributor
   */
  select?: Prisma.TrackContributorSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the TrackContributor
   */
  omit?: Prisma.TrackContributorOmit<ExtArgs> | null
  /**
   * The data used to update TrackContributors.
   */
  data: Prisma.XOR<Prisma.TrackContributorUpdateManyMutationInput, Prisma.TrackContributorUncheckedUpdateManyInput>
  /**
   * Filter which TrackContributors to update
   */
  where?: Prisma.TrackContributorWhereInput
  /**
   * Limit how many TrackContributors to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackContributorIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * TrackContributor upsert
 */
export type TrackContributorUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackContributor
   */
  select?: Prisma.TrackContributorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackContributor
   */
  omit?: Prisma.TrackContributorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackContributorInclude<ExtArgs> | null
  /**
   * The filter to search for the TrackContributor to update in case it exists.
   */
  where: Prisma.TrackContributorWhereUniqueInput
  /**
   * In case the TrackContributor found by the `where` argument doesn't exist, create a new TrackContributor with this data.
   */
  create: Prisma.XOR<Prisma.TrackContributorCreateInput, Prisma.TrackContributorUncheckedCreateInput>
  /**
   * In case the TrackContributor was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.TrackContributorUpdateInput, Prisma.TrackContributorUncheckedUpdateInput>
}

/**
 * TrackContributor delete
 */
export type TrackContributorDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackContributor
   */
  select?: Prisma.TrackContributorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackContributor
   */
  omit?: Prisma.TrackContributorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackContributorInclude<ExtArgs> | null
  /**
   * Filter which TrackContributor to delete.
   */
  where: Prisma.TrackContributorWhereUniqueInput
}

/**
 * TrackContributor deleteMany
 */
export type TrackContributorDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which TrackContributors to delete
   */
  where?: Prisma.TrackContributorWhereInput
  /**
   * Limit how many TrackContributors to delete.
   */
  limit?: number
}

/**
 * TrackContributor without action
 */
export type TrackContributorDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the TrackContributor
   */
  select?: Prisma.TrackContributorSelect<ExtArgs> | null
  /**
   * Omit specific fields from the TrackContributor
   */
  omit?: Prisma.TrackContributorOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.TrackContributorInclude<ExtArgs> | null
}
