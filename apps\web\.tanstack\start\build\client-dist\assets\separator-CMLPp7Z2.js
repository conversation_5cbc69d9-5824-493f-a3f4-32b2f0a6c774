import{r as c,j as e}from"./main-B9Fv5CdX.js";import{P as d}from"./label-CNQvdrLZ.js";import{a as v}from"./button-Ispz1G12.js";var m="Separator",n="horizontal",f=["horizontal","vertical"],s=c.forwardRef((r,t)=>{const{decorative:o,orientation:a=n,...l}=r,i=u(a)?a:n,p=o?{role:"none"}:{"aria-orientation":i==="vertical"?i:void 0,role:"separator"};return e.jsx(d.div,{"data-orientation":i,...p,...l,ref:t})});s.displayName=m;function u(r){return f.includes(r)}var h=s;function z({className:r,orientation:t="horizontal",decorative:o=!0,...a}){return e.jsx(h,{"data-slot":"separator",decorative:o,orientation:t,className:v("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",r),...a})}export{z as S};
