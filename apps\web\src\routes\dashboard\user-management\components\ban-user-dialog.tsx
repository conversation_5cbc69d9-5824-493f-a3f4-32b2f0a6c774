import { useState } from "react";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";

import { authClient } from "@/lib/auth-client";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import type { UserTableItem } from "./types";

interface BanUserDialogProps {
  children: React.ReactNode;
  user: UserTableItem;
  onUserBanned: () => void;
}

export function BanUserDialog({
  children,
  user,
  onUserBanned,
}: BanUserDialogProps) {
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    banReason: "",
    banDuration: "permanent" as "permanent" | "temporary",
    banDays: 7,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const banParams: {
        userId: string;
        banReason?: string;
        banExpiresIn?: number;
      } = {
        userId: user.id,
      };

      // Add ban reason if provided
      if (formData.banReason.trim()) {
        banParams.banReason = formData.banReason.trim();
      }

      // Add expiration if temporary ban
      if (formData.banDuration === "temporary") {
        banParams.banExpiresIn = formData.banDays * 24 * 60 * 60; // Convert days to seconds
      }

      await authClient.admin.banUser(banParams);

      toast.success(
        `Successfully banned ${user.name}${
          formData.banDuration === "temporary"
            ? ` for ${formData.banDays} day${formData.banDays > 1 ? "s" : ""}`
            : " permanently"
        }`
      );

      setOpen(false);
      setFormData({
        banReason: "",
        banDuration: "permanent",
        banDays: 7,
      });
      onUserBanned();
    } catch (error: any) {
      console.error("Failed to ban user:", error);
      toast.error("Failed to ban user: " + (error.message || "Unknown error"));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Ban User: {user.name}</DialogTitle>
          <DialogDescription>
            Prevent {user.email} from signing in. You can specify a reason and
            duration.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="banReason">Ban Reason</Label>
            <Input
              id="banReason"
              placeholder="Enter reason for ban (optional)"
              value={formData.banReason}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, banReason: e.target.value }))
              }
              disabled={isLoading}
            />
          </div>

          <div className="space-y-2">
            <Label>Ban Duration</Label>
            <Select
              value={formData.banDuration}
              onValueChange={(value: "permanent" | "temporary") =>
                setFormData((prev) => ({ ...prev, banDuration: value }))
              }
              disabled={isLoading}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="permanent">Permanent</SelectItem>
                <SelectItem value="temporary">Temporary</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {formData.banDuration === "temporary" && (
            <div className="space-y-2">
              <Label htmlFor="banDays">Duration (Days)</Label>
              <Input
                id="banDays"
                type="number"
                min="1"
                max="365"
                placeholder="Enter number of days"
                value={formData.banDays}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    banDays: parseInt(e.target.value) || 1,
                  }))
                }
                disabled={isLoading}
              />
            </div>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" variant="destructive" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Banning...
                </>
              ) : (
                "Ban User"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
