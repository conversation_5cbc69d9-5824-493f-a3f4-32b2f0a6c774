import { useState, useEffect } from "react";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";
import { useMutation } from "@tanstack/react-query";

import { useTRPCClient } from "@/utils/trpc";
import { authClient } from "@/lib/auth-client";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import type { LabelTableItem } from "@/routes/dashboard/label/components/label-table";

interface UpdateLabelDialogProps {
  children: React.ReactNode;
  label: LabelTableItem;
  onLabelUpdated: () => void;
}

export function UpdateLabelDialog({
  children,
  label,
  onLabelUpdated,
}: UpdateLabelDialogProps) {
  const [open, setOpen] = useState(false);
  const trpcClient = useTRPCClient();
  const { data: session } = authClient.useSession();
  const isAdmin = session?.user?.role === "admin";
  const [formData, setFormData] = useState({
    name: "",
  });

  // Initialize form data when dialog opens
  useEffect(() => {
    if (open) {
      setFormData({
        name: label.name || "",
      });
    }
  }, [open, label]);

  const updateLabelMutation = useMutation({
    mutationFn: async (data: { id: string; name: string }) => {
      return trpcClient.label.update.mutate(data);
    },
    onSuccess: () => {
      toast.success("Label updated successfully");
      setOpen(false);
      onLabelUpdated();
    },
    onError: (error: any) => {
      console.error("Failed to update label:", error);
      toast.error(
        "Failed to update label: " + (error.message || "Unknown error")
      );
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast.error("Label name is required");
      return;
    }

    try {
      await updateLabelMutation.mutateAsync({
        id: label.id,
        name: formData.name.trim(),
      });
    } catch (error) {
      // Error handling is done in the mutation
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Edit Label: {label.name}</DialogTitle>
          <DialogDescription>Update the label information.</DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Basic Information */}
          <div className="space-y-4">
            {isAdmin && (
              <div className="space-y-2">
                <Label htmlFor="createdBy">Created By</Label>
                <Input
                  id="createdBy"
                  value={`${label.user.email} | ${label.user.name}`}
                  disabled
                />
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="name" className="block">
                Label Name <span className="text-red-500">*</span>
              </Label>
              <p
                className="text-muted-foreground text-xs"
                role="region"
                aria-live="polite"
              >
                This is the name that will be displayed for the label. Please
                make sure to use the correct name.
              </p>
              <Input
                id="name"
                placeholder="Enter label name"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                required
                disabled={updateLabelMutation.isPending}
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={updateLabelMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={updateLabelMutation.isPending || !formData.name.trim()}
            >
              {updateLabelMutation.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                "Update Label"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
