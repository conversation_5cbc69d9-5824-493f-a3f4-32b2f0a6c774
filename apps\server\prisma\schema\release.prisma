enum ReleaseFormat {
    SINGLE
    EP
    ALBUM
}

enum ExplicitContent {
    EXPLICIT
    NOT_EXPLICIT
    CLEAN
}

enum ArtistRole {
    PRIMARY
    FEATURING
}

enum ReleaseStatus {
    DRAFT
    PENDING
    DELIVERED
    REJECTED
}

model Release {
    id String @id @default(uuid())
    title String
    releaseVersion String?
    label String
    format ReleaseFormat
    compilation Boolean
    explicit ExplicitContent
    upc String?
    catalogNumber String
    releaseDate DateTime
    preorderDate DateTime?
    originalReleaseDate DateTime?
    recordingYear Int
    recordingCountry String
    copyrightYear Int
    copyrightHolder String
    publishingYear Int
    publishingHolder String
    courtesyLine String?
    genre String
    subGenre String?
    language String

    coverArts ReleaseCoverArt[]

    status ReleaseStatus @default(DRAFT)
    submittedAt DateTime?
    approvedAt DateTime?
    rejectedAt DateTime?
    rejectedReason String?

    userId    String
    user      User    @relation(fields: [userId], references: [id], onDelete: Cascade)

    artists ReleaseArtist[]
    tracks ReleaseTrack[]

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@index([userId])
    @@map("release")
}

model ReleaseArtist {
    releaseId String
    artistId String
    role ArtistRole

    release Release @relation(fields: [releaseId], references: [id])
    artist Artist @relation(fields: [artistId], references: [id])

    @@id([releaseId, artistId])
    @@map("release_artist")
}

model ReleaseTrack {
    releaseId String
    trackId String
    trackOrder Int

    release Release @relation(fields: [releaseId], references: [id])
    track Track @relation(fields: [trackId], references: [id])

    @@id([releaseId, trackId])
    @@map("release_track")
}

model ReleaseCoverArt {
    id String @id @default(uuid())
    releaseId String
    imageUrl String
    imageKey String
    fileName String?
    fileSize Int?
    mimeType String?
    width Int?
    height Int?

    release Release @relation(fields: [releaseId], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@map("release_cover_art")
}