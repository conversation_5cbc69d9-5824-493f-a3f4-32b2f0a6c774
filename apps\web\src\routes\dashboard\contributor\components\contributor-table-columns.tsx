import type { ColumnDef, FilterFn } from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";
import { RowActions } from "@/routes/dashboard/contributor/components/row-actions";
import type { ContributorTableItem } from "@/routes/dashboard/contributor/components/contributor-table";

// Custom filter functions
export const multiColumnFilterFn: FilterFn<ContributorTableItem> = (
  row,
  columnId,
  filterValue
) => {
  const searchableRowContent = `${row.original.name}`.toLowerCase();
  const searchTerm = (filterValue ?? "").toLowerCase();
  return searchableRowContent.includes(searchTerm);
};

export const createColumns = (
  onContributorDeleted: () => void,
  isAdmin: boolean
): ColumnDef<ContributorTableItem>[] => {
  const baseColumns: ColumnDef<ContributorTableItem>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      size: 28,
      enableSorting: false,
      enableHiding: false,
    },
    {
      header: "Contributor Name",
      accessorKey: "name",
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue("name")}</div>
      ),
      size: 200,
      filterFn: multiColumnFilterFn,
      enableHiding: false,
    },
    {
      header: "Created At",
      accessorKey: "createdAt",
      cell: ({ row }) => {
        const date = new Date(row.getValue("createdAt"));
        return (
          <div className="text-sm text-muted-foreground">
            {date.toLocaleDateString()}
          </div>
        );
      },
      size: 120,
    },
  ];

  // Insert Created By column for admins before Actions
  if (isAdmin) {
    baseColumns.push({
      header: "Created By",
      accessorKey: "user",
      cell: ({ row }) => (
        <div className="text-sm min-w-0 truncate">
          {row.original.user?.email ?? "-"} | {row.original.user?.name ?? "-"}
        </div>
      ),
      size: 200,
    });
  }

  baseColumns.push({
    id: "actions",
    header: () => <span className="sr-only">Actions</span>,
    cell: ({ row }) => (
      <RowActions row={row} onContributorDeleted={onContributorDeleted} />
    ),
    size: 80,
    enableHiding: false,
  });

  return baseColumns;
};
