import { protectedProcedure, publicProcedure, router } from "../lib/trpc";
import { TRPCError } from "@trpc/server";
import { auth } from "../lib/auth";
import { EmailService } from "../lib/email";
import { z } from "zod";

export const emailRouter = router({
  // Email verification endpoints
  sendVerificationEmail: protectedProcedure.mutation(async ({ ctx }) => {
    const user = ctx.session.user;

    if (user.emailVerified) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "Email is already verified",
      });
    }

    try {
      // Use Better Auth's built-in email verification method
      await auth.api.sendVerificationEmail({
        body: {
          email: user.email,
          callbackURL: `${process.env.CORS_ORIGIN}/dashboard`, // Redirect to dashboard after verification
        },
      });

      return {
        success: true,
        message: "Verification email sent successfully",
      };
    } catch (error: any) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: error.message || "Failed to send verification email",
      });
    }
  }),

  // Password reset endpoints
  sendPasswordResetEmail: publicProcedure
    .input(z.object({ email: z.string().email() }))
    .mutation(async ({ input }) => {
      try {
        // Use Better Auth's built-in password reset method
        await auth.api.forgetPassword({
          body: {
            email: input.email,
            redirectTo: `${process.env.CORS_ORIGIN}/reset-password`, // Redirect to reset password page
          },
        });

        return {
          success: true,
          message: "Password reset email sent successfully",
        };
      } catch (error: any) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message || "Failed to send password reset email",
        });
      }
    }),

  resetPassword: publicProcedure
    .input(
      z.object({
        token: z.string(),
        newPassword: z
          .string()
          .min(8, "Password must be at least 8 characters"),
      })
    )
    .mutation(async ({ input }) => {
      try {
        // Use Better Auth's built-in password reset method
        await auth.api.resetPassword({
          body: {
            token: input.token,
            newPassword: input.newPassword,
          },
        });

        return {
          success: true,
          message: "Password reset successfully",
        };
      } catch (error: any) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message || "Failed to reset password",
        });
      }
    }),

  // Email change endpoints
  requestEmailChange: protectedProcedure
    .input(z.object({ newEmail: z.string().email() }))
    .mutation(async ({ ctx, input }) => {
      const user = ctx.session.user;

      // Check if the new email is the same as current email
      if (user.email === input.newEmail) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "New email address must be different from current email",
        });
      }

      try {
        // Manually trigger the email change verification using Better Auth's internal methods
        // Since there's no direct server API for changeEmail, we'll use the internal adapter
        const token = crypto.randomUUID();
        const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour from now

        // Store the email change request in the verification table
        await auth.$context.then(async (ctx) => {
          await ctx.internalAdapter.createVerificationValue({
            identifier: `email-change-token:${token}`,
            value: JSON.stringify({
              userId: user.id,
              newEmail: input.newEmail,
            }),
            expiresAt,
          });
        });

        // Generate the verification URL
        const verificationUrl = `${process.env.CORS_ORIGIN}/change-email?token=${token}`;

        // Send the email change verification email
        const result = await EmailService.sendEmailChangeVerification({
          email: user.email, // Send to current email for security
          newEmail: input.newEmail,
          url: verificationUrl,
          name: user.name,
        });

        if (!result.success) {
          throw new Error(
            result.error || "Failed to send email change verification"
          );
        }

        return {
          success: true,
          message: "Email change verification sent successfully",
        };
      } catch (error: any) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message || "Failed to send email change verification",
        });
      }
    }),

  verifyEmailChange: publicProcedure
    .input(z.object({ token: z.string() }))
    .mutation(async ({ input }) => {
      try {
        const ctx = await auth.$context;

        // Find the verification record
        const verification = await ctx.internalAdapter.findVerificationValue(
          `email-change-token:${input.token}`
        );

        if (!verification) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Invalid or expired verification token",
          });
        }

        // Parse the stored data
        const { userId, newEmail } = JSON.parse(verification.value);

        // Update the user's email
        await ctx.internalAdapter.updateUser(userId, {
          email: newEmail,
          emailVerified: true, // Mark the new email as verified
        });

        // Delete the verification record
        await ctx.internalAdapter.deleteVerificationValue(verification.id);

        return {
          success: true,
          message: "Email changed successfully",
        };
      } catch (error: any) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message || "Failed to verify email change",
        });
      }
    }),
});
