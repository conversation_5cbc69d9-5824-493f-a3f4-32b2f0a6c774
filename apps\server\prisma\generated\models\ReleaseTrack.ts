
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `ReleaseTrack` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model ReleaseTrack
 * 
 */
export type ReleaseTrackModel = runtime.Types.Result.DefaultSelection<Prisma.$ReleaseTrackPayload>

export type AggregateReleaseTrack = {
  _count: ReleaseTrackCountAggregateOutputType | null
  _avg: ReleaseTrackAvgAggregateOutputType | null
  _sum: ReleaseTrackSumAggregateOutputType | null
  _min: ReleaseTrackMinAggregateOutputType | null
  _max: ReleaseTrackMaxAggregateOutputType | null
}

export type ReleaseTrackAvgAggregateOutputType = {
  trackOrder: number | null
}

export type ReleaseTrackSumAggregateOutputType = {
  trackOrder: number | null
}

export type ReleaseTrackMinAggregateOutputType = {
  releaseId: string | null
  trackId: string | null
  trackOrder: number | null
}

export type ReleaseTrackMaxAggregateOutputType = {
  releaseId: string | null
  trackId: string | null
  trackOrder: number | null
}

export type ReleaseTrackCountAggregateOutputType = {
  releaseId: number
  trackId: number
  trackOrder: number
  _all: number
}


export type ReleaseTrackAvgAggregateInputType = {
  trackOrder?: true
}

export type ReleaseTrackSumAggregateInputType = {
  trackOrder?: true
}

export type ReleaseTrackMinAggregateInputType = {
  releaseId?: true
  trackId?: true
  trackOrder?: true
}

export type ReleaseTrackMaxAggregateInputType = {
  releaseId?: true
  trackId?: true
  trackOrder?: true
}

export type ReleaseTrackCountAggregateInputType = {
  releaseId?: true
  trackId?: true
  trackOrder?: true
  _all?: true
}

export type ReleaseTrackAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which ReleaseTrack to aggregate.
   */
  where?: Prisma.ReleaseTrackWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ReleaseTracks to fetch.
   */
  orderBy?: Prisma.ReleaseTrackOrderByWithRelationInput | Prisma.ReleaseTrackOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.ReleaseTrackWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ReleaseTracks from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ReleaseTracks.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned ReleaseTracks
  **/
  _count?: true | ReleaseTrackCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: ReleaseTrackAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: ReleaseTrackSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: ReleaseTrackMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: ReleaseTrackMaxAggregateInputType
}

export type GetReleaseTrackAggregateType<T extends ReleaseTrackAggregateArgs> = {
      [P in keyof T & keyof AggregateReleaseTrack]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateReleaseTrack[P]>
    : Prisma.GetScalarType<T[P], AggregateReleaseTrack[P]>
}




export type ReleaseTrackGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ReleaseTrackWhereInput
  orderBy?: Prisma.ReleaseTrackOrderByWithAggregationInput | Prisma.ReleaseTrackOrderByWithAggregationInput[]
  by: Prisma.ReleaseTrackScalarFieldEnum[] | Prisma.ReleaseTrackScalarFieldEnum
  having?: Prisma.ReleaseTrackScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: ReleaseTrackCountAggregateInputType | true
  _avg?: ReleaseTrackAvgAggregateInputType
  _sum?: ReleaseTrackSumAggregateInputType
  _min?: ReleaseTrackMinAggregateInputType
  _max?: ReleaseTrackMaxAggregateInputType
}

export type ReleaseTrackGroupByOutputType = {
  releaseId: string
  trackId: string
  trackOrder: number
  _count: ReleaseTrackCountAggregateOutputType | null
  _avg: ReleaseTrackAvgAggregateOutputType | null
  _sum: ReleaseTrackSumAggregateOutputType | null
  _min: ReleaseTrackMinAggregateOutputType | null
  _max: ReleaseTrackMaxAggregateOutputType | null
}

type GetReleaseTrackGroupByPayload<T extends ReleaseTrackGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<ReleaseTrackGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof ReleaseTrackGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], ReleaseTrackGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], ReleaseTrackGroupByOutputType[P]>
      }
    >
  > 



export type ReleaseTrackWhereInput = {
  AND?: Prisma.ReleaseTrackWhereInput | Prisma.ReleaseTrackWhereInput[]
  OR?: Prisma.ReleaseTrackWhereInput[]
  NOT?: Prisma.ReleaseTrackWhereInput | Prisma.ReleaseTrackWhereInput[]
  releaseId?: Prisma.StringFilter<"ReleaseTrack"> | string
  trackId?: Prisma.StringFilter<"ReleaseTrack"> | string
  trackOrder?: Prisma.IntFilter<"ReleaseTrack"> | number
  release?: Prisma.XOR<Prisma.ReleaseScalarRelationFilter, Prisma.ReleaseWhereInput>
  track?: Prisma.XOR<Prisma.TrackScalarRelationFilter, Prisma.TrackWhereInput>
}

export type ReleaseTrackOrderByWithRelationInput = {
  releaseId?: Prisma.SortOrder
  trackId?: Prisma.SortOrder
  trackOrder?: Prisma.SortOrder
  release?: Prisma.ReleaseOrderByWithRelationInput
  track?: Prisma.TrackOrderByWithRelationInput
}

export type ReleaseTrackWhereUniqueInput = Prisma.AtLeast<{
  releaseId_trackId?: Prisma.ReleaseTrackReleaseIdTrackIdCompoundUniqueInput
  AND?: Prisma.ReleaseTrackWhereInput | Prisma.ReleaseTrackWhereInput[]
  OR?: Prisma.ReleaseTrackWhereInput[]
  NOT?: Prisma.ReleaseTrackWhereInput | Prisma.ReleaseTrackWhereInput[]
  releaseId?: Prisma.StringFilter<"ReleaseTrack"> | string
  trackId?: Prisma.StringFilter<"ReleaseTrack"> | string
  trackOrder?: Prisma.IntFilter<"ReleaseTrack"> | number
  release?: Prisma.XOR<Prisma.ReleaseScalarRelationFilter, Prisma.ReleaseWhereInput>
  track?: Prisma.XOR<Prisma.TrackScalarRelationFilter, Prisma.TrackWhereInput>
}, "releaseId_trackId">

export type ReleaseTrackOrderByWithAggregationInput = {
  releaseId?: Prisma.SortOrder
  trackId?: Prisma.SortOrder
  trackOrder?: Prisma.SortOrder
  _count?: Prisma.ReleaseTrackCountOrderByAggregateInput
  _avg?: Prisma.ReleaseTrackAvgOrderByAggregateInput
  _max?: Prisma.ReleaseTrackMaxOrderByAggregateInput
  _min?: Prisma.ReleaseTrackMinOrderByAggregateInput
  _sum?: Prisma.ReleaseTrackSumOrderByAggregateInput
}

export type ReleaseTrackScalarWhereWithAggregatesInput = {
  AND?: Prisma.ReleaseTrackScalarWhereWithAggregatesInput | Prisma.ReleaseTrackScalarWhereWithAggregatesInput[]
  OR?: Prisma.ReleaseTrackScalarWhereWithAggregatesInput[]
  NOT?: Prisma.ReleaseTrackScalarWhereWithAggregatesInput | Prisma.ReleaseTrackScalarWhereWithAggregatesInput[]
  releaseId?: Prisma.StringWithAggregatesFilter<"ReleaseTrack"> | string
  trackId?: Prisma.StringWithAggregatesFilter<"ReleaseTrack"> | string
  trackOrder?: Prisma.IntWithAggregatesFilter<"ReleaseTrack"> | number
}

export type ReleaseTrackCreateInput = {
  trackOrder: number
  release: Prisma.ReleaseCreateNestedOneWithoutTracksInput
  track: Prisma.TrackCreateNestedOneWithoutReleasesInput
}

export type ReleaseTrackUncheckedCreateInput = {
  releaseId: string
  trackId: string
  trackOrder: number
}

export type ReleaseTrackUpdateInput = {
  trackOrder?: Prisma.IntFieldUpdateOperationsInput | number
  release?: Prisma.ReleaseUpdateOneRequiredWithoutTracksNestedInput
  track?: Prisma.TrackUpdateOneRequiredWithoutReleasesNestedInput
}

export type ReleaseTrackUncheckedUpdateInput = {
  releaseId?: Prisma.StringFieldUpdateOperationsInput | string
  trackId?: Prisma.StringFieldUpdateOperationsInput | string
  trackOrder?: Prisma.IntFieldUpdateOperationsInput | number
}

export type ReleaseTrackCreateManyInput = {
  releaseId: string
  trackId: string
  trackOrder: number
}

export type ReleaseTrackUpdateManyMutationInput = {
  trackOrder?: Prisma.IntFieldUpdateOperationsInput | number
}

export type ReleaseTrackUncheckedUpdateManyInput = {
  releaseId?: Prisma.StringFieldUpdateOperationsInput | string
  trackId?: Prisma.StringFieldUpdateOperationsInput | string
  trackOrder?: Prisma.IntFieldUpdateOperationsInput | number
}

export type ReleaseTrackListRelationFilter = {
  every?: Prisma.ReleaseTrackWhereInput
  some?: Prisma.ReleaseTrackWhereInput
  none?: Prisma.ReleaseTrackWhereInput
}

export type ReleaseTrackOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type ReleaseTrackReleaseIdTrackIdCompoundUniqueInput = {
  releaseId: string
  trackId: string
}

export type ReleaseTrackCountOrderByAggregateInput = {
  releaseId?: Prisma.SortOrder
  trackId?: Prisma.SortOrder
  trackOrder?: Prisma.SortOrder
}

export type ReleaseTrackAvgOrderByAggregateInput = {
  trackOrder?: Prisma.SortOrder
}

export type ReleaseTrackMaxOrderByAggregateInput = {
  releaseId?: Prisma.SortOrder
  trackId?: Prisma.SortOrder
  trackOrder?: Prisma.SortOrder
}

export type ReleaseTrackMinOrderByAggregateInput = {
  releaseId?: Prisma.SortOrder
  trackId?: Prisma.SortOrder
  trackOrder?: Prisma.SortOrder
}

export type ReleaseTrackSumOrderByAggregateInput = {
  trackOrder?: Prisma.SortOrder
}

export type ReleaseTrackCreateNestedManyWithoutReleaseInput = {
  create?: Prisma.XOR<Prisma.ReleaseTrackCreateWithoutReleaseInput, Prisma.ReleaseTrackUncheckedCreateWithoutReleaseInput> | Prisma.ReleaseTrackCreateWithoutReleaseInput[] | Prisma.ReleaseTrackUncheckedCreateWithoutReleaseInput[]
  connectOrCreate?: Prisma.ReleaseTrackCreateOrConnectWithoutReleaseInput | Prisma.ReleaseTrackCreateOrConnectWithoutReleaseInput[]
  createMany?: Prisma.ReleaseTrackCreateManyReleaseInputEnvelope
  connect?: Prisma.ReleaseTrackWhereUniqueInput | Prisma.ReleaseTrackWhereUniqueInput[]
}

export type ReleaseTrackUncheckedCreateNestedManyWithoutReleaseInput = {
  create?: Prisma.XOR<Prisma.ReleaseTrackCreateWithoutReleaseInput, Prisma.ReleaseTrackUncheckedCreateWithoutReleaseInput> | Prisma.ReleaseTrackCreateWithoutReleaseInput[] | Prisma.ReleaseTrackUncheckedCreateWithoutReleaseInput[]
  connectOrCreate?: Prisma.ReleaseTrackCreateOrConnectWithoutReleaseInput | Prisma.ReleaseTrackCreateOrConnectWithoutReleaseInput[]
  createMany?: Prisma.ReleaseTrackCreateManyReleaseInputEnvelope
  connect?: Prisma.ReleaseTrackWhereUniqueInput | Prisma.ReleaseTrackWhereUniqueInput[]
}

export type ReleaseTrackUpdateManyWithoutReleaseNestedInput = {
  create?: Prisma.XOR<Prisma.ReleaseTrackCreateWithoutReleaseInput, Prisma.ReleaseTrackUncheckedCreateWithoutReleaseInput> | Prisma.ReleaseTrackCreateWithoutReleaseInput[] | Prisma.ReleaseTrackUncheckedCreateWithoutReleaseInput[]
  connectOrCreate?: Prisma.ReleaseTrackCreateOrConnectWithoutReleaseInput | Prisma.ReleaseTrackCreateOrConnectWithoutReleaseInput[]
  upsert?: Prisma.ReleaseTrackUpsertWithWhereUniqueWithoutReleaseInput | Prisma.ReleaseTrackUpsertWithWhereUniqueWithoutReleaseInput[]
  createMany?: Prisma.ReleaseTrackCreateManyReleaseInputEnvelope
  set?: Prisma.ReleaseTrackWhereUniqueInput | Prisma.ReleaseTrackWhereUniqueInput[]
  disconnect?: Prisma.ReleaseTrackWhereUniqueInput | Prisma.ReleaseTrackWhereUniqueInput[]
  delete?: Prisma.ReleaseTrackWhereUniqueInput | Prisma.ReleaseTrackWhereUniqueInput[]
  connect?: Prisma.ReleaseTrackWhereUniqueInput | Prisma.ReleaseTrackWhereUniqueInput[]
  update?: Prisma.ReleaseTrackUpdateWithWhereUniqueWithoutReleaseInput | Prisma.ReleaseTrackUpdateWithWhereUniqueWithoutReleaseInput[]
  updateMany?: Prisma.ReleaseTrackUpdateManyWithWhereWithoutReleaseInput | Prisma.ReleaseTrackUpdateManyWithWhereWithoutReleaseInput[]
  deleteMany?: Prisma.ReleaseTrackScalarWhereInput | Prisma.ReleaseTrackScalarWhereInput[]
}

export type ReleaseTrackUncheckedUpdateManyWithoutReleaseNestedInput = {
  create?: Prisma.XOR<Prisma.ReleaseTrackCreateWithoutReleaseInput, Prisma.ReleaseTrackUncheckedCreateWithoutReleaseInput> | Prisma.ReleaseTrackCreateWithoutReleaseInput[] | Prisma.ReleaseTrackUncheckedCreateWithoutReleaseInput[]
  connectOrCreate?: Prisma.ReleaseTrackCreateOrConnectWithoutReleaseInput | Prisma.ReleaseTrackCreateOrConnectWithoutReleaseInput[]
  upsert?: Prisma.ReleaseTrackUpsertWithWhereUniqueWithoutReleaseInput | Prisma.ReleaseTrackUpsertWithWhereUniqueWithoutReleaseInput[]
  createMany?: Prisma.ReleaseTrackCreateManyReleaseInputEnvelope
  set?: Prisma.ReleaseTrackWhereUniqueInput | Prisma.ReleaseTrackWhereUniqueInput[]
  disconnect?: Prisma.ReleaseTrackWhereUniqueInput | Prisma.ReleaseTrackWhereUniqueInput[]
  delete?: Prisma.ReleaseTrackWhereUniqueInput | Prisma.ReleaseTrackWhereUniqueInput[]
  connect?: Prisma.ReleaseTrackWhereUniqueInput | Prisma.ReleaseTrackWhereUniqueInput[]
  update?: Prisma.ReleaseTrackUpdateWithWhereUniqueWithoutReleaseInput | Prisma.ReleaseTrackUpdateWithWhereUniqueWithoutReleaseInput[]
  updateMany?: Prisma.ReleaseTrackUpdateManyWithWhereWithoutReleaseInput | Prisma.ReleaseTrackUpdateManyWithWhereWithoutReleaseInput[]
  deleteMany?: Prisma.ReleaseTrackScalarWhereInput | Prisma.ReleaseTrackScalarWhereInput[]
}

export type ReleaseTrackCreateNestedManyWithoutTrackInput = {
  create?: Prisma.XOR<Prisma.ReleaseTrackCreateWithoutTrackInput, Prisma.ReleaseTrackUncheckedCreateWithoutTrackInput> | Prisma.ReleaseTrackCreateWithoutTrackInput[] | Prisma.ReleaseTrackUncheckedCreateWithoutTrackInput[]
  connectOrCreate?: Prisma.ReleaseTrackCreateOrConnectWithoutTrackInput | Prisma.ReleaseTrackCreateOrConnectWithoutTrackInput[]
  createMany?: Prisma.ReleaseTrackCreateManyTrackInputEnvelope
  connect?: Prisma.ReleaseTrackWhereUniqueInput | Prisma.ReleaseTrackWhereUniqueInput[]
}

export type ReleaseTrackUncheckedCreateNestedManyWithoutTrackInput = {
  create?: Prisma.XOR<Prisma.ReleaseTrackCreateWithoutTrackInput, Prisma.ReleaseTrackUncheckedCreateWithoutTrackInput> | Prisma.ReleaseTrackCreateWithoutTrackInput[] | Prisma.ReleaseTrackUncheckedCreateWithoutTrackInput[]
  connectOrCreate?: Prisma.ReleaseTrackCreateOrConnectWithoutTrackInput | Prisma.ReleaseTrackCreateOrConnectWithoutTrackInput[]
  createMany?: Prisma.ReleaseTrackCreateManyTrackInputEnvelope
  connect?: Prisma.ReleaseTrackWhereUniqueInput | Prisma.ReleaseTrackWhereUniqueInput[]
}

export type ReleaseTrackUpdateManyWithoutTrackNestedInput = {
  create?: Prisma.XOR<Prisma.ReleaseTrackCreateWithoutTrackInput, Prisma.ReleaseTrackUncheckedCreateWithoutTrackInput> | Prisma.ReleaseTrackCreateWithoutTrackInput[] | Prisma.ReleaseTrackUncheckedCreateWithoutTrackInput[]
  connectOrCreate?: Prisma.ReleaseTrackCreateOrConnectWithoutTrackInput | Prisma.ReleaseTrackCreateOrConnectWithoutTrackInput[]
  upsert?: Prisma.ReleaseTrackUpsertWithWhereUniqueWithoutTrackInput | Prisma.ReleaseTrackUpsertWithWhereUniqueWithoutTrackInput[]
  createMany?: Prisma.ReleaseTrackCreateManyTrackInputEnvelope
  set?: Prisma.ReleaseTrackWhereUniqueInput | Prisma.ReleaseTrackWhereUniqueInput[]
  disconnect?: Prisma.ReleaseTrackWhereUniqueInput | Prisma.ReleaseTrackWhereUniqueInput[]
  delete?: Prisma.ReleaseTrackWhereUniqueInput | Prisma.ReleaseTrackWhereUniqueInput[]
  connect?: Prisma.ReleaseTrackWhereUniqueInput | Prisma.ReleaseTrackWhereUniqueInput[]
  update?: Prisma.ReleaseTrackUpdateWithWhereUniqueWithoutTrackInput | Prisma.ReleaseTrackUpdateWithWhereUniqueWithoutTrackInput[]
  updateMany?: Prisma.ReleaseTrackUpdateManyWithWhereWithoutTrackInput | Prisma.ReleaseTrackUpdateManyWithWhereWithoutTrackInput[]
  deleteMany?: Prisma.ReleaseTrackScalarWhereInput | Prisma.ReleaseTrackScalarWhereInput[]
}

export type ReleaseTrackUncheckedUpdateManyWithoutTrackNestedInput = {
  create?: Prisma.XOR<Prisma.ReleaseTrackCreateWithoutTrackInput, Prisma.ReleaseTrackUncheckedCreateWithoutTrackInput> | Prisma.ReleaseTrackCreateWithoutTrackInput[] | Prisma.ReleaseTrackUncheckedCreateWithoutTrackInput[]
  connectOrCreate?: Prisma.ReleaseTrackCreateOrConnectWithoutTrackInput | Prisma.ReleaseTrackCreateOrConnectWithoutTrackInput[]
  upsert?: Prisma.ReleaseTrackUpsertWithWhereUniqueWithoutTrackInput | Prisma.ReleaseTrackUpsertWithWhereUniqueWithoutTrackInput[]
  createMany?: Prisma.ReleaseTrackCreateManyTrackInputEnvelope
  set?: Prisma.ReleaseTrackWhereUniqueInput | Prisma.ReleaseTrackWhereUniqueInput[]
  disconnect?: Prisma.ReleaseTrackWhereUniqueInput | Prisma.ReleaseTrackWhereUniqueInput[]
  delete?: Prisma.ReleaseTrackWhereUniqueInput | Prisma.ReleaseTrackWhereUniqueInput[]
  connect?: Prisma.ReleaseTrackWhereUniqueInput | Prisma.ReleaseTrackWhereUniqueInput[]
  update?: Prisma.ReleaseTrackUpdateWithWhereUniqueWithoutTrackInput | Prisma.ReleaseTrackUpdateWithWhereUniqueWithoutTrackInput[]
  updateMany?: Prisma.ReleaseTrackUpdateManyWithWhereWithoutTrackInput | Prisma.ReleaseTrackUpdateManyWithWhereWithoutTrackInput[]
  deleteMany?: Prisma.ReleaseTrackScalarWhereInput | Prisma.ReleaseTrackScalarWhereInput[]
}

export type ReleaseTrackCreateWithoutReleaseInput = {
  trackOrder: number
  track: Prisma.TrackCreateNestedOneWithoutReleasesInput
}

export type ReleaseTrackUncheckedCreateWithoutReleaseInput = {
  trackId: string
  trackOrder: number
}

export type ReleaseTrackCreateOrConnectWithoutReleaseInput = {
  where: Prisma.ReleaseTrackWhereUniqueInput
  create: Prisma.XOR<Prisma.ReleaseTrackCreateWithoutReleaseInput, Prisma.ReleaseTrackUncheckedCreateWithoutReleaseInput>
}

export type ReleaseTrackCreateManyReleaseInputEnvelope = {
  data: Prisma.ReleaseTrackCreateManyReleaseInput | Prisma.ReleaseTrackCreateManyReleaseInput[]
  skipDuplicates?: boolean
}

export type ReleaseTrackUpsertWithWhereUniqueWithoutReleaseInput = {
  where: Prisma.ReleaseTrackWhereUniqueInput
  update: Prisma.XOR<Prisma.ReleaseTrackUpdateWithoutReleaseInput, Prisma.ReleaseTrackUncheckedUpdateWithoutReleaseInput>
  create: Prisma.XOR<Prisma.ReleaseTrackCreateWithoutReleaseInput, Prisma.ReleaseTrackUncheckedCreateWithoutReleaseInput>
}

export type ReleaseTrackUpdateWithWhereUniqueWithoutReleaseInput = {
  where: Prisma.ReleaseTrackWhereUniqueInput
  data: Prisma.XOR<Prisma.ReleaseTrackUpdateWithoutReleaseInput, Prisma.ReleaseTrackUncheckedUpdateWithoutReleaseInput>
}

export type ReleaseTrackUpdateManyWithWhereWithoutReleaseInput = {
  where: Prisma.ReleaseTrackScalarWhereInput
  data: Prisma.XOR<Prisma.ReleaseTrackUpdateManyMutationInput, Prisma.ReleaseTrackUncheckedUpdateManyWithoutReleaseInput>
}

export type ReleaseTrackScalarWhereInput = {
  AND?: Prisma.ReleaseTrackScalarWhereInput | Prisma.ReleaseTrackScalarWhereInput[]
  OR?: Prisma.ReleaseTrackScalarWhereInput[]
  NOT?: Prisma.ReleaseTrackScalarWhereInput | Prisma.ReleaseTrackScalarWhereInput[]
  releaseId?: Prisma.StringFilter<"ReleaseTrack"> | string
  trackId?: Prisma.StringFilter<"ReleaseTrack"> | string
  trackOrder?: Prisma.IntFilter<"ReleaseTrack"> | number
}

export type ReleaseTrackCreateWithoutTrackInput = {
  trackOrder: number
  release: Prisma.ReleaseCreateNestedOneWithoutTracksInput
}

export type ReleaseTrackUncheckedCreateWithoutTrackInput = {
  releaseId: string
  trackOrder: number
}

export type ReleaseTrackCreateOrConnectWithoutTrackInput = {
  where: Prisma.ReleaseTrackWhereUniqueInput
  create: Prisma.XOR<Prisma.ReleaseTrackCreateWithoutTrackInput, Prisma.ReleaseTrackUncheckedCreateWithoutTrackInput>
}

export type ReleaseTrackCreateManyTrackInputEnvelope = {
  data: Prisma.ReleaseTrackCreateManyTrackInput | Prisma.ReleaseTrackCreateManyTrackInput[]
  skipDuplicates?: boolean
}

export type ReleaseTrackUpsertWithWhereUniqueWithoutTrackInput = {
  where: Prisma.ReleaseTrackWhereUniqueInput
  update: Prisma.XOR<Prisma.ReleaseTrackUpdateWithoutTrackInput, Prisma.ReleaseTrackUncheckedUpdateWithoutTrackInput>
  create: Prisma.XOR<Prisma.ReleaseTrackCreateWithoutTrackInput, Prisma.ReleaseTrackUncheckedCreateWithoutTrackInput>
}

export type ReleaseTrackUpdateWithWhereUniqueWithoutTrackInput = {
  where: Prisma.ReleaseTrackWhereUniqueInput
  data: Prisma.XOR<Prisma.ReleaseTrackUpdateWithoutTrackInput, Prisma.ReleaseTrackUncheckedUpdateWithoutTrackInput>
}

export type ReleaseTrackUpdateManyWithWhereWithoutTrackInput = {
  where: Prisma.ReleaseTrackScalarWhereInput
  data: Prisma.XOR<Prisma.ReleaseTrackUpdateManyMutationInput, Prisma.ReleaseTrackUncheckedUpdateManyWithoutTrackInput>
}

export type ReleaseTrackCreateManyReleaseInput = {
  trackId: string
  trackOrder: number
}

export type ReleaseTrackUpdateWithoutReleaseInput = {
  trackOrder?: Prisma.IntFieldUpdateOperationsInput | number
  track?: Prisma.TrackUpdateOneRequiredWithoutReleasesNestedInput
}

export type ReleaseTrackUncheckedUpdateWithoutReleaseInput = {
  trackId?: Prisma.StringFieldUpdateOperationsInput | string
  trackOrder?: Prisma.IntFieldUpdateOperationsInput | number
}

export type ReleaseTrackUncheckedUpdateManyWithoutReleaseInput = {
  trackId?: Prisma.StringFieldUpdateOperationsInput | string
  trackOrder?: Prisma.IntFieldUpdateOperationsInput | number
}

export type ReleaseTrackCreateManyTrackInput = {
  releaseId: string
  trackOrder: number
}

export type ReleaseTrackUpdateWithoutTrackInput = {
  trackOrder?: Prisma.IntFieldUpdateOperationsInput | number
  release?: Prisma.ReleaseUpdateOneRequiredWithoutTracksNestedInput
}

export type ReleaseTrackUncheckedUpdateWithoutTrackInput = {
  releaseId?: Prisma.StringFieldUpdateOperationsInput | string
  trackOrder?: Prisma.IntFieldUpdateOperationsInput | number
}

export type ReleaseTrackUncheckedUpdateManyWithoutTrackInput = {
  releaseId?: Prisma.StringFieldUpdateOperationsInput | string
  trackOrder?: Prisma.IntFieldUpdateOperationsInput | number
}



export type ReleaseTrackSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  releaseId?: boolean
  trackId?: boolean
  trackOrder?: boolean
  release?: boolean | Prisma.ReleaseDefaultArgs<ExtArgs>
  track?: boolean | Prisma.TrackDefaultArgs<ExtArgs>
}, ExtArgs["result"]["releaseTrack"]>

export type ReleaseTrackSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  releaseId?: boolean
  trackId?: boolean
  trackOrder?: boolean
  release?: boolean | Prisma.ReleaseDefaultArgs<ExtArgs>
  track?: boolean | Prisma.TrackDefaultArgs<ExtArgs>
}, ExtArgs["result"]["releaseTrack"]>

export type ReleaseTrackSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  releaseId?: boolean
  trackId?: boolean
  trackOrder?: boolean
  release?: boolean | Prisma.ReleaseDefaultArgs<ExtArgs>
  track?: boolean | Prisma.TrackDefaultArgs<ExtArgs>
}, ExtArgs["result"]["releaseTrack"]>

export type ReleaseTrackSelectScalar = {
  releaseId?: boolean
  trackId?: boolean
  trackOrder?: boolean
}

export type ReleaseTrackOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"releaseId" | "trackId" | "trackOrder", ExtArgs["result"]["releaseTrack"]>
export type ReleaseTrackInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  release?: boolean | Prisma.ReleaseDefaultArgs<ExtArgs>
  track?: boolean | Prisma.TrackDefaultArgs<ExtArgs>
}
export type ReleaseTrackIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  release?: boolean | Prisma.ReleaseDefaultArgs<ExtArgs>
  track?: boolean | Prisma.TrackDefaultArgs<ExtArgs>
}
export type ReleaseTrackIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  release?: boolean | Prisma.ReleaseDefaultArgs<ExtArgs>
  track?: boolean | Prisma.TrackDefaultArgs<ExtArgs>
}

export type $ReleaseTrackPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "ReleaseTrack"
  objects: {
    release: Prisma.$ReleasePayload<ExtArgs>
    track: Prisma.$TrackPayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    releaseId: string
    trackId: string
    trackOrder: number
  }, ExtArgs["result"]["releaseTrack"]>
  composites: {}
}

export type ReleaseTrackGetPayload<S extends boolean | null | undefined | ReleaseTrackDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$ReleaseTrackPayload, S>

export type ReleaseTrackCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<ReleaseTrackFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: ReleaseTrackCountAggregateInputType | true
  }

export interface ReleaseTrackDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['ReleaseTrack'], meta: { name: 'ReleaseTrack' } }
  /**
   * Find zero or one ReleaseTrack that matches the filter.
   * @param {ReleaseTrackFindUniqueArgs} args - Arguments to find a ReleaseTrack
   * @example
   * // Get one ReleaseTrack
   * const releaseTrack = await prisma.releaseTrack.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends ReleaseTrackFindUniqueArgs>(args: Prisma.SelectSubset<T, ReleaseTrackFindUniqueArgs<ExtArgs>>): Prisma.Prisma__ReleaseTrackClient<runtime.Types.Result.GetResult<Prisma.$ReleaseTrackPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one ReleaseTrack that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {ReleaseTrackFindUniqueOrThrowArgs} args - Arguments to find a ReleaseTrack
   * @example
   * // Get one ReleaseTrack
   * const releaseTrack = await prisma.releaseTrack.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends ReleaseTrackFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, ReleaseTrackFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__ReleaseTrackClient<runtime.Types.Result.GetResult<Prisma.$ReleaseTrackPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first ReleaseTrack that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReleaseTrackFindFirstArgs} args - Arguments to find a ReleaseTrack
   * @example
   * // Get one ReleaseTrack
   * const releaseTrack = await prisma.releaseTrack.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends ReleaseTrackFindFirstArgs>(args?: Prisma.SelectSubset<T, ReleaseTrackFindFirstArgs<ExtArgs>>): Prisma.Prisma__ReleaseTrackClient<runtime.Types.Result.GetResult<Prisma.$ReleaseTrackPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first ReleaseTrack that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReleaseTrackFindFirstOrThrowArgs} args - Arguments to find a ReleaseTrack
   * @example
   * // Get one ReleaseTrack
   * const releaseTrack = await prisma.releaseTrack.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends ReleaseTrackFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, ReleaseTrackFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__ReleaseTrackClient<runtime.Types.Result.GetResult<Prisma.$ReleaseTrackPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more ReleaseTracks that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReleaseTrackFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all ReleaseTracks
   * const releaseTracks = await prisma.releaseTrack.findMany()
   * 
   * // Get first 10 ReleaseTracks
   * const releaseTracks = await prisma.releaseTrack.findMany({ take: 10 })
   * 
   * // Only select the `releaseId`
   * const releaseTrackWithReleaseIdOnly = await prisma.releaseTrack.findMany({ select: { releaseId: true } })
   * 
   */
  findMany<T extends ReleaseTrackFindManyArgs>(args?: Prisma.SelectSubset<T, ReleaseTrackFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ReleaseTrackPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a ReleaseTrack.
   * @param {ReleaseTrackCreateArgs} args - Arguments to create a ReleaseTrack.
   * @example
   * // Create one ReleaseTrack
   * const ReleaseTrack = await prisma.releaseTrack.create({
   *   data: {
   *     // ... data to create a ReleaseTrack
   *   }
   * })
   * 
   */
  create<T extends ReleaseTrackCreateArgs>(args: Prisma.SelectSubset<T, ReleaseTrackCreateArgs<ExtArgs>>): Prisma.Prisma__ReleaseTrackClient<runtime.Types.Result.GetResult<Prisma.$ReleaseTrackPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many ReleaseTracks.
   * @param {ReleaseTrackCreateManyArgs} args - Arguments to create many ReleaseTracks.
   * @example
   * // Create many ReleaseTracks
   * const releaseTrack = await prisma.releaseTrack.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends ReleaseTrackCreateManyArgs>(args?: Prisma.SelectSubset<T, ReleaseTrackCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many ReleaseTracks and returns the data saved in the database.
   * @param {ReleaseTrackCreateManyAndReturnArgs} args - Arguments to create many ReleaseTracks.
   * @example
   * // Create many ReleaseTracks
   * const releaseTrack = await prisma.releaseTrack.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many ReleaseTracks and only return the `releaseId`
   * const releaseTrackWithReleaseIdOnly = await prisma.releaseTrack.createManyAndReturn({
   *   select: { releaseId: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends ReleaseTrackCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, ReleaseTrackCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ReleaseTrackPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a ReleaseTrack.
   * @param {ReleaseTrackDeleteArgs} args - Arguments to delete one ReleaseTrack.
   * @example
   * // Delete one ReleaseTrack
   * const ReleaseTrack = await prisma.releaseTrack.delete({
   *   where: {
   *     // ... filter to delete one ReleaseTrack
   *   }
   * })
   * 
   */
  delete<T extends ReleaseTrackDeleteArgs>(args: Prisma.SelectSubset<T, ReleaseTrackDeleteArgs<ExtArgs>>): Prisma.Prisma__ReleaseTrackClient<runtime.Types.Result.GetResult<Prisma.$ReleaseTrackPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one ReleaseTrack.
   * @param {ReleaseTrackUpdateArgs} args - Arguments to update one ReleaseTrack.
   * @example
   * // Update one ReleaseTrack
   * const releaseTrack = await prisma.releaseTrack.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends ReleaseTrackUpdateArgs>(args: Prisma.SelectSubset<T, ReleaseTrackUpdateArgs<ExtArgs>>): Prisma.Prisma__ReleaseTrackClient<runtime.Types.Result.GetResult<Prisma.$ReleaseTrackPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more ReleaseTracks.
   * @param {ReleaseTrackDeleteManyArgs} args - Arguments to filter ReleaseTracks to delete.
   * @example
   * // Delete a few ReleaseTracks
   * const { count } = await prisma.releaseTrack.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends ReleaseTrackDeleteManyArgs>(args?: Prisma.SelectSubset<T, ReleaseTrackDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more ReleaseTracks.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReleaseTrackUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many ReleaseTracks
   * const releaseTrack = await prisma.releaseTrack.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends ReleaseTrackUpdateManyArgs>(args: Prisma.SelectSubset<T, ReleaseTrackUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more ReleaseTracks and returns the data updated in the database.
   * @param {ReleaseTrackUpdateManyAndReturnArgs} args - Arguments to update many ReleaseTracks.
   * @example
   * // Update many ReleaseTracks
   * const releaseTrack = await prisma.releaseTrack.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more ReleaseTracks and only return the `releaseId`
   * const releaseTrackWithReleaseIdOnly = await prisma.releaseTrack.updateManyAndReturn({
   *   select: { releaseId: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends ReleaseTrackUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, ReleaseTrackUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ReleaseTrackPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one ReleaseTrack.
   * @param {ReleaseTrackUpsertArgs} args - Arguments to update or create a ReleaseTrack.
   * @example
   * // Update or create a ReleaseTrack
   * const releaseTrack = await prisma.releaseTrack.upsert({
   *   create: {
   *     // ... data to create a ReleaseTrack
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the ReleaseTrack we want to update
   *   }
   * })
   */
  upsert<T extends ReleaseTrackUpsertArgs>(args: Prisma.SelectSubset<T, ReleaseTrackUpsertArgs<ExtArgs>>): Prisma.Prisma__ReleaseTrackClient<runtime.Types.Result.GetResult<Prisma.$ReleaseTrackPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of ReleaseTracks.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReleaseTrackCountArgs} args - Arguments to filter ReleaseTracks to count.
   * @example
   * // Count the number of ReleaseTracks
   * const count = await prisma.releaseTrack.count({
   *   where: {
   *     // ... the filter for the ReleaseTracks we want to count
   *   }
   * })
  **/
  count<T extends ReleaseTrackCountArgs>(
    args?: Prisma.Subset<T, ReleaseTrackCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], ReleaseTrackCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a ReleaseTrack.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReleaseTrackAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends ReleaseTrackAggregateArgs>(args: Prisma.Subset<T, ReleaseTrackAggregateArgs>): Prisma.PrismaPromise<GetReleaseTrackAggregateType<T>>

  /**
   * Group by ReleaseTrack.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReleaseTrackGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends ReleaseTrackGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: ReleaseTrackGroupByArgs['orderBy'] }
      : { orderBy?: ReleaseTrackGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, ReleaseTrackGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetReleaseTrackGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the ReleaseTrack model
 */
readonly fields: ReleaseTrackFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for ReleaseTrack.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__ReleaseTrackClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  release<T extends Prisma.ReleaseDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.ReleaseDefaultArgs<ExtArgs>>): Prisma.Prisma__ReleaseClient<runtime.Types.Result.GetResult<Prisma.$ReleasePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  track<T extends Prisma.TrackDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.TrackDefaultArgs<ExtArgs>>): Prisma.Prisma__TrackClient<runtime.Types.Result.GetResult<Prisma.$TrackPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the ReleaseTrack model
 */
export interface ReleaseTrackFieldRefs {
  readonly releaseId: Prisma.FieldRef<"ReleaseTrack", 'String'>
  readonly trackId: Prisma.FieldRef<"ReleaseTrack", 'String'>
  readonly trackOrder: Prisma.FieldRef<"ReleaseTrack", 'Int'>
}
    

// Custom InputTypes
/**
 * ReleaseTrack findUnique
 */
export type ReleaseTrackFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseTrack
   */
  select?: Prisma.ReleaseTrackSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseTrack
   */
  omit?: Prisma.ReleaseTrackOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseTrackInclude<ExtArgs> | null
  /**
   * Filter, which ReleaseTrack to fetch.
   */
  where: Prisma.ReleaseTrackWhereUniqueInput
}

/**
 * ReleaseTrack findUniqueOrThrow
 */
export type ReleaseTrackFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseTrack
   */
  select?: Prisma.ReleaseTrackSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseTrack
   */
  omit?: Prisma.ReleaseTrackOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseTrackInclude<ExtArgs> | null
  /**
   * Filter, which ReleaseTrack to fetch.
   */
  where: Prisma.ReleaseTrackWhereUniqueInput
}

/**
 * ReleaseTrack findFirst
 */
export type ReleaseTrackFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseTrack
   */
  select?: Prisma.ReleaseTrackSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseTrack
   */
  omit?: Prisma.ReleaseTrackOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseTrackInclude<ExtArgs> | null
  /**
   * Filter, which ReleaseTrack to fetch.
   */
  where?: Prisma.ReleaseTrackWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ReleaseTracks to fetch.
   */
  orderBy?: Prisma.ReleaseTrackOrderByWithRelationInput | Prisma.ReleaseTrackOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for ReleaseTracks.
   */
  cursor?: Prisma.ReleaseTrackWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ReleaseTracks from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ReleaseTracks.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of ReleaseTracks.
   */
  distinct?: Prisma.ReleaseTrackScalarFieldEnum | Prisma.ReleaseTrackScalarFieldEnum[]
}

/**
 * ReleaseTrack findFirstOrThrow
 */
export type ReleaseTrackFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseTrack
   */
  select?: Prisma.ReleaseTrackSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseTrack
   */
  omit?: Prisma.ReleaseTrackOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseTrackInclude<ExtArgs> | null
  /**
   * Filter, which ReleaseTrack to fetch.
   */
  where?: Prisma.ReleaseTrackWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ReleaseTracks to fetch.
   */
  orderBy?: Prisma.ReleaseTrackOrderByWithRelationInput | Prisma.ReleaseTrackOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for ReleaseTracks.
   */
  cursor?: Prisma.ReleaseTrackWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ReleaseTracks from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ReleaseTracks.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of ReleaseTracks.
   */
  distinct?: Prisma.ReleaseTrackScalarFieldEnum | Prisma.ReleaseTrackScalarFieldEnum[]
}

/**
 * ReleaseTrack findMany
 */
export type ReleaseTrackFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseTrack
   */
  select?: Prisma.ReleaseTrackSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseTrack
   */
  omit?: Prisma.ReleaseTrackOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseTrackInclude<ExtArgs> | null
  /**
   * Filter, which ReleaseTracks to fetch.
   */
  where?: Prisma.ReleaseTrackWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ReleaseTracks to fetch.
   */
  orderBy?: Prisma.ReleaseTrackOrderByWithRelationInput | Prisma.ReleaseTrackOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing ReleaseTracks.
   */
  cursor?: Prisma.ReleaseTrackWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ReleaseTracks from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ReleaseTracks.
   */
  skip?: number
  distinct?: Prisma.ReleaseTrackScalarFieldEnum | Prisma.ReleaseTrackScalarFieldEnum[]
}

/**
 * ReleaseTrack create
 */
export type ReleaseTrackCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseTrack
   */
  select?: Prisma.ReleaseTrackSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseTrack
   */
  omit?: Prisma.ReleaseTrackOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseTrackInclude<ExtArgs> | null
  /**
   * The data needed to create a ReleaseTrack.
   */
  data: Prisma.XOR<Prisma.ReleaseTrackCreateInput, Prisma.ReleaseTrackUncheckedCreateInput>
}

/**
 * ReleaseTrack createMany
 */
export type ReleaseTrackCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many ReleaseTracks.
   */
  data: Prisma.ReleaseTrackCreateManyInput | Prisma.ReleaseTrackCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * ReleaseTrack createManyAndReturn
 */
export type ReleaseTrackCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseTrack
   */
  select?: Prisma.ReleaseTrackSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseTrack
   */
  omit?: Prisma.ReleaseTrackOmit<ExtArgs> | null
  /**
   * The data used to create many ReleaseTracks.
   */
  data: Prisma.ReleaseTrackCreateManyInput | Prisma.ReleaseTrackCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseTrackIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * ReleaseTrack update
 */
export type ReleaseTrackUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseTrack
   */
  select?: Prisma.ReleaseTrackSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseTrack
   */
  omit?: Prisma.ReleaseTrackOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseTrackInclude<ExtArgs> | null
  /**
   * The data needed to update a ReleaseTrack.
   */
  data: Prisma.XOR<Prisma.ReleaseTrackUpdateInput, Prisma.ReleaseTrackUncheckedUpdateInput>
  /**
   * Choose, which ReleaseTrack to update.
   */
  where: Prisma.ReleaseTrackWhereUniqueInput
}

/**
 * ReleaseTrack updateMany
 */
export type ReleaseTrackUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update ReleaseTracks.
   */
  data: Prisma.XOR<Prisma.ReleaseTrackUpdateManyMutationInput, Prisma.ReleaseTrackUncheckedUpdateManyInput>
  /**
   * Filter which ReleaseTracks to update
   */
  where?: Prisma.ReleaseTrackWhereInput
  /**
   * Limit how many ReleaseTracks to update.
   */
  limit?: number
}

/**
 * ReleaseTrack updateManyAndReturn
 */
export type ReleaseTrackUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseTrack
   */
  select?: Prisma.ReleaseTrackSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseTrack
   */
  omit?: Prisma.ReleaseTrackOmit<ExtArgs> | null
  /**
   * The data used to update ReleaseTracks.
   */
  data: Prisma.XOR<Prisma.ReleaseTrackUpdateManyMutationInput, Prisma.ReleaseTrackUncheckedUpdateManyInput>
  /**
   * Filter which ReleaseTracks to update
   */
  where?: Prisma.ReleaseTrackWhereInput
  /**
   * Limit how many ReleaseTracks to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseTrackIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * ReleaseTrack upsert
 */
export type ReleaseTrackUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseTrack
   */
  select?: Prisma.ReleaseTrackSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseTrack
   */
  omit?: Prisma.ReleaseTrackOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseTrackInclude<ExtArgs> | null
  /**
   * The filter to search for the ReleaseTrack to update in case it exists.
   */
  where: Prisma.ReleaseTrackWhereUniqueInput
  /**
   * In case the ReleaseTrack found by the `where` argument doesn't exist, create a new ReleaseTrack with this data.
   */
  create: Prisma.XOR<Prisma.ReleaseTrackCreateInput, Prisma.ReleaseTrackUncheckedCreateInput>
  /**
   * In case the ReleaseTrack was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.ReleaseTrackUpdateInput, Prisma.ReleaseTrackUncheckedUpdateInput>
}

/**
 * ReleaseTrack delete
 */
export type ReleaseTrackDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseTrack
   */
  select?: Prisma.ReleaseTrackSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseTrack
   */
  omit?: Prisma.ReleaseTrackOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseTrackInclude<ExtArgs> | null
  /**
   * Filter which ReleaseTrack to delete.
   */
  where: Prisma.ReleaseTrackWhereUniqueInput
}

/**
 * ReleaseTrack deleteMany
 */
export type ReleaseTrackDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which ReleaseTracks to delete
   */
  where?: Prisma.ReleaseTrackWhereInput
  /**
   * Limit how many ReleaseTracks to delete.
   */
  limit?: number
}

/**
 * ReleaseTrack without action
 */
export type ReleaseTrackDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseTrack
   */
  select?: Prisma.ReleaseTrackSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseTrack
   */
  omit?: Prisma.ReleaseTrackOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseTrackInclude<ExtArgs> | null
}
