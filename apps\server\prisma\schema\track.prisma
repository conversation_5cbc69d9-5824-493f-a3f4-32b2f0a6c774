enum RightsClaim {
    NO_CLAIM
    REPORT
    MONETIZE
    BLOCK
}

enum TrackStatus {
    DRAFT
    READY
}

model Track {
    id String @id @default(uuid())
    title String
    isrc String?
    trackVersion String?
    recordingYear Int
    publishingYear Int
    publishingHolder String
    genre String
    subGenre String?
    lyrics String?
    previewStart String?
    previewLength String?
    metadataLanguage String
    explicit ExplicitContent
    audioLanguage String
    rightsClaim RightsClaim

    trackFiles TrackFile[]

    status TrackStatus @default(DRAFT)
    submittedAt DateTime?
    readyAt DateTime?

    userId String
    user User @relation(fields: [userId], references: [id])

    artists TrackArtist[]
    contributors TrackContributor[]
    releases ReleaseTrack[]

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@index([userId])
    @@map("track")
}

model TrackArtist {
    trackId String
    artistId String
    role ArtistRole

    track Track @relation(fields: [trackId], references: [id])
    artist Artist @relation(fields: [artistId], references: [id])

    @@id([trackId, artistId])
    @@map("track_artist")
}

model TrackContributor {
    trackId String
    contributorId String
    role String

    track Track @relation(fields: [trackId], references: [id])
    contributor Contributor @relation(fields: [contributorId], references: [id])

    @@id([trackId, contributorId])
    @@map("track_contributor")
}

model TrackFile {
    id String @id @default(uuid())
    trackId String
    fileUrl String
    fileKey String
    fileName String?
    fileSize Int?
    mimeType String?
    duration Int?

    track Track @relation(fields: [trackId], references: [id])

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@map("track_file")
}