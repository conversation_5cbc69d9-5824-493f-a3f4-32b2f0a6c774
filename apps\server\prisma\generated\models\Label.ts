
/* !!! This is code generated by Prisma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `Label` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model Label
 * 
 */
export type LabelModel = runtime.Types.Result.DefaultSelection<Prisma.$LabelPayload>

export type AggregateLabel = {
  _count: LabelCountAggregateOutputType | null
  _min: LabelMinAggregateOutputType | null
  _max: LabelMaxAggregateOutputType | null
}

export type LabelMinAggregateOutputType = {
  id: string | null
  name: string | null
  userId: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type LabelMaxAggregateOutputType = {
  id: string | null
  name: string | null
  userId: string | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type LabelCountAggregateOutputType = {
  id: number
  name: number
  userId: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type LabelMinAggregateInputType = {
  id?: true
  name?: true
  userId?: true
  createdAt?: true
  updatedAt?: true
}

export type LabelMaxAggregateInputType = {
  id?: true
  name?: true
  userId?: true
  createdAt?: true
  updatedAt?: true
}

export type LabelCountAggregateInputType = {
  id?: true
  name?: true
  userId?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type LabelAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Label to aggregate.
   */
  where?: Prisma.LabelWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Labels to fetch.
   */
  orderBy?: Prisma.LabelOrderByWithRelationInput | Prisma.LabelOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.LabelWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Labels from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Labels.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned Labels
  **/
  _count?: true | LabelCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: LabelMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: LabelMaxAggregateInputType
}

export type GetLabelAggregateType<T extends LabelAggregateArgs> = {
      [P in keyof T & keyof AggregateLabel]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateLabel[P]>
    : Prisma.GetScalarType<T[P], AggregateLabel[P]>
}




export type LabelGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.LabelWhereInput
  orderBy?: Prisma.LabelOrderByWithAggregationInput | Prisma.LabelOrderByWithAggregationInput[]
  by: Prisma.LabelScalarFieldEnum[] | Prisma.LabelScalarFieldEnum
  having?: Prisma.LabelScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: LabelCountAggregateInputType | true
  _min?: LabelMinAggregateInputType
  _max?: LabelMaxAggregateInputType
}

export type LabelGroupByOutputType = {
  id: string
  name: string
  userId: string
  createdAt: Date
  updatedAt: Date
  _count: LabelCountAggregateOutputType | null
  _min: LabelMinAggregateOutputType | null
  _max: LabelMaxAggregateOutputType | null
}

type GetLabelGroupByPayload<T extends LabelGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<LabelGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof LabelGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], LabelGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], LabelGroupByOutputType[P]>
      }
    >
  > 



export type LabelWhereInput = {
  AND?: Prisma.LabelWhereInput | Prisma.LabelWhereInput[]
  OR?: Prisma.LabelWhereInput[]
  NOT?: Prisma.LabelWhereInput | Prisma.LabelWhereInput[]
  id?: Prisma.StringFilter<"Label"> | string
  name?: Prisma.StringFilter<"Label"> | string
  userId?: Prisma.StringFilter<"Label"> | string
  createdAt?: Prisma.DateTimeFilter<"Label"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Label"> | Date | string
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
  artists?: Prisma.ArtistListRelationFilter
}

export type LabelOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  user?: Prisma.UserOrderByWithRelationInput
  artists?: Prisma.ArtistOrderByRelationAggregateInput
}

export type LabelWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  userId_name?: Prisma.LabelUserIdNameCompoundUniqueInput
  AND?: Prisma.LabelWhereInput | Prisma.LabelWhereInput[]
  OR?: Prisma.LabelWhereInput[]
  NOT?: Prisma.LabelWhereInput | Prisma.LabelWhereInput[]
  name?: Prisma.StringFilter<"Label"> | string
  userId?: Prisma.StringFilter<"Label"> | string
  createdAt?: Prisma.DateTimeFilter<"Label"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Label"> | Date | string
  user?: Prisma.XOR<Prisma.UserScalarRelationFilter, Prisma.UserWhereInput>
  artists?: Prisma.ArtistListRelationFilter
}, "id" | "userId_name">

export type LabelOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.LabelCountOrderByAggregateInput
  _max?: Prisma.LabelMaxOrderByAggregateInput
  _min?: Prisma.LabelMinOrderByAggregateInput
}

export type LabelScalarWhereWithAggregatesInput = {
  AND?: Prisma.LabelScalarWhereWithAggregatesInput | Prisma.LabelScalarWhereWithAggregatesInput[]
  OR?: Prisma.LabelScalarWhereWithAggregatesInput[]
  NOT?: Prisma.LabelScalarWhereWithAggregatesInput | Prisma.LabelScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"Label"> | string
  name?: Prisma.StringWithAggregatesFilter<"Label"> | string
  userId?: Prisma.StringWithAggregatesFilter<"Label"> | string
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"Label"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"Label"> | Date | string
}

export type LabelCreateInput = {
  id?: string
  name: string
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutLabelsInput
  artists?: Prisma.ArtistCreateNestedManyWithoutLabelInput
}

export type LabelUncheckedCreateInput = {
  id?: string
  name: string
  userId: string
  createdAt?: Date | string
  updatedAt?: Date | string
  artists?: Prisma.ArtistUncheckedCreateNestedManyWithoutLabelInput
}

export type LabelUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutLabelsNestedInput
  artists?: Prisma.ArtistUpdateManyWithoutLabelNestedInput
}

export type LabelUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  artists?: Prisma.ArtistUncheckedUpdateManyWithoutLabelNestedInput
}

export type LabelCreateManyInput = {
  id?: string
  name: string
  userId: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type LabelUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type LabelUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type LabelNullableScalarRelationFilter = {
  is?: Prisma.LabelWhereInput | null
  isNot?: Prisma.LabelWhereInput | null
}

export type LabelListRelationFilter = {
  every?: Prisma.LabelWhereInput
  some?: Prisma.LabelWhereInput
  none?: Prisma.LabelWhereInput
}

export type LabelOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type LabelUserIdNameCompoundUniqueInput = {
  userId: string
  name: string
}

export type LabelCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type LabelMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type LabelMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  name?: Prisma.SortOrder
  userId?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type LabelCreateNestedOneWithoutArtistsInput = {
  create?: Prisma.XOR<Prisma.LabelCreateWithoutArtistsInput, Prisma.LabelUncheckedCreateWithoutArtistsInput>
  connectOrCreate?: Prisma.LabelCreateOrConnectWithoutArtistsInput
  connect?: Prisma.LabelWhereUniqueInput
}

export type LabelUpdateOneWithoutArtistsNestedInput = {
  create?: Prisma.XOR<Prisma.LabelCreateWithoutArtistsInput, Prisma.LabelUncheckedCreateWithoutArtistsInput>
  connectOrCreate?: Prisma.LabelCreateOrConnectWithoutArtistsInput
  upsert?: Prisma.LabelUpsertWithoutArtistsInput
  disconnect?: Prisma.LabelWhereInput | boolean
  delete?: Prisma.LabelWhereInput | boolean
  connect?: Prisma.LabelWhereUniqueInput
  update?: Prisma.XOR<Prisma.XOR<Prisma.LabelUpdateToOneWithWhereWithoutArtistsInput, Prisma.LabelUpdateWithoutArtistsInput>, Prisma.LabelUncheckedUpdateWithoutArtistsInput>
}

export type LabelCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.LabelCreateWithoutUserInput, Prisma.LabelUncheckedCreateWithoutUserInput> | Prisma.LabelCreateWithoutUserInput[] | Prisma.LabelUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.LabelCreateOrConnectWithoutUserInput | Prisma.LabelCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.LabelCreateManyUserInputEnvelope
  connect?: Prisma.LabelWhereUniqueInput | Prisma.LabelWhereUniqueInput[]
}

export type LabelUncheckedCreateNestedManyWithoutUserInput = {
  create?: Prisma.XOR<Prisma.LabelCreateWithoutUserInput, Prisma.LabelUncheckedCreateWithoutUserInput> | Prisma.LabelCreateWithoutUserInput[] | Prisma.LabelUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.LabelCreateOrConnectWithoutUserInput | Prisma.LabelCreateOrConnectWithoutUserInput[]
  createMany?: Prisma.LabelCreateManyUserInputEnvelope
  connect?: Prisma.LabelWhereUniqueInput | Prisma.LabelWhereUniqueInput[]
}

export type LabelUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.LabelCreateWithoutUserInput, Prisma.LabelUncheckedCreateWithoutUserInput> | Prisma.LabelCreateWithoutUserInput[] | Prisma.LabelUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.LabelCreateOrConnectWithoutUserInput | Prisma.LabelCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.LabelUpsertWithWhereUniqueWithoutUserInput | Prisma.LabelUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.LabelCreateManyUserInputEnvelope
  set?: Prisma.LabelWhereUniqueInput | Prisma.LabelWhereUniqueInput[]
  disconnect?: Prisma.LabelWhereUniqueInput | Prisma.LabelWhereUniqueInput[]
  delete?: Prisma.LabelWhereUniqueInput | Prisma.LabelWhereUniqueInput[]
  connect?: Prisma.LabelWhereUniqueInput | Prisma.LabelWhereUniqueInput[]
  update?: Prisma.LabelUpdateWithWhereUniqueWithoutUserInput | Prisma.LabelUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.LabelUpdateManyWithWhereWithoutUserInput | Prisma.LabelUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.LabelScalarWhereInput | Prisma.LabelScalarWhereInput[]
}

export type LabelUncheckedUpdateManyWithoutUserNestedInput = {
  create?: Prisma.XOR<Prisma.LabelCreateWithoutUserInput, Prisma.LabelUncheckedCreateWithoutUserInput> | Prisma.LabelCreateWithoutUserInput[] | Prisma.LabelUncheckedCreateWithoutUserInput[]
  connectOrCreate?: Prisma.LabelCreateOrConnectWithoutUserInput | Prisma.LabelCreateOrConnectWithoutUserInput[]
  upsert?: Prisma.LabelUpsertWithWhereUniqueWithoutUserInput | Prisma.LabelUpsertWithWhereUniqueWithoutUserInput[]
  createMany?: Prisma.LabelCreateManyUserInputEnvelope
  set?: Prisma.LabelWhereUniqueInput | Prisma.LabelWhereUniqueInput[]
  disconnect?: Prisma.LabelWhereUniqueInput | Prisma.LabelWhereUniqueInput[]
  delete?: Prisma.LabelWhereUniqueInput | Prisma.LabelWhereUniqueInput[]
  connect?: Prisma.LabelWhereUniqueInput | Prisma.LabelWhereUniqueInput[]
  update?: Prisma.LabelUpdateWithWhereUniqueWithoutUserInput | Prisma.LabelUpdateWithWhereUniqueWithoutUserInput[]
  updateMany?: Prisma.LabelUpdateManyWithWhereWithoutUserInput | Prisma.LabelUpdateManyWithWhereWithoutUserInput[]
  deleteMany?: Prisma.LabelScalarWhereInput | Prisma.LabelScalarWhereInput[]
}

export type LabelCreateWithoutArtistsInput = {
  id?: string
  name: string
  createdAt?: Date | string
  updatedAt?: Date | string
  user: Prisma.UserCreateNestedOneWithoutLabelsInput
}

export type LabelUncheckedCreateWithoutArtistsInput = {
  id?: string
  name: string
  userId: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type LabelCreateOrConnectWithoutArtistsInput = {
  where: Prisma.LabelWhereUniqueInput
  create: Prisma.XOR<Prisma.LabelCreateWithoutArtistsInput, Prisma.LabelUncheckedCreateWithoutArtistsInput>
}

export type LabelUpsertWithoutArtistsInput = {
  update: Prisma.XOR<Prisma.LabelUpdateWithoutArtistsInput, Prisma.LabelUncheckedUpdateWithoutArtistsInput>
  create: Prisma.XOR<Prisma.LabelCreateWithoutArtistsInput, Prisma.LabelUncheckedCreateWithoutArtistsInput>
  where?: Prisma.LabelWhereInput
}

export type LabelUpdateToOneWithWhereWithoutArtistsInput = {
  where?: Prisma.LabelWhereInput
  data: Prisma.XOR<Prisma.LabelUpdateWithoutArtistsInput, Prisma.LabelUncheckedUpdateWithoutArtistsInput>
}

export type LabelUpdateWithoutArtistsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  user?: Prisma.UserUpdateOneRequiredWithoutLabelsNestedInput
}

export type LabelUncheckedUpdateWithoutArtistsInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  userId?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type LabelCreateWithoutUserInput = {
  id?: string
  name: string
  createdAt?: Date | string
  updatedAt?: Date | string
  artists?: Prisma.ArtistCreateNestedManyWithoutLabelInput
}

export type LabelUncheckedCreateWithoutUserInput = {
  id?: string
  name: string
  createdAt?: Date | string
  updatedAt?: Date | string
  artists?: Prisma.ArtistUncheckedCreateNestedManyWithoutLabelInput
}

export type LabelCreateOrConnectWithoutUserInput = {
  where: Prisma.LabelWhereUniqueInput
  create: Prisma.XOR<Prisma.LabelCreateWithoutUserInput, Prisma.LabelUncheckedCreateWithoutUserInput>
}

export type LabelCreateManyUserInputEnvelope = {
  data: Prisma.LabelCreateManyUserInput | Prisma.LabelCreateManyUserInput[]
  skipDuplicates?: boolean
}

export type LabelUpsertWithWhereUniqueWithoutUserInput = {
  where: Prisma.LabelWhereUniqueInput
  update: Prisma.XOR<Prisma.LabelUpdateWithoutUserInput, Prisma.LabelUncheckedUpdateWithoutUserInput>
  create: Prisma.XOR<Prisma.LabelCreateWithoutUserInput, Prisma.LabelUncheckedCreateWithoutUserInput>
}

export type LabelUpdateWithWhereUniqueWithoutUserInput = {
  where: Prisma.LabelWhereUniqueInput
  data: Prisma.XOR<Prisma.LabelUpdateWithoutUserInput, Prisma.LabelUncheckedUpdateWithoutUserInput>
}

export type LabelUpdateManyWithWhereWithoutUserInput = {
  where: Prisma.LabelScalarWhereInput
  data: Prisma.XOR<Prisma.LabelUpdateManyMutationInput, Prisma.LabelUncheckedUpdateManyWithoutUserInput>
}

export type LabelScalarWhereInput = {
  AND?: Prisma.LabelScalarWhereInput | Prisma.LabelScalarWhereInput[]
  OR?: Prisma.LabelScalarWhereInput[]
  NOT?: Prisma.LabelScalarWhereInput | Prisma.LabelScalarWhereInput[]
  id?: Prisma.StringFilter<"Label"> | string
  name?: Prisma.StringFilter<"Label"> | string
  userId?: Prisma.StringFilter<"Label"> | string
  createdAt?: Prisma.DateTimeFilter<"Label"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"Label"> | Date | string
}

export type LabelCreateManyUserInput = {
  id?: string
  name: string
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type LabelUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  artists?: Prisma.ArtistUpdateManyWithoutLabelNestedInput
}

export type LabelUncheckedUpdateWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  artists?: Prisma.ArtistUncheckedUpdateManyWithoutLabelNestedInput
}

export type LabelUncheckedUpdateManyWithoutUserInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  name?: Prisma.StringFieldUpdateOperationsInput | string
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}


/**
 * Count Type LabelCountOutputType
 */

export type LabelCountOutputType = {
  artists: number
}

export type LabelCountOutputTypeSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  artists?: boolean | LabelCountOutputTypeCountArtistsArgs
}

/**
 * LabelCountOutputType without action
 */
export type LabelCountOutputTypeDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the LabelCountOutputType
   */
  select?: Prisma.LabelCountOutputTypeSelect<ExtArgs> | null
}

/**
 * LabelCountOutputType without action
 */
export type LabelCountOutputTypeCountArtistsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ArtistWhereInput
}


export type LabelSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  userId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  artists?: boolean | Prisma.Label$artistsArgs<ExtArgs>
  _count?: boolean | Prisma.LabelCountOutputTypeDefaultArgs<ExtArgs>
}, ExtArgs["result"]["label"]>

export type LabelSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  userId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}, ExtArgs["result"]["label"]>

export type LabelSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  name?: boolean
  userId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}, ExtArgs["result"]["label"]>

export type LabelSelectScalar = {
  id?: boolean
  name?: boolean
  userId?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type LabelOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "name" | "userId" | "createdAt" | "updatedAt", ExtArgs["result"]["label"]>
export type LabelInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
  artists?: boolean | Prisma.Label$artistsArgs<ExtArgs>
  _count?: boolean | Prisma.LabelCountOutputTypeDefaultArgs<ExtArgs>
}
export type LabelIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}
export type LabelIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  user?: boolean | Prisma.UserDefaultArgs<ExtArgs>
}

export type $LabelPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "Label"
  objects: {
    user: Prisma.$UserPayload<ExtArgs>
    artists: Prisma.$ArtistPayload<ExtArgs>[]
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    name: string
    userId: string
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["label"]>
  composites: {}
}

export type LabelGetPayload<S extends boolean | null | undefined | LabelDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$LabelPayload, S>

export type LabelCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<LabelFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: LabelCountAggregateInputType | true
  }

export interface LabelDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Label'], meta: { name: 'Label' } }
  /**
   * Find zero or one Label that matches the filter.
   * @param {LabelFindUniqueArgs} args - Arguments to find a Label
   * @example
   * // Get one Label
   * const label = await prisma.label.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends LabelFindUniqueArgs>(args: Prisma.SelectSubset<T, LabelFindUniqueArgs<ExtArgs>>): Prisma.Prisma__LabelClient<runtime.Types.Result.GetResult<Prisma.$LabelPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one Label that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {LabelFindUniqueOrThrowArgs} args - Arguments to find a Label
   * @example
   * // Get one Label
   * const label = await prisma.label.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends LabelFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, LabelFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__LabelClient<runtime.Types.Result.GetResult<Prisma.$LabelPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Label that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LabelFindFirstArgs} args - Arguments to find a Label
   * @example
   * // Get one Label
   * const label = await prisma.label.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends LabelFindFirstArgs>(args?: Prisma.SelectSubset<T, LabelFindFirstArgs<ExtArgs>>): Prisma.Prisma__LabelClient<runtime.Types.Result.GetResult<Prisma.$LabelPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first Label that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LabelFindFirstOrThrowArgs} args - Arguments to find a Label
   * @example
   * // Get one Label
   * const label = await prisma.label.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends LabelFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, LabelFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__LabelClient<runtime.Types.Result.GetResult<Prisma.$LabelPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more Labels that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LabelFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all Labels
   * const labels = await prisma.label.findMany()
   * 
   * // Get first 10 Labels
   * const labels = await prisma.label.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const labelWithIdOnly = await prisma.label.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends LabelFindManyArgs>(args?: Prisma.SelectSubset<T, LabelFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$LabelPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a Label.
   * @param {LabelCreateArgs} args - Arguments to create a Label.
   * @example
   * // Create one Label
   * const Label = await prisma.label.create({
   *   data: {
   *     // ... data to create a Label
   *   }
   * })
   * 
   */
  create<T extends LabelCreateArgs>(args: Prisma.SelectSubset<T, LabelCreateArgs<ExtArgs>>): Prisma.Prisma__LabelClient<runtime.Types.Result.GetResult<Prisma.$LabelPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many Labels.
   * @param {LabelCreateManyArgs} args - Arguments to create many Labels.
   * @example
   * // Create many Labels
   * const label = await prisma.label.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends LabelCreateManyArgs>(args?: Prisma.SelectSubset<T, LabelCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many Labels and returns the data saved in the database.
   * @param {LabelCreateManyAndReturnArgs} args - Arguments to create many Labels.
   * @example
   * // Create many Labels
   * const label = await prisma.label.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many Labels and only return the `id`
   * const labelWithIdOnly = await prisma.label.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends LabelCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, LabelCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$LabelPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a Label.
   * @param {LabelDeleteArgs} args - Arguments to delete one Label.
   * @example
   * // Delete one Label
   * const Label = await prisma.label.delete({
   *   where: {
   *     // ... filter to delete one Label
   *   }
   * })
   * 
   */
  delete<T extends LabelDeleteArgs>(args: Prisma.SelectSubset<T, LabelDeleteArgs<ExtArgs>>): Prisma.Prisma__LabelClient<runtime.Types.Result.GetResult<Prisma.$LabelPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one Label.
   * @param {LabelUpdateArgs} args - Arguments to update one Label.
   * @example
   * // Update one Label
   * const label = await prisma.label.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends LabelUpdateArgs>(args: Prisma.SelectSubset<T, LabelUpdateArgs<ExtArgs>>): Prisma.Prisma__LabelClient<runtime.Types.Result.GetResult<Prisma.$LabelPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more Labels.
   * @param {LabelDeleteManyArgs} args - Arguments to filter Labels to delete.
   * @example
   * // Delete a few Labels
   * const { count } = await prisma.label.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends LabelDeleteManyArgs>(args?: Prisma.SelectSubset<T, LabelDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Labels.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LabelUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many Labels
   * const label = await prisma.label.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends LabelUpdateManyArgs>(args: Prisma.SelectSubset<T, LabelUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more Labels and returns the data updated in the database.
   * @param {LabelUpdateManyAndReturnArgs} args - Arguments to update many Labels.
   * @example
   * // Update many Labels
   * const label = await prisma.label.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more Labels and only return the `id`
   * const labelWithIdOnly = await prisma.label.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends LabelUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, LabelUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$LabelPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one Label.
   * @param {LabelUpsertArgs} args - Arguments to update or create a Label.
   * @example
   * // Update or create a Label
   * const label = await prisma.label.upsert({
   *   create: {
   *     // ... data to create a Label
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the Label we want to update
   *   }
   * })
   */
  upsert<T extends LabelUpsertArgs>(args: Prisma.SelectSubset<T, LabelUpsertArgs<ExtArgs>>): Prisma.Prisma__LabelClient<runtime.Types.Result.GetResult<Prisma.$LabelPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of Labels.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LabelCountArgs} args - Arguments to filter Labels to count.
   * @example
   * // Count the number of Labels
   * const count = await prisma.label.count({
   *   where: {
   *     // ... the filter for the Labels we want to count
   *   }
   * })
  **/
  count<T extends LabelCountArgs>(
    args?: Prisma.Subset<T, LabelCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], LabelCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a Label.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LabelAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends LabelAggregateArgs>(args: Prisma.Subset<T, LabelAggregateArgs>): Prisma.PrismaPromise<GetLabelAggregateType<T>>

  /**
   * Group by Label.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {LabelGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends LabelGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: LabelGroupByArgs['orderBy'] }
      : { orderBy?: LabelGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, LabelGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetLabelGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the Label model
 */
readonly fields: LabelFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for Label.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__LabelClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  user<T extends Prisma.UserDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.UserDefaultArgs<ExtArgs>>): Prisma.Prisma__UserClient<runtime.Types.Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  artists<T extends Prisma.Label$artistsArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.Label$artistsArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ArtistPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the Label model
 */
export interface LabelFieldRefs {
  readonly id: Prisma.FieldRef<"Label", 'String'>
  readonly name: Prisma.FieldRef<"Label", 'String'>
  readonly userId: Prisma.FieldRef<"Label", 'String'>
  readonly createdAt: Prisma.FieldRef<"Label", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"Label", 'DateTime'>
}
    

// Custom InputTypes
/**
 * Label findUnique
 */
export type LabelFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Label
   */
  select?: Prisma.LabelSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Label
   */
  omit?: Prisma.LabelOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LabelInclude<ExtArgs> | null
  /**
   * Filter, which Label to fetch.
   */
  where: Prisma.LabelWhereUniqueInput
}

/**
 * Label findUniqueOrThrow
 */
export type LabelFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Label
   */
  select?: Prisma.LabelSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Label
   */
  omit?: Prisma.LabelOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LabelInclude<ExtArgs> | null
  /**
   * Filter, which Label to fetch.
   */
  where: Prisma.LabelWhereUniqueInput
}

/**
 * Label findFirst
 */
export type LabelFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Label
   */
  select?: Prisma.LabelSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Label
   */
  omit?: Prisma.LabelOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LabelInclude<ExtArgs> | null
  /**
   * Filter, which Label to fetch.
   */
  where?: Prisma.LabelWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Labels to fetch.
   */
  orderBy?: Prisma.LabelOrderByWithRelationInput | Prisma.LabelOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Labels.
   */
  cursor?: Prisma.LabelWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Labels from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Labels.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Labels.
   */
  distinct?: Prisma.LabelScalarFieldEnum | Prisma.LabelScalarFieldEnum[]
}

/**
 * Label findFirstOrThrow
 */
export type LabelFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Label
   */
  select?: Prisma.LabelSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Label
   */
  omit?: Prisma.LabelOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LabelInclude<ExtArgs> | null
  /**
   * Filter, which Label to fetch.
   */
  where?: Prisma.LabelWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Labels to fetch.
   */
  orderBy?: Prisma.LabelOrderByWithRelationInput | Prisma.LabelOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for Labels.
   */
  cursor?: Prisma.LabelWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Labels from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Labels.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of Labels.
   */
  distinct?: Prisma.LabelScalarFieldEnum | Prisma.LabelScalarFieldEnum[]
}

/**
 * Label findMany
 */
export type LabelFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Label
   */
  select?: Prisma.LabelSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Label
   */
  omit?: Prisma.LabelOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LabelInclude<ExtArgs> | null
  /**
   * Filter, which Labels to fetch.
   */
  where?: Prisma.LabelWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of Labels to fetch.
   */
  orderBy?: Prisma.LabelOrderByWithRelationInput | Prisma.LabelOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing Labels.
   */
  cursor?: Prisma.LabelWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` Labels from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` Labels.
   */
  skip?: number
  distinct?: Prisma.LabelScalarFieldEnum | Prisma.LabelScalarFieldEnum[]
}

/**
 * Label create
 */
export type LabelCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Label
   */
  select?: Prisma.LabelSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Label
   */
  omit?: Prisma.LabelOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LabelInclude<ExtArgs> | null
  /**
   * The data needed to create a Label.
   */
  data: Prisma.XOR<Prisma.LabelCreateInput, Prisma.LabelUncheckedCreateInput>
}

/**
 * Label createMany
 */
export type LabelCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many Labels.
   */
  data: Prisma.LabelCreateManyInput | Prisma.LabelCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * Label createManyAndReturn
 */
export type LabelCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Label
   */
  select?: Prisma.LabelSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Label
   */
  omit?: Prisma.LabelOmit<ExtArgs> | null
  /**
   * The data used to create many Labels.
   */
  data: Prisma.LabelCreateManyInput | Prisma.LabelCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LabelIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * Label update
 */
export type LabelUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Label
   */
  select?: Prisma.LabelSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Label
   */
  omit?: Prisma.LabelOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LabelInclude<ExtArgs> | null
  /**
   * The data needed to update a Label.
   */
  data: Prisma.XOR<Prisma.LabelUpdateInput, Prisma.LabelUncheckedUpdateInput>
  /**
   * Choose, which Label to update.
   */
  where: Prisma.LabelWhereUniqueInput
}

/**
 * Label updateMany
 */
export type LabelUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update Labels.
   */
  data: Prisma.XOR<Prisma.LabelUpdateManyMutationInput, Prisma.LabelUncheckedUpdateManyInput>
  /**
   * Filter which Labels to update
   */
  where?: Prisma.LabelWhereInput
  /**
   * Limit how many Labels to update.
   */
  limit?: number
}

/**
 * Label updateManyAndReturn
 */
export type LabelUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Label
   */
  select?: Prisma.LabelSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the Label
   */
  omit?: Prisma.LabelOmit<ExtArgs> | null
  /**
   * The data used to update Labels.
   */
  data: Prisma.XOR<Prisma.LabelUpdateManyMutationInput, Prisma.LabelUncheckedUpdateManyInput>
  /**
   * Filter which Labels to update
   */
  where?: Prisma.LabelWhereInput
  /**
   * Limit how many Labels to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LabelIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * Label upsert
 */
export type LabelUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Label
   */
  select?: Prisma.LabelSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Label
   */
  omit?: Prisma.LabelOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LabelInclude<ExtArgs> | null
  /**
   * The filter to search for the Label to update in case it exists.
   */
  where: Prisma.LabelWhereUniqueInput
  /**
   * In case the Label found by the `where` argument doesn't exist, create a new Label with this data.
   */
  create: Prisma.XOR<Prisma.LabelCreateInput, Prisma.LabelUncheckedCreateInput>
  /**
   * In case the Label was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.LabelUpdateInput, Prisma.LabelUncheckedUpdateInput>
}

/**
 * Label delete
 */
export type LabelDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Label
   */
  select?: Prisma.LabelSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Label
   */
  omit?: Prisma.LabelOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LabelInclude<ExtArgs> | null
  /**
   * Filter which Label to delete.
   */
  where: Prisma.LabelWhereUniqueInput
}

/**
 * Label deleteMany
 */
export type LabelDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which Labels to delete
   */
  where?: Prisma.LabelWhereInput
  /**
   * Limit how many Labels to delete.
   */
  limit?: number
}

/**
 * Label.artists
 */
export type Label$artistsArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Artist
   */
  select?: Prisma.ArtistSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Artist
   */
  omit?: Prisma.ArtistOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ArtistInclude<ExtArgs> | null
  where?: Prisma.ArtistWhereInput
  orderBy?: Prisma.ArtistOrderByWithRelationInput | Prisma.ArtistOrderByWithRelationInput[]
  cursor?: Prisma.ArtistWhereUniqueInput
  take?: number
  skip?: number
  distinct?: Prisma.ArtistScalarFieldEnum | Prisma.ArtistScalarFieldEnum[]
}

/**
 * Label without action
 */
export type LabelDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the Label
   */
  select?: Prisma.LabelSelect<ExtArgs> | null
  /**
   * Omit specific fields from the Label
   */
  omit?: Prisma.LabelOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.LabelInclude<ExtArgs> | null
}
