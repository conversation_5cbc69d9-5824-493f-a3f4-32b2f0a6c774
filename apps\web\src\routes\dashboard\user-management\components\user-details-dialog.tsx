import { useState } from "react";
import {
  CheckCircleIcon,
  XCircleIcon,
  ShieldIcon,
  UserIcon,
} from "lucide-react";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import type { UserTableItem } from "./types";

interface UserDetailsDialogProps {
  children: React.ReactNode;
  user: UserTableItem;
}

export function UserDetailsDialog({ children, user }: UserDetailsDialogProps) {
  const [open, setOpen] = useState(false);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>User Details</DialogTitle>
          <DialogDescription>
            Complete information about {user.name}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Basic Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <Label className="text-sm font-medium text-muted-foreground">
                  Full Name
                </Label>
                <p className="text-sm">{user.name}</p>
              </div>
              <div className="space-y-1">
                <Label className="text-sm font-medium text-muted-foreground">
                  Email
                </Label>
                <div className="flex items-center gap-2">
                  <p className="text-sm">{user.email}</p>
                  {user.emailVerified ? (
                    <CheckCircleIcon className="h-4 w-4 text-green-500" />
                  ) : (
                    <XCircleIcon className="h-4 w-4 text-red-500" />
                  )}
                </div>
              </div>
              <div className="space-y-1">
                <Label className="text-sm font-medium text-muted-foreground">
                  Phone
                </Label>
                <p className="text-sm">
                  {user.phone || (
                    <span className="text-muted-foreground italic">
                      Not provided
                    </span>
                  )}
                </p>
              </div>
              <div className="space-y-1">
                <Label className="text-sm font-medium text-muted-foreground">
                  Role
                </Label>
                <div className="flex items-center gap-2">
                  {user.role === "admin" ? (
                    <ShieldIcon className="h-4 w-4 text-blue-500" />
                  ) : (
                    <UserIcon className="h-4 w-4 text-gray-500" />
                  )}
                  <span className="text-sm capitalize">{user.displayRole}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Account Status */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Account Status</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <Label className="text-sm font-medium text-muted-foreground">
                  Status
                </Label>
                <Badge
                  className={cn(
                    user.status === "Banned" && "bg-red-500/10 text-red-700",
                    user.status === "Active" &&
                      "bg-green-500/10 text-green-700",
                    user.status === "Pending" &&
                      "bg-yellow-500/10 text-yellow-700"
                  )}
                >
                  {user.status}
                </Badge>
              </div>
              <div className="space-y-1">
                <Label className="text-sm font-medium text-muted-foreground">
                  Email Verified
                </Label>
                <p className="text-sm">
                  {user.emailVerified ? (
                    <span className="text-green-600">Yes</span>
                  ) : (
                    <span className="text-red-600">No</span>
                  )}
                </p>
              </div>
            </div>
          </div>

          {/* Ban Information (only show if user is banned) */}
          {user.banned && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Ban Information</h3>
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 space-y-3">
                <div className="space-y-1">
                  <Label className="text-sm font-medium text-red-800">
                    Ban Reason
                  </Label>
                  <p className="text-sm text-red-700">
                    {user.banReason || (
                      <span className="italic">No reason provided</span>
                    )}
                  </p>
                </div>
                {user.banExpires && (
                  <div className="space-y-1">
                    <Label className="text-sm font-medium text-red-800">
                      Ban Expires
                    </Label>
                    <p className="text-sm text-red-700">
                      {new Date(user.banExpires).toLocaleString()}
                    </p>
                  </div>
                )}
                {!user.banExpires && (
                  <div className="space-y-1">
                    <Label className="text-sm font-medium text-red-800">
                      Ban Type
                    </Label>
                    <p className="text-sm text-red-700">Permanent</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Account Timestamps */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Account Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <Label className="text-sm font-medium text-muted-foreground">
                  Created At
                </Label>
                <p className="text-sm">
                  {new Date(user.createdAt).toLocaleString()}
                </p>
              </div>
              <div className="space-y-1">
                <Label className="text-sm font-medium text-muted-foreground">
                  Last Updated
                </Label>
                <p className="text-sm">
                  {new Date(user.updatedAt).toLocaleString()}
                </p>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
