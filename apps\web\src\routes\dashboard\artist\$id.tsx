import { createFile<PERSON>out<PERSON>, <PERSON> } from "@tanstack/react-router";
import { useQuery } from "@tanstack/react-query";
import {
  ArrowLeft,
  Edit,
  Instagram,
  MapPin,
  Music,
  Tag,
  Building,
  UserIcon,
  MusicIcon,
  MailIcon,
  CalendarIcon,
  FileText,
} from "lucide-react";
import { toast } from "sonner";

import { useTRPCClient } from "@/utils/trpc";
import { authClient } from "@/lib/auth-client";
import Loader from "@/components/loader";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { UpdateArtistDialog } from "@/routes/dashboard/artist/components/update-artist-dialog";
import { capitalizeGenre, type Genre } from "@/lib/genre";
import type { ArtistTableItem } from "@/routes/dashboard/artist/components/artist-table";
import { PlatformBadge } from "@/components/platform-badge";

export const Route = createFileRoute("/dashboard/artist/$id")({
  component: ArtistDetailsPage,
  head: ({ params }) => ({
    meta: [
      {
        title: `Artist Details - ${params.id} - Soundmera App`,
      },
    ],
  }),
});

function ArtistDetailsPage() {
  const { id } = Route.useParams();
  const navigate = Route.useNavigate();
  const trpcClient = useTRPCClient();
  const { data: session, isPending: sessionPending } = authClient.useSession();
  const isAdmin = session?.user?.role === "admin";

  // Fetch artist details
  const {
    data: artist,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["artist", id],
    queryFn: async () => {
      const response = await trpcClient.artist.getById.query({ id });
      // Transform the response to match ArtistTableItem type
      return {
        ...response,
        createdAt: new Date(response.createdAt),
        updatedAt: new Date(response.updatedAt),
      } as ArtistTableItem;
    },
    enabled: !!id,
  });

  // Authentication check
  if (sessionPending) {
    return <Loader />;
  }

  if (!session || !session.user) {
    navigate({ to: "/auth" });
    return <Loader />;
  }

  if (isLoading) {
    return <Loader />;
  }

  if (error) {
    toast.error("Failed to load artist details");
    return (
      <div className="flex flex-col items-center justify-center h-96 space-y-4">
        <p className="text-muted-foreground">Failed to load artist details</p>
        <Button onClick={() => refetch()}>Try Again</Button>
      </div>
    );
  }

  if (!artist) {
    return (
      <div className="flex flex-col items-center justify-center h-96 space-y-4">
        <p className="text-muted-foreground">Artist not found</p>
        <Button onClick={() => navigate({ to: "/dashboard/artist" })}>
          Back to Artists
        </Button>
      </div>
    );
  }

  const handleArtistUpdated = () => {
    refetch();
    toast.success("Artist updated successfully");
  };

  return (
    <div className="container mx-auto p-2 space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <Link
          to="/dashboard/artist"
          className="inline-flex items-center text-sm text-muted-foreground hover:text-foreground hover:underline mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Artists
        </Link>
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">{artist.name}</h1>
            <p className="text-muted-foreground text-sm">Artist Details</p>
          </div>
          <UpdateArtistDialog
            artist={artist}
            onArtistUpdated={handleArtistUpdated}
          >
            <Button>
              <Edit className="h-4 w-4" />
              Edit Artist
            </Button>
          </UpdateArtistDialog>
        </div>
      </div>

      {/* Bento Grid Layout */}
      <div className="grid grid-cols-1 md:grid-cols-[2fr_1fr] gap-2">
        {/* Basic Information */}
        <Card className="gap-2 overflow-hidden">
          <CardHeader>
            <CardTitle className="text-xl">Basic Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="grid grid-cols-[120px_1fr] gap-4 md:gap-20 items-start">
                <div className="flex items-center gap-1">
                  <UserIcon className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">
                    Artist Name
                  </span>
                </div>
                <span className="break-all">{artist.name}</span>
              </div>

              <div className="grid grid-cols-[120px_1fr] gap-4 md:gap-20 items-start">
                <div className="flex items-center gap-1">
                  <MusicIcon className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">Genre</span>
                </div>
                <span className="break-all">
                  {artist.genre ? capitalizeGenre(artist.genre as Genre) : "-"}
                </span>
              </div>

              <div className="grid grid-cols-[120px_1fr] gap-4 md:gap-20 items-start">
                <div className="flex items-center gap-1">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">Country</span>
                </div>
                <span className="break-all">{artist.country || "-"}</span>
              </div>

              <div className="grid grid-cols-[120px_1fr] gap-4 md:gap-20 items-start">
                <div className="flex items-center gap-1">
                  <Building className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">Label</span>
                </div>
                <span className="break-all">{artist.label?.name || "-"}</span>
              </div>

              <div className="grid grid-cols-[120px_1fr] gap-4 md:gap-20 items-start">
                <div className="flex items-center gap-1">
                  <Instagram className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">
                    Instagram
                  </span>
                </div>
                <span className="break-all">
                  {artist.instagram ? (
                    <a
                      href={`https://instagram.com/${artist.instagram}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:underline break-all"
                    >
                      {artist.instagram}
                    </a>
                  ) : (
                    "-"
                  )}
                </span>
              </div>

              <div className="grid grid-cols-[120px_1fr] gap-4 md:gap-20 items-start">
                <div className="flex items-center gap-1">
                  <FileText className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">
                    Biography
                  </span>
                </div>
                <span className="break-all">{artist.biography}</span>
              </div>

              {isAdmin && (
                <>
                  <div className="border-t pt-3 mt-3 space-y-2">
                    <div className="grid grid-cols-[120px_1fr] gap-4 md:gap-20 items-start">
                      <div className="flex items-center gap-1">
                        <UserIcon className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm text-muted-foreground">
                          Created By
                        </span>
                      </div>
                      <span className="break-all">{artist.user.name}</span>
                    </div>

                    <div className="grid grid-cols-[120px_1fr] gap-4 md:gap-20 items-start">
                      <div className="flex items-center gap-1">
                        <MailIcon className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm text-muted-foreground">
                          Email
                        </span>
                      </div>
                      <span className="break-all">{artist.user.email}</span>
                    </div>

                    <div className="grid grid-cols-[120px_1fr] gap-4 md:gap-20 items-start">
                      <div className="flex items-center gap-1">
                        <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm text-muted-foreground">
                          Created Date
                        </span>
                      </div>
                      <span className="break-all">
                        {artist.createdAt.toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Platforms */}
        <Card className="overflow-hidden gap-2">
          <CardHeader>
            <CardTitle>Platforms</CardTitle>
          </CardHeader>
          <CardContent>
            {artist.identifiers && artist.identifiers.length > 0 ? (
              <div className="flex flex-col gap-3">
                {artist.identifiers.map((identifier, index) => (
                  <PlatformBadge
                    key={index}
                    service={identifier.service}
                    identifier={identifier.identifier}
                    width={20}
                    height={20}
                    className="text-sm justify-start w-full h-10"
                  />
                ))}
              </div>
            ) : (
              <p className="text-muted-foreground">No platforms configured</p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Projects Section */}
      <Card>
        <CardHeader>
          <CardTitle>Projects (to-do later)</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            This section will contain artist projects and releases.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
