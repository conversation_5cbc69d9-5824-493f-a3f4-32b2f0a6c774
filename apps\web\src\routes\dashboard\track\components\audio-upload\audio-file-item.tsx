import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import {
  formatFileSize,
  type FileState,
} from "@/components/upload/uploader-provider";
import { AudioPlayer } from "./audio-player";
import { DeleteConfirmDialog } from "./delete-confirm-dialog";
import { Music, AlertCircle, X, Check, Trash2 } from "lucide-react";

interface AudioFileItemProps {
  fileState: FileState;
  onRemove: () => void;
  onDelete: () => void;
  onCancel: () => void;
}

export const AudioFileItem: React.FC<AudioFileItemProps> = ({
  fileState,
  onRemove,
  onDelete,
  onCancel,
}) => {
  const { file, status, progress, error, url } = fileState;
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  // Create preview URL for pending files
  useEffect(() => {
    if (status === "PENDING" || status === "ERROR") {
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);

      return () => {
        URL.revokeObjectURL(objectUrl);
      };
    } else {
      setPreviewUrl(null);
    }
  }, [file, status]);

  const getStatusIcon = () => {
    switch (status) {
      case "UPLOADING":
        return (
          <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-500 border-t-transparent" />
        );
      case "COMPLETE":
        return <Check className="h-4 w-4 text-green-500" />;
      case "ERROR":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Music className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case "UPLOADING":
        return "border-border bg-muted/50";
      case "COMPLETE":
        return "border-border bg-background";
      case "ERROR":
        return "border-destructive/20 bg-destructive/5";
      default:
        return "border-border bg-muted/20";
    }
  };

  const shouldShowPlayer =
    ((status === "PENDING" || status === "ERROR") && previewUrl) ||
    (status === "COMPLETE" && url);
  const audioSrc = previewUrl || url || "";
  const isPreview = status === "PENDING" || status === "ERROR";

  return (
    <div className={cn("p-4 rounded-lg border", getStatusColor())}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3 min-w-0 flex-1">
          {getStatusIcon()}
          <div className="min-w-0 flex-1">
            <p className="text-sm font-medium truncate">{file.name}</p>
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <span>{formatFileSize(file.size)}</span>
              {status === "COMPLETE" && (
                <>
                  <span>•</span>
                  <span className="text-green-600">Uploaded</span>
                </>
              )}
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {status === "UPLOADING" && (
            <div className="flex items-center gap-2">
              <div className="w-20">
                <Progress value={progress} className="h-2" />
              </div>
              <span className="text-xs text-muted-foreground w-10">
                {Math.round(progress)}%
              </span>
              <Button
                variant="ghost"
                size="sm"
                onClick={onCancel}
                className="h-6 w-6 p-0"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          )}

          {(status === "PENDING" || status === "ERROR") && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onRemove}
              className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
              title="Remove file"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          )}

          {status === "COMPLETE" && (
            <DeleteConfirmDialog fileName={file.name} onConfirm={onDelete}>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                title="Delete uploaded file"
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </DeleteConfirmDialog>
          )}
        </div>
      </div>

      {/* Audio Player for both preview and uploaded files */}
      {shouldShowPlayer && (
        <div className="mt-3 pt-3 border-t border-border">
          <AudioPlayer
            src={audioSrc}
            fileName={file.name}
            isPreview={isPreview}
          />
        </div>
      )}

      {error && <div className="mt-2 text-xs text-destructive">{error}</div>}
    </div>
  );
};
