import type { ColumnDef, FilterFn } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import type { TrackTableItem } from "./track-table";
import { capitalizeGenre } from "@/lib/genre";
import { RowActions } from "./row-actions";

// Custom filter functions
export const multiColumnFilterFn: FilterFn<TrackTableItem> = (
  row,
  columnId,
  filterValue
) => {
  const searchableRowContent = `${row.original.title} ${row.original.isrc || ""} ${row.original.genre}`.toLowerCase();
  const searchTerm = (filterValue ?? "").toLowerCase();
  return searchableRowContent.includes(searchTerm);
};

export const statusFilterFn: FilterFn<TrackTableItem> = (
  row,
  columnId,
  filterValue: string[]
) => {
  if (!filterValue?.length) return true;
  const status = row.getValue(columnId) as string;
  return filterValue.includes(status);
};

export const createColumns = (
  onTrackDeleted: () => void,
  isAdmin: boolean
): ColumnDef<TrackTableItem>[] => {
  const baseColumns: ColumnDef<TrackTableItem>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      size: 28,
      enableSorting: false,
      enableHiding: false,
    },
    {
      header: "Track Title",
      accessorKey: "title",
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue("title")}</div>
      ),
      size: 200,
      filterFn: multiColumnFilterFn,
      enableHiding: false,
    },
    {
      header: "Status",
      accessorKey: "status",
      cell: ({ row }) => {
        const status = row.getValue("status") as string;
        return (
          <Badge variant={status === "READY" ? "default" : "secondary"}>
            {status}
          </Badge>
        );
      },
      size: 100,
      filterFn: statusFilterFn,
    },
    {
      header: "Genre",
      accessorKey: "genre",
      cell: ({ row }) => (
        <div className="text-sm">
          <Badge variant="outline">
            {capitalizeGenre(row.getValue("genre"))}
          </Badge>
        </div>
      ),
      size: 120,
    },
    {
      header: "Artists",
      accessorKey: "artists",
      cell: ({ row }) => {
        const artists = row.original.artists || [];
        if (artists.length === 0) {
          return (
            <span className="text-muted-foreground text-sm">No artists</span>
          );
        }
        const primaryArtists = artists.filter(a => a.role === "PRIMARY");
        const featuringArtists = artists.filter(a => a.role === "FEATURING");
        
        return (
          <div className="text-sm">
            {primaryArtists.map(a => a.artist.name).join(", ")}
            {featuringArtists.length > 0 && (
              <span className="text-muted-foreground">
                {" feat. " + featuringArtists.map(a => a.artist.name).join(", ")}
              </span>
            )}
          </div>
        );
      },
      size: 180,
      enableSorting: false,
    },
    {
      header: "Recording Year",
      accessorKey: "recordingYear",
      cell: ({ row }) => (
        <div className="text-sm">{row.getValue("recordingYear")}</div>
      ),
      size: 120,
    },
    {
      header: "Publishing Year",
      accessorKey: "publishingYear",
      cell: ({ row }) => (
        <div className="text-sm">{row.getValue("publishingYear")}</div>
      ),
      size: 120,
    },
    {
      header: "ISRC",
      accessorKey: "isrc",
      cell: ({ row }) => (
        <div className="text-sm font-mono">
          {row.getValue("isrc") || (
            <span className="text-muted-foreground">Not specified</span>
          )}
        </div>
      ),
      size: 150,
    },
    {
      header: "Track Version",
      accessorKey: "trackVersion",
      cell: ({ row }) => (
        <div className="text-sm">
          {row.getValue("trackVersion") || (
            <span className="text-muted-foreground">Not specified</span>
          )}
        </div>
      ),
      size: 120,
    },
    {
      header: "Sub Genre",
      accessorKey: "subGenre",
      cell: ({ row }) => (
        <div className="text-sm">
          {row.getValue("subGenre") || (
            <span className="text-muted-foreground">Not specified</span>
          )}
        </div>
      ),
      size: 120,
    },
    {
      header: "Explicit",
      accessorKey: "explicit",
      cell: ({ row }) => {
        const explicit = row.getValue("explicit") as string;
        return (
          <Badge 
            variant={explicit === "EXPLICIT" ? "destructive" : explicit === "CLEAN" ? "default" : "secondary"}
          >
            {explicit.replace("_", " ")}
          </Badge>
        );
      },
      size: 100,
    },
    {
      header: "Publishing Holder",
      accessorKey: "publishingHolder",
      cell: ({ row }) => (
        <div className="text-sm">{row.getValue("publishingHolder")}</div>
      ),
      size: 150,
    },
    {
      header: "Lyrics",
      accessorKey: "lyrics",
      cell: ({ row }) => {
        const lyrics = row.getValue("lyrics") as string;
        return (
          <div className="text-sm">
            {lyrics ? (
              <span className="text-green-600">Available</span>
            ) : (
              <span className="text-muted-foreground">Not provided</span>
            )}
          </div>
        );
      },
      size: 100,
      enableSorting: false,
    },
    {
      header: "Preview Start",
      accessorKey: "previewStart",
      cell: ({ row }) => (
        <div className="text-sm font-mono">
          {row.getValue("previewStart") || (
            <span className="text-muted-foreground">Not set</span>
          )}
        </div>
      ),
      size: 100,
    },
    {
      header: "Preview Length",
      accessorKey: "previewLength",
      cell: ({ row }) => (
        <div className="text-sm font-mono">
          {row.getValue("previewLength") || (
            <span className="text-muted-foreground">Not set</span>
          )}
        </div>
      ),
      size: 100,
    },
    {
      header: "Metadata Language",
      accessorKey: "metadataLanguage",
      cell: ({ row }) => (
        <div className="text-sm uppercase">{row.getValue("metadataLanguage")}</div>
      ),
      size: 120,
    },
    {
      header: "Audio Language",
      accessorKey: "audioLanguage",
      cell: ({ row }) => (
        <div className="text-sm uppercase">{row.getValue("audioLanguage")}</div>
      ),
      size: 120,
    },
    {
      header: "Rights Claim",
      accessorKey: "rightsClaim",
      cell: ({ row }) => {
        const rightsClaim = row.getValue("rightsClaim") as string;
        return (
          <Badge variant="outline">
            {rightsClaim.replace("_", " ")}
          </Badge>
        );
      },
      size: 120,
    },
    {
      header: "Submitted At",
      accessorKey: "submittedAt",
      cell: ({ row }) => {
        const date = row.getValue("submittedAt") as Date | null;
        return (
          <div className="text-sm text-muted-foreground">
            {date ? date.toLocaleDateString() : "Not submitted"}
          </div>
        );
      },
      size: 120,
    },
    {
      header: "Ready At",
      accessorKey: "readyAt",
      cell: ({ row }) => {
        const date = row.getValue("readyAt") as Date | null;
        return (
          <div className="text-sm text-muted-foreground">
            {date ? date.toLocaleDateString() : "Not ready"}
          </div>
        );
      },
      size: 120,
    },
    {
      header: "Created At",
      accessorKey: "createdAt",
      cell: ({ row }) => {
        const date = new Date(row.getValue("createdAt"));
        return (
          <div className="text-sm text-muted-foreground">
            {date.toLocaleDateString()}
          </div>
        );
      },
      size: 120,
    },
  ];

  baseColumns.push({
    id: "actions",
    header: () => <span className="sr-only">Actions</span>,
    cell: ({ row }) => (
      <RowActions row={row} onTrackDeleted={onTrackDeleted} />
    ),
    size: 80,
    enableHiding: false,
  });

  // Insert Created By column for admins before Actions
  if (isAdmin) {
    baseColumns.splice(baseColumns.length - 1, 0, {
      header: "Created By",
      accessorKey: "user",
      cell: ({ row }) => (
        <div className="text-sm min-w-0 truncate">
          {row.original.user?.email ?? "-"} | {row.original.user?.name ?? "-"}
        </div>
      ),
      size: 200,
    });
  }

  return baseColumns;
};
