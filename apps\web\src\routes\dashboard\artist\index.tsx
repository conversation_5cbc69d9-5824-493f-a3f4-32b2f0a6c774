import { createFileRoute } from "@tanstack/react-router";
import Loader from "@/components/loader";
import { authClient } from "@/lib/auth-client";
import { useEffect } from "react";
import ArtistTable from "./components/artist-table";

export const Route = createFileRoute("/dashboard/artist/")({
  component: RouteComponent,
  head: () => ({
    meta: [
      {
        title: "Artist Management - Soundmera App",
      },
    ],
  }),
});

function RouteComponent() {
  const navigate = Route.useNavigate();
  const { data: session, isPending } = authClient.useSession();

  useEffect(() => {
    if (!session && !isPending) {
      navigate({
        to: "/auth",
      });
      return;
    }
  }, [session, isPending, navigate]);

  if (isPending) {
    return <Loader />;
  }

  if (!session || !session.user) {
    return <Loader />;
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="pl-2 text-xl font-bold">Artist Management</div>
      <div className="p-2">
        <ArtistTable />
      </div>
    </div>
  );
}
