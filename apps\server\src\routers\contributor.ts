import { protectedProcedure, router } from "@/lib/trpc";
import { z } from "zod";
import prisma from "../../prisma/index";
import { TRPCError } from "@trpc/server";

// Validation schemas
const CreateContributorSchema = z.object({
  name: z
    .string()
    .min(1, "Contributor name is required")
    .max(100, "Name too long"),
});

const UpdateContributorSchema = z.object({
  id: z.string().uuid(),
  name: z
    .string()
    .min(1, "Contributor name is required")
    .max(100, "Name too long"),
});

const GetContributorSchema = z.object({
  id: z.string().uuid(),
});

const GetContributorsSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  search: z.string().optional(),
});

const DeleteContributorSchema = z.object({
  id: z.string().uuid(),
});

export const contributorRouter = router({
  // Create a new contributor
  create: protectedProcedure
    .input(CreateContributorSchema)
    .mutation(async ({ ctx, input }) => {
      const { session } = ctx;

      try {
        // Check if contributor name already exists for this user
        const existingContributor = await prisma.contributor.findFirst({
          where: {
            userId: session.user.id,
            name: input.name,
          },
        });

        if (existingContributor) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "You already have a contributor with this name",
          });
        }

        const contributor = await prisma.contributor.create({
          data: {
            name: input.name,
            userId: session.user.id,
          },
          include: {
            user: true,
          },
        });

        return contributor;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create contributor",
          cause: error,
        });
      }
    }),

  // Get a single contributor by ID
  getById: protectedProcedure
    .input(GetContributorSchema)
    .query(async ({ ctx, input }) => {
      const { session } = ctx;
      const isAdmin = session.user.role === "admin";

      try {
        const contributor = await prisma.contributor.findUnique({
          where: {
            id: input.id,
          },
          include: {
            user: true,
          },
        });

        if (!contributor) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Contributor not found",
          });
        }

        // Check if user owns this contributor
        if (contributor.userId !== session.user.id && !isAdmin) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "Not authorized to view this contributor",
          });
        }

        return contributor;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to get contributor",
          cause: error,
        });
      }
    }),

  // Get all contributors for the current user with pagination and filtering
  getAll: protectedProcedure
    .input(GetContributorsSchema)
    .query(async ({ ctx, input }) => {
      const { session } = ctx;
      const isAdmin = session.user.role === "admin";
      const { page, limit, search } = input;
      const skip = (page - 1) * limit;

      try {
        const where = {
          ...(isAdmin ? {} : { userId: session.user.id }),
          ...(search && {
            name: {
              contains: search,
              mode: "insensitive" as const,
            },
          }),
        };

        const [contributors, total] = await Promise.all([
          prisma.contributor.findMany({
            where,
            include: {
              user: true,
            },
            orderBy: {
              createdAt: "desc",
            },
            skip,
            take: limit,
          }),
          prisma.contributor.count({ where }),
        ]);

        return {
          contributors,
          pagination: {
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
          },
        };
      } catch (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to get contributors",
          cause: error,
        });
      }
    }),

  // Update a contributor
  update: protectedProcedure
    .input(UpdateContributorSchema)
    .mutation(async ({ ctx, input }) => {
      const { session } = ctx;
      const isAdmin = session.user.role === "admin";
      const { id, name } = input;

      try {
        // Check if contributor exists and belongs to user
        const existingContributor = await prisma.contributor.findUnique({
          where: { id },
        });

        if (!existingContributor) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Contributor not found",
          });
        }

        if (existingContributor.userId !== session.user.id && !isAdmin) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "Not authorized to update this contributor",
          });
        }

        // Check for name conflicts if name is being updated
        if (name !== existingContributor.name) {
          const nameConflict = await prisma.contributor.findFirst({
            where: {
              userId: session.user.id,
              name: name,
              id: { not: id },
            },
          });

          if (nameConflict) {
            throw new TRPCError({
              code: "CONFLICT",
              message: "You already have a contributor with this name",
            });
          }
        }

        const updatedContributor = await prisma.contributor.update({
          where: { id },
          data: { name },
          include: {
            user: true,
          },
        });

        return updatedContributor;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update contributor",
          cause: error,
        });
      }
    }),

  // Delete a contributor
  delete: protectedProcedure
    .input(DeleteContributorSchema)
    .mutation(async ({ ctx, input }) => {
      const { session } = ctx;
      const isAdmin = session.user.role === "admin";

      try {
        // Check if contributor exists and belongs to user
        const existingContributor = await prisma.contributor.findUnique({
          where: { id: input.id },
        });

        if (!existingContributor) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Contributor not found",
          });
        }

        if (existingContributor.userId !== session.user.id && !isAdmin) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "Not authorized to delete this contributor",
          });
        }

        // Delete contributor
        await prisma.contributor.delete({
          where: { id: input.id },
        });

        return { success: true, message: "Contributor deleted successfully" };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete contributor",
          cause: error,
        });
      }
    }),

  // Get all contributors for dropdown/select (minimal data)
  getForSelect: protectedProcedure.query(async ({ ctx }) => {
    const { session } = ctx;
    const isAdmin = session.user.role === "admin";

    try {
      const contributors = await prisma.contributor.findMany({
        where: {
          ...(isAdmin ? {} : { userId: session.user.id }),
        },
        select: {
          id: true,
          name: true,
        },
        orderBy: {
          name: "asc",
        },
      });

      return contributors;
    } catch (error) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to get contributors for select",
        cause: error,
      });
    }
  }),
});
