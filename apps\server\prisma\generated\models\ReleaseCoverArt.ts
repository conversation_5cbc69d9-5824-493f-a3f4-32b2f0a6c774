
/* !!! This is code generated by <PERSON>risma. Do not edit directly. !!! */
/* eslint-disable */
// @ts-nocheck 
/**
 * This file exports the `ReleaseCoverArt` model and its related types.
 *
 * 🟢 You can import this file directly.
 */
import * as runtime from "@prisma/client/runtime/library"
import type * as $Enums from "../enums"
import type * as Prisma from "../internal/prismaNamespace"

/**
 * Model ReleaseCoverArt
 * 
 */
export type ReleaseCoverArtModel = runtime.Types.Result.DefaultSelection<Prisma.$ReleaseCoverArtPayload>

export type AggregateReleaseCoverArt = {
  _count: ReleaseCoverArtCountAggregateOutputType | null
  _avg: ReleaseCoverArtAvgAggregateOutputType | null
  _sum: ReleaseCoverArtSumAggregateOutputType | null
  _min: ReleaseCoverArtMinAggregateOutputType | null
  _max: ReleaseCoverArtMaxAggregateOutputType | null
}

export type ReleaseCoverArtAvgAggregateOutputType = {
  fileSize: number | null
  width: number | null
  height: number | null
}

export type ReleaseCoverArtSumAggregateOutputType = {
  fileSize: number | null
  width: number | null
  height: number | null
}

export type ReleaseCoverArtMinAggregateOutputType = {
  id: string | null
  releaseId: string | null
  imageUrl: string | null
  imageKey: string | null
  fileName: string | null
  fileSize: number | null
  mimeType: string | null
  width: number | null
  height: number | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type ReleaseCoverArtMaxAggregateOutputType = {
  id: string | null
  releaseId: string | null
  imageUrl: string | null
  imageKey: string | null
  fileName: string | null
  fileSize: number | null
  mimeType: string | null
  width: number | null
  height: number | null
  createdAt: Date | null
  updatedAt: Date | null
}

export type ReleaseCoverArtCountAggregateOutputType = {
  id: number
  releaseId: number
  imageUrl: number
  imageKey: number
  fileName: number
  fileSize: number
  mimeType: number
  width: number
  height: number
  createdAt: number
  updatedAt: number
  _all: number
}


export type ReleaseCoverArtAvgAggregateInputType = {
  fileSize?: true
  width?: true
  height?: true
}

export type ReleaseCoverArtSumAggregateInputType = {
  fileSize?: true
  width?: true
  height?: true
}

export type ReleaseCoverArtMinAggregateInputType = {
  id?: true
  releaseId?: true
  imageUrl?: true
  imageKey?: true
  fileName?: true
  fileSize?: true
  mimeType?: true
  width?: true
  height?: true
  createdAt?: true
  updatedAt?: true
}

export type ReleaseCoverArtMaxAggregateInputType = {
  id?: true
  releaseId?: true
  imageUrl?: true
  imageKey?: true
  fileName?: true
  fileSize?: true
  mimeType?: true
  width?: true
  height?: true
  createdAt?: true
  updatedAt?: true
}

export type ReleaseCoverArtCountAggregateInputType = {
  id?: true
  releaseId?: true
  imageUrl?: true
  imageKey?: true
  fileName?: true
  fileSize?: true
  mimeType?: true
  width?: true
  height?: true
  createdAt?: true
  updatedAt?: true
  _all?: true
}

export type ReleaseCoverArtAggregateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which ReleaseCoverArt to aggregate.
   */
  where?: Prisma.ReleaseCoverArtWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ReleaseCoverArts to fetch.
   */
  orderBy?: Prisma.ReleaseCoverArtOrderByWithRelationInput | Prisma.ReleaseCoverArtOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the start position
   */
  cursor?: Prisma.ReleaseCoverArtWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ReleaseCoverArts from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ReleaseCoverArts.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Count returned ReleaseCoverArts
  **/
  _count?: true | ReleaseCoverArtCountAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to average
  **/
  _avg?: ReleaseCoverArtAvgAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to sum
  **/
  _sum?: ReleaseCoverArtSumAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the minimum value
  **/
  _min?: ReleaseCoverArtMinAggregateInputType
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
   * 
   * Select which fields to find the maximum value
  **/
  _max?: ReleaseCoverArtMaxAggregateInputType
}

export type GetReleaseCoverArtAggregateType<T extends ReleaseCoverArtAggregateArgs> = {
      [P in keyof T & keyof AggregateReleaseCoverArt]: P extends '_count' | 'count'
    ? T[P] extends true
      ? number
      : Prisma.GetScalarType<T[P], AggregateReleaseCoverArt[P]>
    : Prisma.GetScalarType<T[P], AggregateReleaseCoverArt[P]>
}




export type ReleaseCoverArtGroupByArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  where?: Prisma.ReleaseCoverArtWhereInput
  orderBy?: Prisma.ReleaseCoverArtOrderByWithAggregationInput | Prisma.ReleaseCoverArtOrderByWithAggregationInput[]
  by: Prisma.ReleaseCoverArtScalarFieldEnum[] | Prisma.ReleaseCoverArtScalarFieldEnum
  having?: Prisma.ReleaseCoverArtScalarWhereWithAggregatesInput
  take?: number
  skip?: number
  _count?: ReleaseCoverArtCountAggregateInputType | true
  _avg?: ReleaseCoverArtAvgAggregateInputType
  _sum?: ReleaseCoverArtSumAggregateInputType
  _min?: ReleaseCoverArtMinAggregateInputType
  _max?: ReleaseCoverArtMaxAggregateInputType
}

export type ReleaseCoverArtGroupByOutputType = {
  id: string
  releaseId: string
  imageUrl: string
  imageKey: string
  fileName: string | null
  fileSize: number | null
  mimeType: string | null
  width: number | null
  height: number | null
  createdAt: Date
  updatedAt: Date
  _count: ReleaseCoverArtCountAggregateOutputType | null
  _avg: ReleaseCoverArtAvgAggregateOutputType | null
  _sum: ReleaseCoverArtSumAggregateOutputType | null
  _min: ReleaseCoverArtMinAggregateOutputType | null
  _max: ReleaseCoverArtMaxAggregateOutputType | null
}

type GetReleaseCoverArtGroupByPayload<T extends ReleaseCoverArtGroupByArgs> = Prisma.PrismaPromise<
  Array<
    Prisma.PickEnumerable<ReleaseCoverArtGroupByOutputType, T['by']> &
      {
        [P in ((keyof T) & (keyof ReleaseCoverArtGroupByOutputType))]: P extends '_count'
          ? T[P] extends boolean
            ? number
            : Prisma.GetScalarType<T[P], ReleaseCoverArtGroupByOutputType[P]>
          : Prisma.GetScalarType<T[P], ReleaseCoverArtGroupByOutputType[P]>
      }
    >
  > 



export type ReleaseCoverArtWhereInput = {
  AND?: Prisma.ReleaseCoverArtWhereInput | Prisma.ReleaseCoverArtWhereInput[]
  OR?: Prisma.ReleaseCoverArtWhereInput[]
  NOT?: Prisma.ReleaseCoverArtWhereInput | Prisma.ReleaseCoverArtWhereInput[]
  id?: Prisma.StringFilter<"ReleaseCoverArt"> | string
  releaseId?: Prisma.StringFilter<"ReleaseCoverArt"> | string
  imageUrl?: Prisma.StringFilter<"ReleaseCoverArt"> | string
  imageKey?: Prisma.StringFilter<"ReleaseCoverArt"> | string
  fileName?: Prisma.StringNullableFilter<"ReleaseCoverArt"> | string | null
  fileSize?: Prisma.IntNullableFilter<"ReleaseCoverArt"> | number | null
  mimeType?: Prisma.StringNullableFilter<"ReleaseCoverArt"> | string | null
  width?: Prisma.IntNullableFilter<"ReleaseCoverArt"> | number | null
  height?: Prisma.IntNullableFilter<"ReleaseCoverArt"> | number | null
  createdAt?: Prisma.DateTimeFilter<"ReleaseCoverArt"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"ReleaseCoverArt"> | Date | string
  release?: Prisma.XOR<Prisma.ReleaseScalarRelationFilter, Prisma.ReleaseWhereInput>
}

export type ReleaseCoverArtOrderByWithRelationInput = {
  id?: Prisma.SortOrder
  releaseId?: Prisma.SortOrder
  imageUrl?: Prisma.SortOrder
  imageKey?: Prisma.SortOrder
  fileName?: Prisma.SortOrderInput | Prisma.SortOrder
  fileSize?: Prisma.SortOrderInput | Prisma.SortOrder
  mimeType?: Prisma.SortOrderInput | Prisma.SortOrder
  width?: Prisma.SortOrderInput | Prisma.SortOrder
  height?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  release?: Prisma.ReleaseOrderByWithRelationInput
}

export type ReleaseCoverArtWhereUniqueInput = Prisma.AtLeast<{
  id?: string
  AND?: Prisma.ReleaseCoverArtWhereInput | Prisma.ReleaseCoverArtWhereInput[]
  OR?: Prisma.ReleaseCoverArtWhereInput[]
  NOT?: Prisma.ReleaseCoverArtWhereInput | Prisma.ReleaseCoverArtWhereInput[]
  releaseId?: Prisma.StringFilter<"ReleaseCoverArt"> | string
  imageUrl?: Prisma.StringFilter<"ReleaseCoverArt"> | string
  imageKey?: Prisma.StringFilter<"ReleaseCoverArt"> | string
  fileName?: Prisma.StringNullableFilter<"ReleaseCoverArt"> | string | null
  fileSize?: Prisma.IntNullableFilter<"ReleaseCoverArt"> | number | null
  mimeType?: Prisma.StringNullableFilter<"ReleaseCoverArt"> | string | null
  width?: Prisma.IntNullableFilter<"ReleaseCoverArt"> | number | null
  height?: Prisma.IntNullableFilter<"ReleaseCoverArt"> | number | null
  createdAt?: Prisma.DateTimeFilter<"ReleaseCoverArt"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"ReleaseCoverArt"> | Date | string
  release?: Prisma.XOR<Prisma.ReleaseScalarRelationFilter, Prisma.ReleaseWhereInput>
}, "id">

export type ReleaseCoverArtOrderByWithAggregationInput = {
  id?: Prisma.SortOrder
  releaseId?: Prisma.SortOrder
  imageUrl?: Prisma.SortOrder
  imageKey?: Prisma.SortOrder
  fileName?: Prisma.SortOrderInput | Prisma.SortOrder
  fileSize?: Prisma.SortOrderInput | Prisma.SortOrder
  mimeType?: Prisma.SortOrderInput | Prisma.SortOrder
  width?: Prisma.SortOrderInput | Prisma.SortOrder
  height?: Prisma.SortOrderInput | Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
  _count?: Prisma.ReleaseCoverArtCountOrderByAggregateInput
  _avg?: Prisma.ReleaseCoverArtAvgOrderByAggregateInput
  _max?: Prisma.ReleaseCoverArtMaxOrderByAggregateInput
  _min?: Prisma.ReleaseCoverArtMinOrderByAggregateInput
  _sum?: Prisma.ReleaseCoverArtSumOrderByAggregateInput
}

export type ReleaseCoverArtScalarWhereWithAggregatesInput = {
  AND?: Prisma.ReleaseCoverArtScalarWhereWithAggregatesInput | Prisma.ReleaseCoverArtScalarWhereWithAggregatesInput[]
  OR?: Prisma.ReleaseCoverArtScalarWhereWithAggregatesInput[]
  NOT?: Prisma.ReleaseCoverArtScalarWhereWithAggregatesInput | Prisma.ReleaseCoverArtScalarWhereWithAggregatesInput[]
  id?: Prisma.StringWithAggregatesFilter<"ReleaseCoverArt"> | string
  releaseId?: Prisma.StringWithAggregatesFilter<"ReleaseCoverArt"> | string
  imageUrl?: Prisma.StringWithAggregatesFilter<"ReleaseCoverArt"> | string
  imageKey?: Prisma.StringWithAggregatesFilter<"ReleaseCoverArt"> | string
  fileName?: Prisma.StringNullableWithAggregatesFilter<"ReleaseCoverArt"> | string | null
  fileSize?: Prisma.IntNullableWithAggregatesFilter<"ReleaseCoverArt"> | number | null
  mimeType?: Prisma.StringNullableWithAggregatesFilter<"ReleaseCoverArt"> | string | null
  width?: Prisma.IntNullableWithAggregatesFilter<"ReleaseCoverArt"> | number | null
  height?: Prisma.IntNullableWithAggregatesFilter<"ReleaseCoverArt"> | number | null
  createdAt?: Prisma.DateTimeWithAggregatesFilter<"ReleaseCoverArt"> | Date | string
  updatedAt?: Prisma.DateTimeWithAggregatesFilter<"ReleaseCoverArt"> | Date | string
}

export type ReleaseCoverArtCreateInput = {
  id?: string
  imageUrl: string
  imageKey: string
  fileName?: string | null
  fileSize?: number | null
  mimeType?: string | null
  width?: number | null
  height?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
  release: Prisma.ReleaseCreateNestedOneWithoutCoverArtsInput
}

export type ReleaseCoverArtUncheckedCreateInput = {
  id?: string
  releaseId: string
  imageUrl: string
  imageKey: string
  fileName?: string | null
  fileSize?: number | null
  mimeType?: string | null
  width?: number | null
  height?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type ReleaseCoverArtUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  imageUrl?: Prisma.StringFieldUpdateOperationsInput | string
  imageKey?: Prisma.StringFieldUpdateOperationsInput | string
  fileName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fileSize?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  mimeType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  width?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  height?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  release?: Prisma.ReleaseUpdateOneRequiredWithoutCoverArtsNestedInput
}

export type ReleaseCoverArtUncheckedUpdateInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  releaseId?: Prisma.StringFieldUpdateOperationsInput | string
  imageUrl?: Prisma.StringFieldUpdateOperationsInput | string
  imageKey?: Prisma.StringFieldUpdateOperationsInput | string
  fileName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fileSize?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  mimeType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  width?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  height?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ReleaseCoverArtCreateManyInput = {
  id?: string
  releaseId: string
  imageUrl: string
  imageKey: string
  fileName?: string | null
  fileSize?: number | null
  mimeType?: string | null
  width?: number | null
  height?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type ReleaseCoverArtUpdateManyMutationInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  imageUrl?: Prisma.StringFieldUpdateOperationsInput | string
  imageKey?: Prisma.StringFieldUpdateOperationsInput | string
  fileName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fileSize?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  mimeType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  width?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  height?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ReleaseCoverArtUncheckedUpdateManyInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  releaseId?: Prisma.StringFieldUpdateOperationsInput | string
  imageUrl?: Prisma.StringFieldUpdateOperationsInput | string
  imageKey?: Prisma.StringFieldUpdateOperationsInput | string
  fileName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fileSize?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  mimeType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  width?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  height?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ReleaseCoverArtListRelationFilter = {
  every?: Prisma.ReleaseCoverArtWhereInput
  some?: Prisma.ReleaseCoverArtWhereInput
  none?: Prisma.ReleaseCoverArtWhereInput
}

export type ReleaseCoverArtOrderByRelationAggregateInput = {
  _count?: Prisma.SortOrder
}

export type ReleaseCoverArtCountOrderByAggregateInput = {
  id?: Prisma.SortOrder
  releaseId?: Prisma.SortOrder
  imageUrl?: Prisma.SortOrder
  imageKey?: Prisma.SortOrder
  fileName?: Prisma.SortOrder
  fileSize?: Prisma.SortOrder
  mimeType?: Prisma.SortOrder
  width?: Prisma.SortOrder
  height?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type ReleaseCoverArtAvgOrderByAggregateInput = {
  fileSize?: Prisma.SortOrder
  width?: Prisma.SortOrder
  height?: Prisma.SortOrder
}

export type ReleaseCoverArtMaxOrderByAggregateInput = {
  id?: Prisma.SortOrder
  releaseId?: Prisma.SortOrder
  imageUrl?: Prisma.SortOrder
  imageKey?: Prisma.SortOrder
  fileName?: Prisma.SortOrder
  fileSize?: Prisma.SortOrder
  mimeType?: Prisma.SortOrder
  width?: Prisma.SortOrder
  height?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type ReleaseCoverArtMinOrderByAggregateInput = {
  id?: Prisma.SortOrder
  releaseId?: Prisma.SortOrder
  imageUrl?: Prisma.SortOrder
  imageKey?: Prisma.SortOrder
  fileName?: Prisma.SortOrder
  fileSize?: Prisma.SortOrder
  mimeType?: Prisma.SortOrder
  width?: Prisma.SortOrder
  height?: Prisma.SortOrder
  createdAt?: Prisma.SortOrder
  updatedAt?: Prisma.SortOrder
}

export type ReleaseCoverArtSumOrderByAggregateInput = {
  fileSize?: Prisma.SortOrder
  width?: Prisma.SortOrder
  height?: Prisma.SortOrder
}

export type ReleaseCoverArtCreateNestedManyWithoutReleaseInput = {
  create?: Prisma.XOR<Prisma.ReleaseCoverArtCreateWithoutReleaseInput, Prisma.ReleaseCoverArtUncheckedCreateWithoutReleaseInput> | Prisma.ReleaseCoverArtCreateWithoutReleaseInput[] | Prisma.ReleaseCoverArtUncheckedCreateWithoutReleaseInput[]
  connectOrCreate?: Prisma.ReleaseCoverArtCreateOrConnectWithoutReleaseInput | Prisma.ReleaseCoverArtCreateOrConnectWithoutReleaseInput[]
  createMany?: Prisma.ReleaseCoverArtCreateManyReleaseInputEnvelope
  connect?: Prisma.ReleaseCoverArtWhereUniqueInput | Prisma.ReleaseCoverArtWhereUniqueInput[]
}

export type ReleaseCoverArtUncheckedCreateNestedManyWithoutReleaseInput = {
  create?: Prisma.XOR<Prisma.ReleaseCoverArtCreateWithoutReleaseInput, Prisma.ReleaseCoverArtUncheckedCreateWithoutReleaseInput> | Prisma.ReleaseCoverArtCreateWithoutReleaseInput[] | Prisma.ReleaseCoverArtUncheckedCreateWithoutReleaseInput[]
  connectOrCreate?: Prisma.ReleaseCoverArtCreateOrConnectWithoutReleaseInput | Prisma.ReleaseCoverArtCreateOrConnectWithoutReleaseInput[]
  createMany?: Prisma.ReleaseCoverArtCreateManyReleaseInputEnvelope
  connect?: Prisma.ReleaseCoverArtWhereUniqueInput | Prisma.ReleaseCoverArtWhereUniqueInput[]
}

export type ReleaseCoverArtUpdateManyWithoutReleaseNestedInput = {
  create?: Prisma.XOR<Prisma.ReleaseCoverArtCreateWithoutReleaseInput, Prisma.ReleaseCoverArtUncheckedCreateWithoutReleaseInput> | Prisma.ReleaseCoverArtCreateWithoutReleaseInput[] | Prisma.ReleaseCoverArtUncheckedCreateWithoutReleaseInput[]
  connectOrCreate?: Prisma.ReleaseCoverArtCreateOrConnectWithoutReleaseInput | Prisma.ReleaseCoverArtCreateOrConnectWithoutReleaseInput[]
  upsert?: Prisma.ReleaseCoverArtUpsertWithWhereUniqueWithoutReleaseInput | Prisma.ReleaseCoverArtUpsertWithWhereUniqueWithoutReleaseInput[]
  createMany?: Prisma.ReleaseCoverArtCreateManyReleaseInputEnvelope
  set?: Prisma.ReleaseCoverArtWhereUniqueInput | Prisma.ReleaseCoverArtWhereUniqueInput[]
  disconnect?: Prisma.ReleaseCoverArtWhereUniqueInput | Prisma.ReleaseCoverArtWhereUniqueInput[]
  delete?: Prisma.ReleaseCoverArtWhereUniqueInput | Prisma.ReleaseCoverArtWhereUniqueInput[]
  connect?: Prisma.ReleaseCoverArtWhereUniqueInput | Prisma.ReleaseCoverArtWhereUniqueInput[]
  update?: Prisma.ReleaseCoverArtUpdateWithWhereUniqueWithoutReleaseInput | Prisma.ReleaseCoverArtUpdateWithWhereUniqueWithoutReleaseInput[]
  updateMany?: Prisma.ReleaseCoverArtUpdateManyWithWhereWithoutReleaseInput | Prisma.ReleaseCoverArtUpdateManyWithWhereWithoutReleaseInput[]
  deleteMany?: Prisma.ReleaseCoverArtScalarWhereInput | Prisma.ReleaseCoverArtScalarWhereInput[]
}

export type ReleaseCoverArtUncheckedUpdateManyWithoutReleaseNestedInput = {
  create?: Prisma.XOR<Prisma.ReleaseCoverArtCreateWithoutReleaseInput, Prisma.ReleaseCoverArtUncheckedCreateWithoutReleaseInput> | Prisma.ReleaseCoverArtCreateWithoutReleaseInput[] | Prisma.ReleaseCoverArtUncheckedCreateWithoutReleaseInput[]
  connectOrCreate?: Prisma.ReleaseCoverArtCreateOrConnectWithoutReleaseInput | Prisma.ReleaseCoverArtCreateOrConnectWithoutReleaseInput[]
  upsert?: Prisma.ReleaseCoverArtUpsertWithWhereUniqueWithoutReleaseInput | Prisma.ReleaseCoverArtUpsertWithWhereUniqueWithoutReleaseInput[]
  createMany?: Prisma.ReleaseCoverArtCreateManyReleaseInputEnvelope
  set?: Prisma.ReleaseCoverArtWhereUniqueInput | Prisma.ReleaseCoverArtWhereUniqueInput[]
  disconnect?: Prisma.ReleaseCoverArtWhereUniqueInput | Prisma.ReleaseCoverArtWhereUniqueInput[]
  delete?: Prisma.ReleaseCoverArtWhereUniqueInput | Prisma.ReleaseCoverArtWhereUniqueInput[]
  connect?: Prisma.ReleaseCoverArtWhereUniqueInput | Prisma.ReleaseCoverArtWhereUniqueInput[]
  update?: Prisma.ReleaseCoverArtUpdateWithWhereUniqueWithoutReleaseInput | Prisma.ReleaseCoverArtUpdateWithWhereUniqueWithoutReleaseInput[]
  updateMany?: Prisma.ReleaseCoverArtUpdateManyWithWhereWithoutReleaseInput | Prisma.ReleaseCoverArtUpdateManyWithWhereWithoutReleaseInput[]
  deleteMany?: Prisma.ReleaseCoverArtScalarWhereInput | Prisma.ReleaseCoverArtScalarWhereInput[]
}

export type NullableIntFieldUpdateOperationsInput = {
  set?: number | null
  increment?: number
  decrement?: number
  multiply?: number
  divide?: number
}

export type ReleaseCoverArtCreateWithoutReleaseInput = {
  id?: string
  imageUrl: string
  imageKey: string
  fileName?: string | null
  fileSize?: number | null
  mimeType?: string | null
  width?: number | null
  height?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type ReleaseCoverArtUncheckedCreateWithoutReleaseInput = {
  id?: string
  imageUrl: string
  imageKey: string
  fileName?: string | null
  fileSize?: number | null
  mimeType?: string | null
  width?: number | null
  height?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type ReleaseCoverArtCreateOrConnectWithoutReleaseInput = {
  where: Prisma.ReleaseCoverArtWhereUniqueInput
  create: Prisma.XOR<Prisma.ReleaseCoverArtCreateWithoutReleaseInput, Prisma.ReleaseCoverArtUncheckedCreateWithoutReleaseInput>
}

export type ReleaseCoverArtCreateManyReleaseInputEnvelope = {
  data: Prisma.ReleaseCoverArtCreateManyReleaseInput | Prisma.ReleaseCoverArtCreateManyReleaseInput[]
  skipDuplicates?: boolean
}

export type ReleaseCoverArtUpsertWithWhereUniqueWithoutReleaseInput = {
  where: Prisma.ReleaseCoverArtWhereUniqueInput
  update: Prisma.XOR<Prisma.ReleaseCoverArtUpdateWithoutReleaseInput, Prisma.ReleaseCoverArtUncheckedUpdateWithoutReleaseInput>
  create: Prisma.XOR<Prisma.ReleaseCoverArtCreateWithoutReleaseInput, Prisma.ReleaseCoverArtUncheckedCreateWithoutReleaseInput>
}

export type ReleaseCoverArtUpdateWithWhereUniqueWithoutReleaseInput = {
  where: Prisma.ReleaseCoverArtWhereUniqueInput
  data: Prisma.XOR<Prisma.ReleaseCoverArtUpdateWithoutReleaseInput, Prisma.ReleaseCoverArtUncheckedUpdateWithoutReleaseInput>
}

export type ReleaseCoverArtUpdateManyWithWhereWithoutReleaseInput = {
  where: Prisma.ReleaseCoverArtScalarWhereInput
  data: Prisma.XOR<Prisma.ReleaseCoverArtUpdateManyMutationInput, Prisma.ReleaseCoverArtUncheckedUpdateManyWithoutReleaseInput>
}

export type ReleaseCoverArtScalarWhereInput = {
  AND?: Prisma.ReleaseCoverArtScalarWhereInput | Prisma.ReleaseCoverArtScalarWhereInput[]
  OR?: Prisma.ReleaseCoverArtScalarWhereInput[]
  NOT?: Prisma.ReleaseCoverArtScalarWhereInput | Prisma.ReleaseCoverArtScalarWhereInput[]
  id?: Prisma.StringFilter<"ReleaseCoverArt"> | string
  releaseId?: Prisma.StringFilter<"ReleaseCoverArt"> | string
  imageUrl?: Prisma.StringFilter<"ReleaseCoverArt"> | string
  imageKey?: Prisma.StringFilter<"ReleaseCoverArt"> | string
  fileName?: Prisma.StringNullableFilter<"ReleaseCoverArt"> | string | null
  fileSize?: Prisma.IntNullableFilter<"ReleaseCoverArt"> | number | null
  mimeType?: Prisma.StringNullableFilter<"ReleaseCoverArt"> | string | null
  width?: Prisma.IntNullableFilter<"ReleaseCoverArt"> | number | null
  height?: Prisma.IntNullableFilter<"ReleaseCoverArt"> | number | null
  createdAt?: Prisma.DateTimeFilter<"ReleaseCoverArt"> | Date | string
  updatedAt?: Prisma.DateTimeFilter<"ReleaseCoverArt"> | Date | string
}

export type ReleaseCoverArtCreateManyReleaseInput = {
  id?: string
  imageUrl: string
  imageKey: string
  fileName?: string | null
  fileSize?: number | null
  mimeType?: string | null
  width?: number | null
  height?: number | null
  createdAt?: Date | string
  updatedAt?: Date | string
}

export type ReleaseCoverArtUpdateWithoutReleaseInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  imageUrl?: Prisma.StringFieldUpdateOperationsInput | string
  imageKey?: Prisma.StringFieldUpdateOperationsInput | string
  fileName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fileSize?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  mimeType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  width?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  height?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ReleaseCoverArtUncheckedUpdateWithoutReleaseInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  imageUrl?: Prisma.StringFieldUpdateOperationsInput | string
  imageKey?: Prisma.StringFieldUpdateOperationsInput | string
  fileName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fileSize?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  mimeType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  width?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  height?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}

export type ReleaseCoverArtUncheckedUpdateManyWithoutReleaseInput = {
  id?: Prisma.StringFieldUpdateOperationsInput | string
  imageUrl?: Prisma.StringFieldUpdateOperationsInput | string
  imageKey?: Prisma.StringFieldUpdateOperationsInput | string
  fileName?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  fileSize?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  mimeType?: Prisma.NullableStringFieldUpdateOperationsInput | string | null
  width?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  height?: Prisma.NullableIntFieldUpdateOperationsInput | number | null
  createdAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
  updatedAt?: Prisma.DateTimeFieldUpdateOperationsInput | Date | string
}



export type ReleaseCoverArtSelect<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  releaseId?: boolean
  imageUrl?: boolean
  imageKey?: boolean
  fileName?: boolean
  fileSize?: boolean
  mimeType?: boolean
  width?: boolean
  height?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  release?: boolean | Prisma.ReleaseDefaultArgs<ExtArgs>
}, ExtArgs["result"]["releaseCoverArt"]>

export type ReleaseCoverArtSelectCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  releaseId?: boolean
  imageUrl?: boolean
  imageKey?: boolean
  fileName?: boolean
  fileSize?: boolean
  mimeType?: boolean
  width?: boolean
  height?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  release?: boolean | Prisma.ReleaseDefaultArgs<ExtArgs>
}, ExtArgs["result"]["releaseCoverArt"]>

export type ReleaseCoverArtSelectUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetSelect<{
  id?: boolean
  releaseId?: boolean
  imageUrl?: boolean
  imageKey?: boolean
  fileName?: boolean
  fileSize?: boolean
  mimeType?: boolean
  width?: boolean
  height?: boolean
  createdAt?: boolean
  updatedAt?: boolean
  release?: boolean | Prisma.ReleaseDefaultArgs<ExtArgs>
}, ExtArgs["result"]["releaseCoverArt"]>

export type ReleaseCoverArtSelectScalar = {
  id?: boolean
  releaseId?: boolean
  imageUrl?: boolean
  imageKey?: boolean
  fileName?: boolean
  fileSize?: boolean
  mimeType?: boolean
  width?: boolean
  height?: boolean
  createdAt?: boolean
  updatedAt?: boolean
}

export type ReleaseCoverArtOmit<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = runtime.Types.Extensions.GetOmit<"id" | "releaseId" | "imageUrl" | "imageKey" | "fileName" | "fileSize" | "mimeType" | "width" | "height" | "createdAt" | "updatedAt", ExtArgs["result"]["releaseCoverArt"]>
export type ReleaseCoverArtInclude<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  release?: boolean | Prisma.ReleaseDefaultArgs<ExtArgs>
}
export type ReleaseCoverArtIncludeCreateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  release?: boolean | Prisma.ReleaseDefaultArgs<ExtArgs>
}
export type ReleaseCoverArtIncludeUpdateManyAndReturn<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  release?: boolean | Prisma.ReleaseDefaultArgs<ExtArgs>
}

export type $ReleaseCoverArtPayload<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  name: "ReleaseCoverArt"
  objects: {
    release: Prisma.$ReleasePayload<ExtArgs>
  }
  scalars: runtime.Types.Extensions.GetPayloadResult<{
    id: string
    releaseId: string
    imageUrl: string
    imageKey: string
    fileName: string | null
    fileSize: number | null
    mimeType: string | null
    width: number | null
    height: number | null
    createdAt: Date
    updatedAt: Date
  }, ExtArgs["result"]["releaseCoverArt"]>
  composites: {}
}

export type ReleaseCoverArtGetPayload<S extends boolean | null | undefined | ReleaseCoverArtDefaultArgs> = runtime.Types.Result.GetResult<Prisma.$ReleaseCoverArtPayload, S>

export type ReleaseCoverArtCountArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> =
  Omit<ReleaseCoverArtFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
    select?: ReleaseCoverArtCountAggregateInputType | true
  }

export interface ReleaseCoverArtDelegate<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['ReleaseCoverArt'], meta: { name: 'ReleaseCoverArt' } }
  /**
   * Find zero or one ReleaseCoverArt that matches the filter.
   * @param {ReleaseCoverArtFindUniqueArgs} args - Arguments to find a ReleaseCoverArt
   * @example
   * // Get one ReleaseCoverArt
   * const releaseCoverArt = await prisma.releaseCoverArt.findUnique({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUnique<T extends ReleaseCoverArtFindUniqueArgs>(args: Prisma.SelectSubset<T, ReleaseCoverArtFindUniqueArgs<ExtArgs>>): Prisma.Prisma__ReleaseCoverArtClient<runtime.Types.Result.GetResult<Prisma.$ReleaseCoverArtPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find one ReleaseCoverArt that matches the filter or throw an error with `error.code='P2025'`
   * if no matches were found.
   * @param {ReleaseCoverArtFindUniqueOrThrowArgs} args - Arguments to find a ReleaseCoverArt
   * @example
   * // Get one ReleaseCoverArt
   * const releaseCoverArt = await prisma.releaseCoverArt.findUniqueOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findUniqueOrThrow<T extends ReleaseCoverArtFindUniqueOrThrowArgs>(args: Prisma.SelectSubset<T, ReleaseCoverArtFindUniqueOrThrowArgs<ExtArgs>>): Prisma.Prisma__ReleaseCoverArtClient<runtime.Types.Result.GetResult<Prisma.$ReleaseCoverArtPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first ReleaseCoverArt that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReleaseCoverArtFindFirstArgs} args - Arguments to find a ReleaseCoverArt
   * @example
   * // Get one ReleaseCoverArt
   * const releaseCoverArt = await prisma.releaseCoverArt.findFirst({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirst<T extends ReleaseCoverArtFindFirstArgs>(args?: Prisma.SelectSubset<T, ReleaseCoverArtFindFirstArgs<ExtArgs>>): Prisma.Prisma__ReleaseCoverArtClient<runtime.Types.Result.GetResult<Prisma.$ReleaseCoverArtPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

  /**
   * Find the first ReleaseCoverArt that matches the filter or
   * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReleaseCoverArtFindFirstOrThrowArgs} args - Arguments to find a ReleaseCoverArt
   * @example
   * // Get one ReleaseCoverArt
   * const releaseCoverArt = await prisma.releaseCoverArt.findFirstOrThrow({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   */
  findFirstOrThrow<T extends ReleaseCoverArtFindFirstOrThrowArgs>(args?: Prisma.SelectSubset<T, ReleaseCoverArtFindFirstOrThrowArgs<ExtArgs>>): Prisma.Prisma__ReleaseCoverArtClient<runtime.Types.Result.GetResult<Prisma.$ReleaseCoverArtPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Find zero or more ReleaseCoverArts that matches the filter.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReleaseCoverArtFindManyArgs} args - Arguments to filter and select certain fields only.
   * @example
   * // Get all ReleaseCoverArts
   * const releaseCoverArts = await prisma.releaseCoverArt.findMany()
   * 
   * // Get first 10 ReleaseCoverArts
   * const releaseCoverArts = await prisma.releaseCoverArt.findMany({ take: 10 })
   * 
   * // Only select the `id`
   * const releaseCoverArtWithIdOnly = await prisma.releaseCoverArt.findMany({ select: { id: true } })
   * 
   */
  findMany<T extends ReleaseCoverArtFindManyArgs>(args?: Prisma.SelectSubset<T, ReleaseCoverArtFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ReleaseCoverArtPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

  /**
   * Create a ReleaseCoverArt.
   * @param {ReleaseCoverArtCreateArgs} args - Arguments to create a ReleaseCoverArt.
   * @example
   * // Create one ReleaseCoverArt
   * const ReleaseCoverArt = await prisma.releaseCoverArt.create({
   *   data: {
   *     // ... data to create a ReleaseCoverArt
   *   }
   * })
   * 
   */
  create<T extends ReleaseCoverArtCreateArgs>(args: Prisma.SelectSubset<T, ReleaseCoverArtCreateArgs<ExtArgs>>): Prisma.Prisma__ReleaseCoverArtClient<runtime.Types.Result.GetResult<Prisma.$ReleaseCoverArtPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Create many ReleaseCoverArts.
   * @param {ReleaseCoverArtCreateManyArgs} args - Arguments to create many ReleaseCoverArts.
   * @example
   * // Create many ReleaseCoverArts
   * const releaseCoverArt = await prisma.releaseCoverArt.createMany({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   *     
   */
  createMany<T extends ReleaseCoverArtCreateManyArgs>(args?: Prisma.SelectSubset<T, ReleaseCoverArtCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Create many ReleaseCoverArts and returns the data saved in the database.
   * @param {ReleaseCoverArtCreateManyAndReturnArgs} args - Arguments to create many ReleaseCoverArts.
   * @example
   * // Create many ReleaseCoverArts
   * const releaseCoverArt = await prisma.releaseCoverArt.createManyAndReturn({
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Create many ReleaseCoverArts and only return the `id`
   * const releaseCoverArtWithIdOnly = await prisma.releaseCoverArt.createManyAndReturn({
   *   select: { id: true },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  createManyAndReturn<T extends ReleaseCoverArtCreateManyAndReturnArgs>(args?: Prisma.SelectSubset<T, ReleaseCoverArtCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ReleaseCoverArtPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

  /**
   * Delete a ReleaseCoverArt.
   * @param {ReleaseCoverArtDeleteArgs} args - Arguments to delete one ReleaseCoverArt.
   * @example
   * // Delete one ReleaseCoverArt
   * const ReleaseCoverArt = await prisma.releaseCoverArt.delete({
   *   where: {
   *     // ... filter to delete one ReleaseCoverArt
   *   }
   * })
   * 
   */
  delete<T extends ReleaseCoverArtDeleteArgs>(args: Prisma.SelectSubset<T, ReleaseCoverArtDeleteArgs<ExtArgs>>): Prisma.Prisma__ReleaseCoverArtClient<runtime.Types.Result.GetResult<Prisma.$ReleaseCoverArtPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Update one ReleaseCoverArt.
   * @param {ReleaseCoverArtUpdateArgs} args - Arguments to update one ReleaseCoverArt.
   * @example
   * // Update one ReleaseCoverArt
   * const releaseCoverArt = await prisma.releaseCoverArt.update({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  update<T extends ReleaseCoverArtUpdateArgs>(args: Prisma.SelectSubset<T, ReleaseCoverArtUpdateArgs<ExtArgs>>): Prisma.Prisma__ReleaseCoverArtClient<runtime.Types.Result.GetResult<Prisma.$ReleaseCoverArtPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

  /**
   * Delete zero or more ReleaseCoverArts.
   * @param {ReleaseCoverArtDeleteManyArgs} args - Arguments to filter ReleaseCoverArts to delete.
   * @example
   * // Delete a few ReleaseCoverArts
   * const { count } = await prisma.releaseCoverArt.deleteMany({
   *   where: {
   *     // ... provide filter here
   *   }
   * })
   * 
   */
  deleteMany<T extends ReleaseCoverArtDeleteManyArgs>(args?: Prisma.SelectSubset<T, ReleaseCoverArtDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more ReleaseCoverArts.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReleaseCoverArtUpdateManyArgs} args - Arguments to update one or more rows.
   * @example
   * // Update many ReleaseCoverArts
   * const releaseCoverArt = await prisma.releaseCoverArt.updateMany({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: {
   *     // ... provide data here
   *   }
   * })
   * 
   */
  updateMany<T extends ReleaseCoverArtUpdateManyArgs>(args: Prisma.SelectSubset<T, ReleaseCoverArtUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<Prisma.BatchPayload>

  /**
   * Update zero or more ReleaseCoverArts and returns the data updated in the database.
   * @param {ReleaseCoverArtUpdateManyAndReturnArgs} args - Arguments to update many ReleaseCoverArts.
   * @example
   * // Update many ReleaseCoverArts
   * const releaseCoverArt = await prisma.releaseCoverArt.updateManyAndReturn({
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * 
   * // Update zero or more ReleaseCoverArts and only return the `id`
   * const releaseCoverArtWithIdOnly = await prisma.releaseCoverArt.updateManyAndReturn({
   *   select: { id: true },
   *   where: {
   *     // ... provide filter here
   *   },
   *   data: [
   *     // ... provide data here
   *   ]
   * })
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * 
   */
  updateManyAndReturn<T extends ReleaseCoverArtUpdateManyAndReturnArgs>(args: Prisma.SelectSubset<T, ReleaseCoverArtUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<runtime.Types.Result.GetResult<Prisma.$ReleaseCoverArtPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

  /**
   * Create or update one ReleaseCoverArt.
   * @param {ReleaseCoverArtUpsertArgs} args - Arguments to update or create a ReleaseCoverArt.
   * @example
   * // Update or create a ReleaseCoverArt
   * const releaseCoverArt = await prisma.releaseCoverArt.upsert({
   *   create: {
   *     // ... data to create a ReleaseCoverArt
   *   },
   *   update: {
   *     // ... in case it already exists, update
   *   },
   *   where: {
   *     // ... the filter for the ReleaseCoverArt we want to update
   *   }
   * })
   */
  upsert<T extends ReleaseCoverArtUpsertArgs>(args: Prisma.SelectSubset<T, ReleaseCoverArtUpsertArgs<ExtArgs>>): Prisma.Prisma__ReleaseCoverArtClient<runtime.Types.Result.GetResult<Prisma.$ReleaseCoverArtPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


  /**
   * Count the number of ReleaseCoverArts.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReleaseCoverArtCountArgs} args - Arguments to filter ReleaseCoverArts to count.
   * @example
   * // Count the number of ReleaseCoverArts
   * const count = await prisma.releaseCoverArt.count({
   *   where: {
   *     // ... the filter for the ReleaseCoverArts we want to count
   *   }
   * })
  **/
  count<T extends ReleaseCoverArtCountArgs>(
    args?: Prisma.Subset<T, ReleaseCoverArtCountArgs>,
  ): Prisma.PrismaPromise<
    T extends runtime.Types.Utils.Record<'select', any>
      ? T['select'] extends true
        ? number
        : Prisma.GetScalarType<T['select'], ReleaseCoverArtCountAggregateOutputType>
      : number
  >

  /**
   * Allows you to perform aggregations operations on a ReleaseCoverArt.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReleaseCoverArtAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
   * @example
   * // Ordered by age ascending
   * // Where email contains prisma.io
   * // Limited to the 10 users
   * const aggregations = await prisma.user.aggregate({
   *   _avg: {
   *     age: true,
   *   },
   *   where: {
   *     email: {
   *       contains: "prisma.io",
   *     },
   *   },
   *   orderBy: {
   *     age: "asc",
   *   },
   *   take: 10,
   * })
  **/
  aggregate<T extends ReleaseCoverArtAggregateArgs>(args: Prisma.Subset<T, ReleaseCoverArtAggregateArgs>): Prisma.PrismaPromise<GetReleaseCoverArtAggregateType<T>>

  /**
   * Group by ReleaseCoverArt.
   * Note, that providing `undefined` is treated as the value not being there.
   * Read more here: https://pris.ly/d/null-undefined
   * @param {ReleaseCoverArtGroupByArgs} args - Group by arguments.
   * @example
   * // Group by city, order by createdAt, get count
   * const result = await prisma.user.groupBy({
   *   by: ['city', 'createdAt'],
   *   orderBy: {
   *     createdAt: true
   *   },
   *   _count: {
   *     _all: true
   *   },
   * })
   * 
  **/
  groupBy<
    T extends ReleaseCoverArtGroupByArgs,
    HasSelectOrTake extends Prisma.Or<
      Prisma.Extends<'skip', Prisma.Keys<T>>,
      Prisma.Extends<'take', Prisma.Keys<T>>
    >,
    OrderByArg extends Prisma.True extends HasSelectOrTake
      ? { orderBy: ReleaseCoverArtGroupByArgs['orderBy'] }
      : { orderBy?: ReleaseCoverArtGroupByArgs['orderBy'] },
    OrderFields extends Prisma.ExcludeUnderscoreKeys<Prisma.Keys<Prisma.MaybeTupleToUnion<T['orderBy']>>>,
    ByFields extends Prisma.MaybeTupleToUnion<T['by']>,
    ByValid extends Prisma.Has<ByFields, OrderFields>,
    HavingFields extends Prisma.GetHavingFields<T['having']>,
    HavingValid extends Prisma.Has<ByFields, HavingFields>,
    ByEmpty extends T['by'] extends never[] ? Prisma.True : Prisma.False,
    InputErrors extends ByEmpty extends Prisma.True
    ? `Error: "by" must not be empty.`
    : HavingValid extends Prisma.False
    ? {
        [P in HavingFields]: P extends ByFields
          ? never
          : P extends string
          ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
          : [
              Error,
              'Field ',
              P,
              ` in "having" needs to be provided in "by"`,
            ]
      }[HavingFields]
    : 'take' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "take", you also need to provide "orderBy"'
    : 'skip' extends Prisma.Keys<T>
    ? 'orderBy' extends Prisma.Keys<T>
      ? ByValid extends Prisma.True
        ? {}
        : {
            [P in OrderFields]: P extends ByFields
              ? never
              : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
          }[OrderFields]
      : 'Error: If you provide "skip", you also need to provide "orderBy"'
    : ByValid extends Prisma.True
    ? {}
    : {
        [P in OrderFields]: P extends ByFields
          ? never
          : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
      }[OrderFields]
  >(args: Prisma.SubsetIntersection<T, ReleaseCoverArtGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetReleaseCoverArtGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
/**
 * Fields of the ReleaseCoverArt model
 */
readonly fields: ReleaseCoverArtFieldRefs;
}

/**
 * The delegate class that acts as a "Promise-like" for ReleaseCoverArt.
 * Why is this prefixed with `Prisma__`?
 * Because we want to prevent naming conflicts as mentioned in
 * https://github.com/prisma/prisma-client-js/issues/707
 */
export interface Prisma__ReleaseCoverArtClient<T, Null = never, ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
  readonly [Symbol.toStringTag]: "PrismaPromise"
  release<T extends Prisma.ReleaseDefaultArgs<ExtArgs> = {}>(args?: Prisma.Subset<T, Prisma.ReleaseDefaultArgs<ExtArgs>>): Prisma.Prisma__ReleaseClient<runtime.Types.Result.GetResult<Prisma.$ReleasePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
  /**
   * Attaches callbacks for the resolution and/or rejection of the Promise.
   * @param onfulfilled The callback to execute when the Promise is resolved.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of which ever callback is executed.
   */
  then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): runtime.Types.Utils.JsPromise<TResult1 | TResult2>
  /**
   * Attaches a callback for only the rejection of the Promise.
   * @param onrejected The callback to execute when the Promise is rejected.
   * @returns A Promise for the completion of the callback.
   */
  catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): runtime.Types.Utils.JsPromise<T | TResult>
  /**
   * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
   * resolved value cannot be modified from the callback.
   * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
   * @returns A Promise for the completion of the callback.
   */
  finally(onfinally?: (() => void) | undefined | null): runtime.Types.Utils.JsPromise<T>
}




/**
 * Fields of the ReleaseCoverArt model
 */
export interface ReleaseCoverArtFieldRefs {
  readonly id: Prisma.FieldRef<"ReleaseCoverArt", 'String'>
  readonly releaseId: Prisma.FieldRef<"ReleaseCoverArt", 'String'>
  readonly imageUrl: Prisma.FieldRef<"ReleaseCoverArt", 'String'>
  readonly imageKey: Prisma.FieldRef<"ReleaseCoverArt", 'String'>
  readonly fileName: Prisma.FieldRef<"ReleaseCoverArt", 'String'>
  readonly fileSize: Prisma.FieldRef<"ReleaseCoverArt", 'Int'>
  readonly mimeType: Prisma.FieldRef<"ReleaseCoverArt", 'String'>
  readonly width: Prisma.FieldRef<"ReleaseCoverArt", 'Int'>
  readonly height: Prisma.FieldRef<"ReleaseCoverArt", 'Int'>
  readonly createdAt: Prisma.FieldRef<"ReleaseCoverArt", 'DateTime'>
  readonly updatedAt: Prisma.FieldRef<"ReleaseCoverArt", 'DateTime'>
}
    

// Custom InputTypes
/**
 * ReleaseCoverArt findUnique
 */
export type ReleaseCoverArtFindUniqueArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseCoverArt
   */
  select?: Prisma.ReleaseCoverArtSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseCoverArt
   */
  omit?: Prisma.ReleaseCoverArtOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseCoverArtInclude<ExtArgs> | null
  /**
   * Filter, which ReleaseCoverArt to fetch.
   */
  where: Prisma.ReleaseCoverArtWhereUniqueInput
}

/**
 * ReleaseCoverArt findUniqueOrThrow
 */
export type ReleaseCoverArtFindUniqueOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseCoverArt
   */
  select?: Prisma.ReleaseCoverArtSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseCoverArt
   */
  omit?: Prisma.ReleaseCoverArtOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseCoverArtInclude<ExtArgs> | null
  /**
   * Filter, which ReleaseCoverArt to fetch.
   */
  where: Prisma.ReleaseCoverArtWhereUniqueInput
}

/**
 * ReleaseCoverArt findFirst
 */
export type ReleaseCoverArtFindFirstArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseCoverArt
   */
  select?: Prisma.ReleaseCoverArtSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseCoverArt
   */
  omit?: Prisma.ReleaseCoverArtOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseCoverArtInclude<ExtArgs> | null
  /**
   * Filter, which ReleaseCoverArt to fetch.
   */
  where?: Prisma.ReleaseCoverArtWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ReleaseCoverArts to fetch.
   */
  orderBy?: Prisma.ReleaseCoverArtOrderByWithRelationInput | Prisma.ReleaseCoverArtOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for ReleaseCoverArts.
   */
  cursor?: Prisma.ReleaseCoverArtWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ReleaseCoverArts from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ReleaseCoverArts.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of ReleaseCoverArts.
   */
  distinct?: Prisma.ReleaseCoverArtScalarFieldEnum | Prisma.ReleaseCoverArtScalarFieldEnum[]
}

/**
 * ReleaseCoverArt findFirstOrThrow
 */
export type ReleaseCoverArtFindFirstOrThrowArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseCoverArt
   */
  select?: Prisma.ReleaseCoverArtSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseCoverArt
   */
  omit?: Prisma.ReleaseCoverArtOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseCoverArtInclude<ExtArgs> | null
  /**
   * Filter, which ReleaseCoverArt to fetch.
   */
  where?: Prisma.ReleaseCoverArtWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ReleaseCoverArts to fetch.
   */
  orderBy?: Prisma.ReleaseCoverArtOrderByWithRelationInput | Prisma.ReleaseCoverArtOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for searching for ReleaseCoverArts.
   */
  cursor?: Prisma.ReleaseCoverArtWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ReleaseCoverArts from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ReleaseCoverArts.
   */
  skip?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
   * 
   * Filter by unique combinations of ReleaseCoverArts.
   */
  distinct?: Prisma.ReleaseCoverArtScalarFieldEnum | Prisma.ReleaseCoverArtScalarFieldEnum[]
}

/**
 * ReleaseCoverArt findMany
 */
export type ReleaseCoverArtFindManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseCoverArt
   */
  select?: Prisma.ReleaseCoverArtSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseCoverArt
   */
  omit?: Prisma.ReleaseCoverArtOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseCoverArtInclude<ExtArgs> | null
  /**
   * Filter, which ReleaseCoverArts to fetch.
   */
  where?: Prisma.ReleaseCoverArtWhereInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
   * 
   * Determine the order of ReleaseCoverArts to fetch.
   */
  orderBy?: Prisma.ReleaseCoverArtOrderByWithRelationInput | Prisma.ReleaseCoverArtOrderByWithRelationInput[]
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
   * 
   * Sets the position for listing ReleaseCoverArts.
   */
  cursor?: Prisma.ReleaseCoverArtWhereUniqueInput
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Take `±n` ReleaseCoverArts from the position of the cursor.
   */
  take?: number
  /**
   * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
   * 
   * Skip the first `n` ReleaseCoverArts.
   */
  skip?: number
  distinct?: Prisma.ReleaseCoverArtScalarFieldEnum | Prisma.ReleaseCoverArtScalarFieldEnum[]
}

/**
 * ReleaseCoverArt create
 */
export type ReleaseCoverArtCreateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseCoverArt
   */
  select?: Prisma.ReleaseCoverArtSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseCoverArt
   */
  omit?: Prisma.ReleaseCoverArtOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseCoverArtInclude<ExtArgs> | null
  /**
   * The data needed to create a ReleaseCoverArt.
   */
  data: Prisma.XOR<Prisma.ReleaseCoverArtCreateInput, Prisma.ReleaseCoverArtUncheckedCreateInput>
}

/**
 * ReleaseCoverArt createMany
 */
export type ReleaseCoverArtCreateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to create many ReleaseCoverArts.
   */
  data: Prisma.ReleaseCoverArtCreateManyInput | Prisma.ReleaseCoverArtCreateManyInput[]
  skipDuplicates?: boolean
}

/**
 * ReleaseCoverArt createManyAndReturn
 */
export type ReleaseCoverArtCreateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseCoverArt
   */
  select?: Prisma.ReleaseCoverArtSelectCreateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseCoverArt
   */
  omit?: Prisma.ReleaseCoverArtOmit<ExtArgs> | null
  /**
   * The data used to create many ReleaseCoverArts.
   */
  data: Prisma.ReleaseCoverArtCreateManyInput | Prisma.ReleaseCoverArtCreateManyInput[]
  skipDuplicates?: boolean
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseCoverArtIncludeCreateManyAndReturn<ExtArgs> | null
}

/**
 * ReleaseCoverArt update
 */
export type ReleaseCoverArtUpdateArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseCoverArt
   */
  select?: Prisma.ReleaseCoverArtSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseCoverArt
   */
  omit?: Prisma.ReleaseCoverArtOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseCoverArtInclude<ExtArgs> | null
  /**
   * The data needed to update a ReleaseCoverArt.
   */
  data: Prisma.XOR<Prisma.ReleaseCoverArtUpdateInput, Prisma.ReleaseCoverArtUncheckedUpdateInput>
  /**
   * Choose, which ReleaseCoverArt to update.
   */
  where: Prisma.ReleaseCoverArtWhereUniqueInput
}

/**
 * ReleaseCoverArt updateMany
 */
export type ReleaseCoverArtUpdateManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * The data used to update ReleaseCoverArts.
   */
  data: Prisma.XOR<Prisma.ReleaseCoverArtUpdateManyMutationInput, Prisma.ReleaseCoverArtUncheckedUpdateManyInput>
  /**
   * Filter which ReleaseCoverArts to update
   */
  where?: Prisma.ReleaseCoverArtWhereInput
  /**
   * Limit how many ReleaseCoverArts to update.
   */
  limit?: number
}

/**
 * ReleaseCoverArt updateManyAndReturn
 */
export type ReleaseCoverArtUpdateManyAndReturnArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseCoverArt
   */
  select?: Prisma.ReleaseCoverArtSelectUpdateManyAndReturn<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseCoverArt
   */
  omit?: Prisma.ReleaseCoverArtOmit<ExtArgs> | null
  /**
   * The data used to update ReleaseCoverArts.
   */
  data: Prisma.XOR<Prisma.ReleaseCoverArtUpdateManyMutationInput, Prisma.ReleaseCoverArtUncheckedUpdateManyInput>
  /**
   * Filter which ReleaseCoverArts to update
   */
  where?: Prisma.ReleaseCoverArtWhereInput
  /**
   * Limit how many ReleaseCoverArts to update.
   */
  limit?: number
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseCoverArtIncludeUpdateManyAndReturn<ExtArgs> | null
}

/**
 * ReleaseCoverArt upsert
 */
export type ReleaseCoverArtUpsertArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseCoverArt
   */
  select?: Prisma.ReleaseCoverArtSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseCoverArt
   */
  omit?: Prisma.ReleaseCoverArtOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseCoverArtInclude<ExtArgs> | null
  /**
   * The filter to search for the ReleaseCoverArt to update in case it exists.
   */
  where: Prisma.ReleaseCoverArtWhereUniqueInput
  /**
   * In case the ReleaseCoverArt found by the `where` argument doesn't exist, create a new ReleaseCoverArt with this data.
   */
  create: Prisma.XOR<Prisma.ReleaseCoverArtCreateInput, Prisma.ReleaseCoverArtUncheckedCreateInput>
  /**
   * In case the ReleaseCoverArt was found with the provided `where` argument, update it with this data.
   */
  update: Prisma.XOR<Prisma.ReleaseCoverArtUpdateInput, Prisma.ReleaseCoverArtUncheckedUpdateInput>
}

/**
 * ReleaseCoverArt delete
 */
export type ReleaseCoverArtDeleteArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseCoverArt
   */
  select?: Prisma.ReleaseCoverArtSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseCoverArt
   */
  omit?: Prisma.ReleaseCoverArtOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseCoverArtInclude<ExtArgs> | null
  /**
   * Filter which ReleaseCoverArt to delete.
   */
  where: Prisma.ReleaseCoverArtWhereUniqueInput
}

/**
 * ReleaseCoverArt deleteMany
 */
export type ReleaseCoverArtDeleteManyArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Filter which ReleaseCoverArts to delete
   */
  where?: Prisma.ReleaseCoverArtWhereInput
  /**
   * Limit how many ReleaseCoverArts to delete.
   */
  limit?: number
}

/**
 * ReleaseCoverArt without action
 */
export type ReleaseCoverArtDefaultArgs<ExtArgs extends runtime.Types.Extensions.InternalArgs = runtime.Types.Extensions.DefaultArgs> = {
  /**
   * Select specific fields to fetch from the ReleaseCoverArt
   */
  select?: Prisma.ReleaseCoverArtSelect<ExtArgs> | null
  /**
   * Omit specific fields from the ReleaseCoverArt
   */
  omit?: Prisma.ReleaseCoverArtOmit<ExtArgs> | null
  /**
   * Choose, which related nodes to fetch as well
   */
  include?: Prisma.ReleaseCoverArtInclude<ExtArgs> | null
}
