import { useState, useCallback, useEffect } from "react";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";

import { authClient } from "@/lib/auth-client";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import type { UserWithRole } from "./types";

interface CreateUserDialogProps {
  children: React.ReactNode;
  onUserCreated: () => void;
}

export function CreateUserDialog({
  children,
  onUserCreated,
}: CreateUserDialogProps) {
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    password: "",
    role: "user" as "user" | "admin",
  });
  const [validationErrors, setValidationErrors] = useState({
    email: "",
    phone: "",
  });
  const [isValidating, setIsValidating] = useState({
    email: false,
    phone: false,
  });

  // Validation functions
  const validateEmail = useCallback(async (email: string) => {
    if (!email || !email.includes("@")) {
      setValidationErrors((prev) => ({ ...prev, email: "" }));
      return;
    }

    setIsValidating((prev) => ({ ...prev, email: true }));

    try {
      // Check if email already exists by querying the user list
      const result = await authClient.admin.listUsers({
        query: {
          searchField: "email",
          searchOperator: "contains",
          searchValue: email,
          limit: 10,
        },
      });

      if (result.data?.users) {
        // Check for exact email match since we used "contains"
        const exactMatch = result.data.users.find(
          (user) => user.email === email
        );
        if (exactMatch) {
          setValidationErrors((prev) => ({
            ...prev,
            email: "Email already exists",
          }));
        } else {
          setValidationErrors((prev) => ({ ...prev, email: "" }));
        }
      } else {
        setValidationErrors((prev) => ({ ...prev, email: "" }));
      }
    } catch (error) {
      console.error("Error validating email:", error);
      setValidationErrors((prev) => ({ ...prev, email: "" }));
    } finally {
      setIsValidating((prev) => ({ ...prev, email: false }));
    }
  }, []);

  const validatePhone = useCallback(async (phone: string) => {
    if (!phone) {
      setValidationErrors((prev) => ({ ...prev, phone: "" }));
      return;
    }

    setIsValidating((prev) => ({ ...prev, phone: true }));

    try {
      // Check if phone already exists by checking all users
      const result = await authClient.admin.listUsers({
        query: {
          limit: 1000, // Get all users to check phone numbers
        },
      });

      if (result.data?.users) {
        const phoneExists = result.data.users.some(
          (user: UserWithRole) => user.phone === phone
        );

        if (phoneExists) {
          setValidationErrors((prev) => ({
            ...prev,
            phone: "Phone number already exists",
          }));
        } else {
          setValidationErrors((prev) => ({ ...prev, phone: "" }));
        }
      }
    } catch (error) {
      console.error("Error validating phone:", error);
      setValidationErrors((prev) => ({ ...prev, phone: "" }));
    } finally {
      setIsValidating((prev) => ({ ...prev, phone: false }));
    }
  }, []);

  // Debounced validation
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (formData.email) {
        validateEmail(formData.email);
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [formData.email, validateEmail]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (formData.phone) {
        validatePhone(formData.phone);
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [formData.phone, validatePhone]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Check for validation errors
    if (validationErrors.email || validationErrors.phone) {
      toast.error("Please fix validation errors before submitting");
      return;
    }

    // Check if validation is still in progress
    if (isValidating.email || isValidating.phone) {
      toast.error("Please wait for validation to complete");
      return;
    }

    setIsLoading(true);

    try {
      await authClient.admin.createUser({
        name: formData.name,
        email: formData.email,
        password: formData.password,
        role: formData.role as any, // Using Better Auth standard roles: "user" and "admin"
        data: {
          phone: formData.phone || null,
        },
      });

      // Send verification email to the newly created user
      try {
        await authClient.sendVerificationEmail({
          email: formData.email,
          callbackURL: `${import.meta.env.VITE_WEB_URL}/auth`, // The redirect URL after verification
        });
        toast.success("User created successfully and verification email sent");
      } catch (emailError: any) {
        console.error("Failed to send verification email:", emailError);
        toast.success(
          "User created successfully, but failed to send verification email"
        );
      }

      setOpen(false);
      setFormData({
        name: "",
        email: "",
        phone: "",
        password: "",
        role: "user" as "user" | "admin",
      });
      setValidationErrors({
        email: "",
        phone: "",
      });
      setIsValidating({
        email: false,
        phone: false,
      });
      onUserCreated();
    } catch (error: any) {
      console.error("Failed to create user:", error);
      toast.error(
        "Failed to create user: " + (error.message || "Unknown error")
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create New User</DialogTitle>
          <DialogDescription>
            Add a new user to the system. They will be able to sign in with the
            provided credentials.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              placeholder="Enter full name"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              required
              disabled={isLoading}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <div className="relative">
              <Input
                id="email"
                type="email"
                placeholder="Enter email address"
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                required
                disabled={isLoading}
                className={validationErrors.email ? "border-red-500" : ""}
              />
              {isValidating.email && (
                <div className="absolute right-3 top-3">
                  <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                </div>
              )}
            </div>
            {validationErrors.email && (
              <p className="text-sm text-red-500">{validationErrors.email}</p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="phone">Phone Number (Optional)</Label>
            <div className="relative">
              <Input
                id="phone"
                type="tel"
                placeholder="Enter phone number"
                value={formData.phone}
                onChange={(e) => handleInputChange("phone", e.target.value)}
                disabled={isLoading}
                className={validationErrors.phone ? "border-red-500" : ""}
              />
              {isValidating.phone && (
                <div className="absolute right-3 top-3">
                  <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                </div>
              )}
            </div>
            {validationErrors.phone && (
              <p className="text-sm text-red-500">{validationErrors.phone}</p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              type="password"
              placeholder="Enter password"
              value={formData.password}
              onChange={(e) => handleInputChange("password", e.target.value)}
              required
              disabled={isLoading}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="role">Role</Label>
            <Select
              value={formData.role}
              onValueChange={(value) => handleInputChange("role", value)}
              disabled={isLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="user">Artist</SelectItem>
                <SelectItem value="admin">Admin</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={
                isLoading ||
                Boolean(validationErrors.email) ||
                Boolean(validationErrors.phone) ||
                isValidating.email ||
                isValidating.phone
              }
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : isValidating.email || isValidating.phone ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Validating...
                </>
              ) : (
                "Create User"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
