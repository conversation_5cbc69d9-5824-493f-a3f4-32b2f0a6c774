import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { useTRPCClient } from "@/utils/trpc";
import { useMutation } from "@tanstack/react-query";
import { Send } from "lucide-react";
import { toast } from "sonner";
import EmailChangeDialog from "@/components/email-change-dialog";

interface SecurityProfileProps {
  session: {
    user: {
      name: string;
      email: string;
      phone?: string | null;
    };
  } | null;
}

export default function SecurityProfile({ session }: SecurityProfileProps) {
  const trpcClient = useTRPCClient();
  const sendPasswordResetEmailMutation = useMutation({
    mutationFn: (email: string) =>
      trpcClient.email.sendPasswordResetEmail.mutate({ email }),
    onSuccess: () => {
      toast.success("Password reset email sent! Please check your inbox.");
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to send password reset email");
    },
  });

  return (
    <>
      <div className="flex flex-col mb-4 sm:mb-5">
        <div className="text-md sm:text-lg font-bold">Account Security</div>
        <div className="text-xs sm:text-sm text-muted-foreground">
          Update your account security here
        </div>
      </div>

      <Separator className="my-4" />

      <div className="grid grid-cols-1 sm:grid-cols-1 lg:grid-cols-5 gap-4 lg:gap-6 xl:gap-8 items-start">
        <div className="lg:col-span-2 space-y-1">
          <Label className="text-sm font-medium">Change Email Address</Label>
          <p className="text-xs sm:text-sm text-muted-foreground leading-relaxed">
            Send a email change link to your email address
          </p>
        </div>
        <div className="lg:col-span-3 space-y-2">
          <EmailChangeDialog currentEmail={session?.user?.email || ""} />
        </div>

        <div className="lg:col-span-2 space-y-1">
          <Label className="text-sm font-medium">Request Password Reset</Label>
          <p className="text-xs sm:text-sm text-muted-foreground leading-relaxed">
            Send a secure password reset link to your email
          </p>
        </div>
        <div className="lg:col-span-3 space-y-2">
          <Button
            type="button"
            variant="default"
            className="w-full sm:min-w-[180px] h-10"
            onClick={() => {
              if (session?.user?.email) {
                sendPasswordResetEmailMutation.mutate(session.user.email);
              } else {
                toast.error("No email address found");
              }
            }}
            disabled={sendPasswordResetEmailMutation.isPending}
          >
            <Send className="h-4 w-4" />
            {sendPasswordResetEmailMutation.isPending
              ? "Sending..."
              : "Send Password Reset Email"}
          </Button>
        </div>
      </div>
    </>
  );
}
