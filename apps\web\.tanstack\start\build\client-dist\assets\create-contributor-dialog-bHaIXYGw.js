import{r as d,a as v,t as o,j as e,L as F}from"./main-B9Fv5CdX.js";import{u as w}from"./useMutation-DGkS69KN.js";import{a as P}from"./auth-client-C8WifPV2.js";import{j as S,k,l as B,m as E,n as L,o as T,p as A}from"./dialog-iGlJJq5Q.js";import{B as h}from"./button-Ispz1G12.js";import{L as p,I as x}from"./label-CNQvdrLZ.js";function H({children:C,onContributorCreated:b}){var l,c,m;const[j,n]=d.useState(!1),g=v(),{data:t}=P.useSession(),f=((l=t==null?void 0:t.user)==null?void 0:l.role)==="admin",[s,i]=d.useState({name:""}),r=w({mutationFn:async a=>g.contributor.create.mutate({name:a.name}),onSuccess:()=>{o.success("Contributor created successfully"),n(!1),i({name:""}),b()},onError:a=>{console.error("Failed to create contributor:",a),o.error("Failed to create contributor: "+(a.message||"Unknown error"))}}),y=async a=>{if(a.preventDefault(),!s.name.trim()){o.error("Contributor name is required");return}try{await r.mutateAsync({name:s.name.trim()})}catch{}},D=(a,u)=>{i(N=>({...N,[a]:u}))};return e.jsxs(S,{open:j,onOpenChange:n,children:[e.jsx(k,{asChild:!0,children:C}),e.jsxs(B,{className:"sm:max-w-[500px]",children:[e.jsxs(E,{children:[e.jsx(L,{children:"Create New Contributor"}),e.jsx(T,{children:"Add a new contributor to your roster."})]}),e.jsxs("form",{onSubmit:y,className:"space-y-4",children:[e.jsxs("div",{className:"space-y-4",children:[f&&e.jsxs("div",{className:"space-y-2",children:[e.jsx(p,{htmlFor:"createdBy",children:"Created By"}),e.jsx(x,{id:"createdBy",value:`${(c=t==null?void 0:t.user)==null?void 0:c.email} | ${(m=t==null?void 0:t.user)==null?void 0:m.name}`,disabled:!0})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(p,{htmlFor:"name",className:"block",children:["Contributor Name ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("p",{className:"text-muted-foreground text-xs",role:"region","aria-live":"polite",children:"This is the name that will be displayed for the contributor. Please make sure to use the correct name."}),e.jsx(x,{id:"name",placeholder:"Enter contributor name",value:s.name,onChange:a=>D("name",a.target.value),required:!0,disabled:r.isPending})]})]}),e.jsxs(A,{children:[e.jsx(h,{type:"button",variant:"outline",onClick:()=>n(!1),disabled:r.isPending,children:"Cancel"}),e.jsx(h,{type:"submit",disabled:r.isPending||!s.name.trim(),children:r.isPending?e.jsxs(e.Fragment,{children:[e.jsx(F,{className:"h-4 w-4 animate-spin"}),"Creating..."]}):"Create Contributor"})]})]})]})]})}export{H as C};
