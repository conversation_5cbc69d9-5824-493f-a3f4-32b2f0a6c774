import { createFileRoute } from "@tanstack/react-router";
import { authClient } from "@/lib/auth-client";
import { useEffect } from "react";
import Loader from "@/components/loader";
import { ContributorTable } from "@/routes/dashboard/contributor/components";

export const Route = createFileRoute("/dashboard/contributor/")({
  component: RouteComponent,
  head: () => ({
    meta: [
      {
        title: "Contributor Management - Soundmera App",
      },
    ],
  }),
});

function RouteComponent() {
  const navigate = Route.useNavigate();
  const { data: session, isPending } = authClient.useSession();

  useEffect(() => {
    if (!session && !isPending) {
      navigate({
        to: "/auth",
      });
      return;
    }
  }, [session, isPending, navigate]);

  if (isPending) {
    return <Loader />;
  }

  if (!session || !session.user) {
    return <Loader />;
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="pl-2 text-xl font-bold">Contributor Management</div>
      <div className="p-2">
        <ContributorTable />
      </div>
    </div>
  );
}
