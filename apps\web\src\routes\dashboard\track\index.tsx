import { createFileRoute } from "@tanstack/react-router";
import { useEffect } from "react";
import { authClient } from "@/lib/auth-client";
import Loader from "@/components/loader";

export const Route = createFileRoute("/dashboard/track/")({
  component: RouteComponent,
  head: () => ({
    meta: [
      {
        title: "Track Management - Soundmera App",
      },
    ],
  }),
});

function RouteComponent() {
  const navigate = Route.useNavigate();
  const { data: session, isPending } = authClient.useSession();

  useEffect(() => {
    if (!session && !isPending) {
      navigate({
        to: "/auth",
      });
      return;
    }
  }, [session, isPending, navigate]);

  if (isPending) {
    return <Loader />;
  }

  if (!session || !session.user) {
    return <Loader />;
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="pl-2 text-xl font-bold">Track Management</div>
      <div className="p-2"></div>
    </div>
  );
}
