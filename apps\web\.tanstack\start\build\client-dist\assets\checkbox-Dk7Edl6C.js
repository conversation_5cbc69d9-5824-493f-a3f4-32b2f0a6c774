import{c as F,r as i,j as o}from"./main-B9Fv5CdX.js";import{u as N,a as H}from"./button-Ispz1G12.js";import{u as O,k as G,j as K}from"./select-Cv6EF9My.js";import{c as U,P as V,u as X,b as I}from"./dialog-iGlJJq5Q.js";import{P}from"./label-CNQvdrLZ.js";/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]],ne=F("Trash",$);var y="Checkbox",[J,se]=U(y),[Q,R]=J(y);function W(t){const{__scopeCheckbox:n,checked:c,children:l,defaultChecked:s,disabled:e,form:h,name:f,onCheckedChange:d,required:k,value:m="on",internal_do_not_use_render:u}=t,[p,v]=X({prop:c,defaultProp:s??!1,onChange:d,caller:y}),[x,C]=i.useState(null),[g,r]=i.useState(null),a=i.useRef(!1),_=x?!!h||!!x.closest("form"):!0,E={checked:p,disabled:e,setChecked:v,control:x,setControl:C,name:f,form:h,value:m,hasConsumerStoppedPropagationRef:a,required:k,defaultChecked:b(s)?!1:s,isFormControl:_,bubbleInput:g,setBubbleInput:r};return o.jsx(Q,{scope:n,...E,children:Y(u)?u(E):l})}var w="CheckboxTrigger",S=i.forwardRef(({__scopeCheckbox:t,onKeyDown:n,onClick:c,...l},s)=>{const{control:e,value:h,disabled:f,checked:d,required:k,setControl:m,setChecked:u,hasConsumerStoppedPropagationRef:p,isFormControl:v,bubbleInput:x}=R(w,t),C=N(s,m),g=i.useRef(d);return i.useEffect(()=>{const r=e==null?void 0:e.form;if(r){const a=()=>u(g.current);return r.addEventListener("reset",a),()=>r.removeEventListener("reset",a)}},[e,u]),o.jsx(P.button,{type:"button",role:"checkbox","aria-checked":b(d)?"mixed":d,"aria-required":k,"data-state":L(d),"data-disabled":f?"":void 0,disabled:f,value:h,...l,ref:C,onKeyDown:I(n,r=>{r.key==="Enter"&&r.preventDefault()}),onClick:I(c,r=>{u(a=>b(a)?!0:!a),x&&v&&(p.current=r.isPropagationStopped(),p.current||r.stopPropagation())})})});S.displayName=w;var M=i.forwardRef((t,n)=>{const{__scopeCheckbox:c,name:l,checked:s,defaultChecked:e,required:h,disabled:f,value:d,onCheckedChange:k,form:m,...u}=t;return o.jsx(W,{__scopeCheckbox:c,checked:s,defaultChecked:e,disabled:f,required:h,onCheckedChange:k,name:l,form:m,value:d,internal_do_not_use_render:({isFormControl:p})=>o.jsxs(o.Fragment,{children:[o.jsx(S,{...u,ref:n,__scopeCheckbox:c}),p&&o.jsx(A,{__scopeCheckbox:c})]})})});M.displayName=y;var T="CheckboxIndicator",B=i.forwardRef((t,n)=>{const{__scopeCheckbox:c,forceMount:l,...s}=t,e=R(T,c);return o.jsx(V,{present:l||b(e.checked)||e.checked===!0,children:o.jsx(P.span,{"data-state":L(e.checked),"data-disabled":e.disabled?"":void 0,...s,ref:n,style:{pointerEvents:"none",...t.style}})})});B.displayName=T;var q="CheckboxBubbleInput",A=i.forwardRef(({__scopeCheckbox:t,...n},c)=>{const{control:l,hasConsumerStoppedPropagationRef:s,checked:e,defaultChecked:h,required:f,disabled:d,name:k,value:m,form:u,bubbleInput:p,setBubbleInput:v}=R(q,t),x=N(c,v),C=O(e),g=G(l);i.useEffect(()=>{const a=p;if(!a)return;const _=window.HTMLInputElement.prototype,j=Object.getOwnPropertyDescriptor(_,"checked").set,z=!s.current;if(C!==e&&j){const D=new Event("click",{bubbles:z});a.indeterminate=b(e),j.call(a,b(e)?!1:e),a.dispatchEvent(D)}},[p,C,e,s]);const r=i.useRef(b(e)?!1:e);return o.jsx(P.input,{type:"checkbox","aria-hidden":!0,defaultChecked:h??r.current,required:f,disabled:d,name:k,value:m,form:u,...n,tabIndex:-1,ref:x,style:{...n.style,...g,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});A.displayName=q;function Y(t){return typeof t=="function"}function b(t){return t==="indeterminate"}function L(t){return b(t)?"indeterminate":t?"checked":"unchecked"}function ae({className:t,...n}){return o.jsx(M,{"data-slot":"checkbox",className:H("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...n,children:o.jsx(B,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:o.jsx(K,{className:"size-3.5"})})})}export{ae as C,ne as T};
