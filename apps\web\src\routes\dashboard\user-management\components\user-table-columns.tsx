import type { ColumnDef, FilterFn } from "@tanstack/react-table";
import {
  CheckCircleIcon,
  XCircleIcon,
  ShieldIcon,
  UserIcon,
} from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { cn } from "@/lib/utils";
import { RowActions } from "./row-actions";
import type { UserTableItem, UserWithRole } from "./types";

// Custom filter functions
export const multiColumnFilterFn: FilterFn<UserTableItem> = (
  row,
  columnId,
  filterValue
) => {
  const searchableRowContent =
    `${row.original.name} ${row.original.email}`.toLowerCase();
  const searchTerm = (filterValue ?? "").toLowerCase();
  return searchableRowContent.includes(searchTerm);
};

export const statusFilterFn: FilterFn<UserTableItem> = (
  row,
  columnId,
  filterValue: string[]
) => {
  if (!filterValue?.length) return true;
  const status = row.getValue(columnId) as string;
  return filterValue.includes(status);
};

export const roleFilterFn: FilterFn<UserTableItem> = (
  row,
  columnId,
  filterValue: string[]
) => {
  if (!filterValue?.length) return true;
  const role = row.getValue(columnId) as string;
  return filterValue.includes(role);
};

export const createColumns = (
  onUserDeleted: () => void
): ColumnDef<UserTableItem>[] => [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    size: 28,
    enableSorting: false,
    enableHiding: false,
  },
  {
    header: "Name",
    accessorKey: "name",
    cell: ({ row }) => (
      <div className="font-medium">{row.getValue("name")}</div>
    ),
    size: 180,
    filterFn: multiColumnFilterFn,
    enableHiding: false,
  },
  {
    header: "Email",
    accessorKey: "email",
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        <span>{row.getValue("email")}</span>
        {row.original.emailVerified ? (
          <CheckCircleIcon className="h-4 w-4 text-green-500" />
        ) : (
          <XCircleIcon className="h-4 w-4 text-red-500" />
        )}
      </div>
    ),
    size: 250,
  },
  {
    header: "Phone",
    accessorKey: "phone",
    cell: ({ row }) => (
      <div className="text-sm">
        {row.getValue("phone") || (
          <span className="text-muted-foreground">Not provided</span>
        )}
      </div>
    ),
    size: 150,
  },
  {
    header: "Role",
    accessorKey: "displayRole",
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        {row.original.role === "admin" ? (
          <ShieldIcon className="h-4 w-4 text-blue-500" />
        ) : (
          <UserIcon className="h-4 w-4 text-gray-500" />
        )}
        <span className="capitalize">{row.getValue("displayRole")}</span>
      </div>
    ),
    size: 120,
    filterFn: roleFilterFn,
  },
  {
    header: "Status",
    accessorKey: "status",
    cell: ({ row }) => (
      <Badge
        className={cn(
          row.getValue("status") === "Banned" && "bg-red-500/10 text-red-700",
          row.getValue("status") === "Active" &&
            "bg-green-500/10 text-green-700",
          row.getValue("status") === "Pending" &&
            "bg-yellow-500/10 text-yellow-700"
        )}
      >
        {row.getValue("status")}
      </Badge>
    ),
    size: 100,
    filterFn: statusFilterFn,
  },
  {
    header: "Created",
    accessorKey: "createdAt",
    cell: ({ row }) => {
      const date = new Date(row.getValue("createdAt"));
      return (
        <div className="text-sm text-muted-foreground">
          {date.toLocaleDateString()}
        </div>
      );
    },
    size: 120,
  },
  {
    id: "actions",
    header: () => <span className="sr-only">Actions</span>,
    cell: ({ row }) => <RowActions row={row} onUserDeleted={onUserDeleted} />,
    size: 60,
    enableHiding: false,
  },
];

// Helper function to transform Better Auth user to table item
export const transformUserToTableItem = (user: UserWithRole): UserTableItem => {
  let status: "Active" | "Banned" | "Pending" = "Active";

  if (user.banned) {
    status = "Banned";
  } else if (!user.emailVerified) {
    status = "Pending";
  }

  // Map Better Auth roles to display roles
  const displayRole =
    user.role === "user"
      ? "Artist"
      : user.role === "admin"
      ? "Admin"
      : "Artist";

  return {
    ...user,
    status,
    displayRole,
  };
};
