import { createFileRoute } from "@tanstack/react-router";

import { Disc3, HouseIcon, Lock, SquareActivity } from "lucide-react";

import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import OverviewProfile from "./components/overview-profile";
import { authClient } from "@/lib/auth-client";
import { useEffect } from "react";
import Loader from "@/components/loader";
import SecurityProfile from "./components/security-profile";

export const Route = createFileRoute("/dashboard/profile/")({
  component: RouteComponent,
  head: () => ({
    meta: [
      {
        title: "Profile Settings - Soundmera App",
      }
    ]
  })
});

function RouteComponent() {
  const navigate = Route.useNavigate();
  const { data: session, isPending } = authClient.useSession();

  useEffect(() => {
    if (!session && !isPending) {
      navigate({
        to: "/auth",
      });
    }
  }, [session, isPending]);

  if (isPending) {
    return <Loader />;
  }

  return (
    <div>
      <div className="pl-2 text-xl font-bold">Profile Settings</div>
      <div className="p-2 pt-4">
        <Tabs defaultValue="tab-1">
          <ScrollArea>
            <TabsList className="mb-2 h-9 p-1 text-sm">
              <TabsTrigger
                value="tab-1"
                className="h-7 px-3 text-sm cursor-pointer"
              >
                <HouseIcon
                  className="-ms-0.5 me-1.5 opacity-60"
                  size={14}
                  aria-hidden="true"
                />
                Overview
              </TabsTrigger>
              <TabsTrigger
                value="tab-2"
                className="group h-7 px-3 text-sm cursor-pointer"
              >
                <Lock
                  className="-ms-0.5 me-1.5 opacity-60"
                  size={14}
                  aria-hidden="true"
                />
                Account Security
              </TabsTrigger>
              <TabsTrigger
                value="tab-3"
                className="group h-7 px-3 text-sm cursor-pointer"
              >
                <SquareActivity
                  className="-ms-0.5 me-1.5 opacity-60"
                  size={14}
                  aria-hidden="true"
                />
                Activites
              </TabsTrigger>
              <TabsTrigger
                value="tab-4"
                className="group h-7 px-3 text-sm cursor-pointer"
              >
                <Disc3
                  className="-ms-0.5 me-1.5 opacity-60"
                  size={14}
                  aria-hidden="true"
                />
                Projects
              </TabsTrigger>
            </TabsList>
            <ScrollBar orientation="horizontal" />
          </ScrollArea>
          <TabsContent value="tab-1">
            <div className="p-1 pt-1">
              <OverviewProfile session={session} />
            </div>
          </TabsContent>
          <TabsContent value="tab-2">
            <div className="p-1 pt-1">
              <SecurityProfile session={session} />
            </div>
          </TabsContent>
          <TabsContent value="tab-3">
            <p className="text-muted-foreground p-4 pt-1 text-center text-xs">
              Content for Tab 3
            </p>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
