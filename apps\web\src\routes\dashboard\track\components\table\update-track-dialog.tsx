import { useState, useEffect } from "react";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";
import { useMutation } from "@tanstack/react-query";

import { useTRPCClient } from "@/utils/trpc";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { GenreSelect } from "@/components/ui/genre-select";
import type { TrackTableItem } from "./track-table";

interface UpdateTrackDialogProps {
  track: TrackTableItem;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTrackUpdated: () => void;
}

const EXPLICIT_OPTIONS = [
  { value: "NOT_EXPLICIT", label: "Not Explicit" },
  { value: "EXPLICIT", label: "Explicit" },
  { value: "CLEAN", label: "Clean" },
] as const;

const RIGHTS_CLAIM_OPTIONS = [
  { value: "NO_CLAIM", label: "No Claim" },
  { value: "REPORT", label: "Report" },
  { value: "MONETIZE", label: "Monetize" },
  { value: "BLOCK", label: "Block" },
] as const;

const STATUS_OPTIONS = [
  { value: "DRAFT", label: "Draft" },
  { value: "READY", label: "Ready" },
] as const;

const LANGUAGE_OPTIONS = [
  { value: "en", label: "English" },
  { value: "es", label: "Spanish" },
  { value: "fr", label: "French" },
  { value: "de", label: "German" },
  { value: "it", label: "Italian" },
  { value: "pt", label: "Portuguese" },
  { value: "ja", label: "Japanese" },
  { value: "ko", label: "Korean" },
  { value: "zh", label: "Chinese" },
  { value: "ar", label: "Arabic" },
  { value: "hi", label: "Hindi" },
  { value: "ru", label: "Russian" },
] as const;

export function UpdateTrackDialog({
  track,
  open,
  onOpenChange,
  onTrackUpdated,
}: UpdateTrackDialogProps) {
  const trpcClient = useTRPCClient();
  
  const [formData, setFormData] = useState({
    title: "",
    isrc: "",
    trackVersion: "",
    recordingYear: new Date().getFullYear(),
    publishingYear: new Date().getFullYear(),
    publishingHolder: "",
    genre: "",
    subGenre: "",
    lyrics: "",
    previewStart: "",
    previewLength: "",
    metadataLanguage: "en",
    explicit: "NOT_EXPLICIT" as const,
    audioLanguage: "en",
    rightsClaim: "NO_CLAIM" as const,
    status: "DRAFT" as const,
  });

  // Initialize form data when track changes
  useEffect(() => {
    if (track) {
      setFormData({
        title: track.title || "",
        isrc: track.isrc || "",
        trackVersion: track.trackVersion || "",
        recordingYear: track.recordingYear,
        publishingYear: track.publishingYear,
        publishingHolder: track.publishingHolder || "",
        genre: track.genre || "",
        subGenre: track.subGenre || "",
        lyrics: track.lyrics || "",
        previewStart: track.previewStart || "",
        previewLength: track.previewLength || "",
        metadataLanguage: track.metadataLanguage || "en",
        explicit: track.explicit || "NOT_EXPLICIT",
        audioLanguage: track.audioLanguage || "en",
        rightsClaim: track.rightsClaim || "NO_CLAIM",
        status: track.status || "DRAFT",
      });
    }
  }, [track]);

  const updateTrackMutation = useMutation({
    mutationFn: async (data: typeof formData) => {
      // Basic validation
      if (!data.title.trim()) {
        throw new Error("Track title is required");
      }
      if (!data.publishingHolder.trim()) {
        throw new Error("Publishing holder is required");
      }
      if (!data.genre.trim()) {
        throw new Error("Genre is required");
      }

      return trpcClient.track.update.mutate({
        id: track.id,
        title: data.title.trim(),
        isrc: data.isrc.trim() || undefined,
        trackVersion: data.trackVersion.trim() || undefined,
        recordingYear: data.recordingYear,
        publishingYear: data.publishingYear,
        publishingHolder: data.publishingHolder.trim(),
        genre: data.genre.trim(),
        subGenre: data.subGenre.trim() || undefined,
        lyrics: data.lyrics.trim() || undefined,
        previewStart: data.previewStart.trim() || undefined,
        previewLength: data.previewLength.trim() || undefined,
        metadataLanguage: data.metadataLanguage,
        explicit: data.explicit,
        audioLanguage: data.audioLanguage,
        rightsClaim: data.rightsClaim,
        status: data.status,
      });
    },
    onSuccess: () => {
      toast.success("Track updated successfully");
      onOpenChange(false);
      onTrackUpdated();
    },
    onError: (error: any) => {
      console.error("Failed to update track:", error);
      toast.error(
        "Failed to update track: " + (error.message || "Unknown error")
      );
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      await updateTrackMutation.mutateAsync(formData);
    } catch (error) {
      // Error handling is done in the mutation
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Update Track</DialogTitle>
          <DialogDescription>
            Update the track information. Changes will be saved immediately.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="title" className="block">
                Track Title <span className="text-red-500">*</span>
              </Label>
              <Input
                id="title"
                placeholder="Enter track title"
                value={formData.title}
                onChange={(e) => handleInputChange("title", e.target.value)}
                required
                disabled={updateTrackMutation.isPending}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="isrc">ISRC</Label>
              <Input
                id="isrc"
                placeholder="CC-XXX-YY-NNNNN"
                value={formData.isrc}
                onChange={(e) => handleInputChange("isrc", e.target.value)}
                disabled={updateTrackMutation.isPending}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="genre">
                Genre <span className="text-red-500">*</span>
              </Label>
              <GenreSelect
                value={formData.genre}
                onValueChange={(value) => handleInputChange("genre", value)}
                disabled={updateTrackMutation.isPending}
                placeholder="Select a genre"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="subGenre">Sub Genre</Label>
              <Input
                id="subGenre"
                placeholder="Enter sub genre"
                value={formData.subGenre}
                onChange={(e) => handleInputChange("subGenre", e.target.value)}
                disabled={updateTrackMutation.isPending}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="trackVersion">Track Version</Label>
              <Input
                id="trackVersion"
                placeholder="e.g., Radio Edit, Extended Mix"
                value={formData.trackVersion}
                onChange={(e) => handleInputChange("trackVersion", e.target.value)}
                disabled={updateTrackMutation.isPending}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="recordingYear">Recording Year</Label>
              <Input
                id="recordingYear"
                type="number"
                min="1900"
                max={new Date().getFullYear() + 1}
                value={formData.recordingYear}
                onChange={(e) => handleInputChange("recordingYear", parseInt(e.target.value))}
                disabled={updateTrackMutation.isPending}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="publishingYear">Publishing Year</Label>
              <Input
                id="publishingYear"
                type="number"
                min="1900"
                max={new Date().getFullYear() + 1}
                value={formData.publishingYear}
                onChange={(e) => handleInputChange("publishingYear", parseInt(e.target.value))}
                disabled={updateTrackMutation.isPending}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="publishingHolder">
              Publishing Holder <span className="text-red-500">*</span>
            </Label>
            <Input
              id="publishingHolder"
              placeholder="Enter publishing holder"
              value={formData.publishingHolder}
              onChange={(e) => handleInputChange("publishingHolder", e.target.value)}
              required
              disabled={updateTrackMutation.isPending}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="explicit">Explicit Content</Label>
              <Select
                value={formData.explicit}
                onValueChange={(value) => handleInputChange("explicit", value)}
                disabled={updateTrackMutation.isPending}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {EXPLICIT_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleInputChange("status", value)}
                disabled={updateTrackMutation.isPending}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {STATUS_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={updateTrackMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={updateTrackMutation.isPending || !formData.title.trim()}
            >
              {updateTrackMutation.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                "Update Track"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
