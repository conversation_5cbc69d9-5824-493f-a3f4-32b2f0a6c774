enum Service {
    YOUTUBE
    SPOTIFY
    APPLE_MUSIC
    SOUNDCLOUD
    AUDIOMACK
}

model Artist {
    id        String  @id @default(uuid())
    name      String
    instagram String?
    biography String?
    country   String?
    labelId   String?
    label     Label?  @relation(fields: [labelId], references: [id], onDelete: SetNull)
    genre     String?

    userId    String
    user      User    @relation(fields: [userId], references: [id], onDelete: Cascade)

    identifiers ArtistIdentifier[]
    releases ReleaseArtist[]
    tracks TrackArtist[]

    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@map("artist")
    @@index([userId])
    @@unique([userId, name])
}

model ArtistIdentifier {
    artist     Artist  @relation(fields: [artistId], references: [id], onDelete: Cascade)
    artistId   String
    service    Service
    identifier String

    @@id([artistId, service])
    @@unique([identifier, service])
    @@map("artist_identifier")
}
